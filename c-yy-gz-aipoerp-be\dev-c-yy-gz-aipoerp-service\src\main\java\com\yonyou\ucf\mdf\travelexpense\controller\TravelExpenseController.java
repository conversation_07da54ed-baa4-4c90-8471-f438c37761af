package com.yonyou.ucf.mdf.travelexpense.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ucf.mdf.travelexpense.service.IConsumeKindQryService;
import com.yonyou.ucf.mdf.travelexpense.service.IExpapportionService;
import com.yonyou.ucf.mdf.travelexpense.service.IPeerPsnService;
import com.yonyou.ypd.bill.response.ResultMessage;
import com.yonyou.ypd.mdf.adapter.controller.BaseController;

import lombok.extern.slf4j.Slf4j;

/**
 * 差旅报销单接口
 *
 * <AUTHOR>
 *
 *         2025年3月24日
 */
@Slf4j
@RequestMapping("/travelexpense")
@RestController
public class TravelExpenseController extends BaseController {

	@Autowired
	private IExpapportionService expapportionService;
	@Autowired
	private IPeerPsnService peerPsnService;
	@Autowired
	private IConsumeKindQryService consumeKindQryService;

	/**
	 * 生成差旅报销单分摊费用
	 *
	 * @param bizObjects 账单明细
	 * @param response
	 */
	@PostMapping("/expapportions")
	public void generateExpapportions(@RequestBody List<BizObject> bizObjects, HttpServletResponse response) {
		try {
			List<BizObject> result = expapportionService.generateExpapportions(bizObjects);
			renderJson(response, ResultMessage.data(result));
		} catch (Exception e) {
			log.error("bizObjects:{}", JSONUtil.toJson(bizObjects), e);
			renderJson(response, ResultMessage.error(e.getMessage()));
		}

	}

	/**
	 * 获取同行人员id
	 *
	 * @param bizObject 报销明细
	 * @param response
	 */
	@PostMapping("/peerPsn")
	public void peerPsn(@RequestBody BizObject bizObject, HttpServletResponse response) {
		try {
			List<String> result = peerPsnService.getPeerPsnIds(bizObject);
//			result = Arrays.asList("2206160709752455168");
			renderJson(response, ResultMessage.data(result));
		} catch (Exception e) {
			log.error("bizObjects:{}", JSONUtil.toJson(bizObject), e);
			renderJson(response, ResultMessage.error(e.getMessage()));
		}

	}

	/**
	 * 获取账单类型id
	 *
	 * @param codes    账单类型编码
	 * @param response
	 */
	@PostMapping("/getConsumeKindIds")
	public void getConsumeKindIds(@RequestBody List<String> codes, HttpServletResponse response) {
		try {
			List<String> result = consumeKindQryService.queryIdByCodes(codes);
			renderJson(response, ResultMessage.data(result));
		} catch (Exception e) {
			log.error("codes:{}", JSONUtil.toJson(codes), e);
			renderJson(response, ResultMessage.error(e.getMessage()));
		}

	}

	/**
	 * 获取所有账单消费类（id/code）映射
	 *
	 * @param response
	 */
	@PostMapping("/getConsumeKindIdCode")
	public void getConsumeKindIdCode(HttpServletResponse response) {
		try {
			Map<String, String> result = consumeKindQryService.queryIdCodeMap();
			renderJson(response, ResultMessage.data(result));
		} catch (Exception e) {
			log.error("errorMsg:{}", e.getMessage(), e);
			renderJson(response, ResultMessage.error(e.getMessage()));
		}

	}

}
