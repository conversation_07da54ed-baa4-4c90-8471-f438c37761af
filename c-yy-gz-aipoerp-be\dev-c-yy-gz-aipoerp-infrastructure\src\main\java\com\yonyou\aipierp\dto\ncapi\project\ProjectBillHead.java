package com.yonyou.aipierp.dto.ncapi.project;

import com.alibaba.fastjson.JSONObject;


public class ProjectBillHead extends JSONObject {

    /**
     * "集团"(必输，code),
     */
    String PK_GROUP = "pk_group";
    /**
     * "管理组织"(必输，旗舰版id)
     */
    String PK_ORG = "pk_org";
    /**
     * "管理组织版本",
     */
    String PK_ORG_V = "pk_org_v";
    /**
     * "项目编码"(必输),
     */
    String PROJECT_CODE = "project_code";
    /**
     * "项目名称"(必输),
     */
    String PROJECT_NAME = "project_name";
    /**
     * "项目基本分类"(必输),
     */
    String PK_EPS = "pk_eps";
    /**
     * "父项目"(旗舰版父项目id),
     */
    String PK_PARENTPRO = "pk_parentpro";
    /**
     * "project_sh_name":"项目简称"
     */
    String PROJECT_SH_NAME = "project_sh_name";
    /**
     * "曾用名"
     */
    String PROJECT_OT_NAME = "project_ot_name";
    /**
     * "计划开始日期",
     */
    String PLAN_START_DATE = "plan_start_date";
    /**
     * 计划完成日期
     */
    String PLAN_FINISH_DATE = "plan_finish_date";
    /**
     * 实际开始日期
     */
    String ACTU_START_DATE = "actu_start_date";
    /**
     * 实际完成日期
     */
    String ACTU_FINISH_DATE = "actu_finish_date";
    /**
     * 计划工期
     */
    String PLANDURATION = "planduration";
    /**
     * 状态日期
     */
    String STATUS_DATE = "status_date";
    /**
     * 期初
     */
    String BEGIN_FLAG = "begin_flag";
    /**
     * 责任部门
     */
    String PK_DUTY_DEPT = "pk_duty_dept";
    /**
     * 责任组织多版本
     */
    String PK_DUTY_ORG_V = "pk_duty_org_v";
    /**
     * 责任组织
     */
    String PK_DUTY_ORG = "pk_duty_org";
    /**
     * 责任组织版本
     */
    String PK_DUTY_DEPT_V = "pk_duty_dept_v";
    /**
     * 排程完成日期
     */
    String ORDER_FINISH_DATE = "order_finish_date";
    /**
     * 需求开始日期
     */
    String REQ_START_DATE = "req_start_date";
    /**
     * 需求完成日期
     */
    String REQ_FINISH_DATE = "req_finish_date";
    /**
     * 开工日期
     */
    String START_WORK_DATE = "start_work_date";
    /**
     * 完工日期
     */
    String FINISH_WORK_DATE = "finish_work_date";
    /**
     * 备注
     */
    String MEMO = "memo";
    /**
     * 单据类型
     */
    String BILL_TYPE = "bill_type";
    /**
     * 父项目任务
     */
    String PK_PARENTTASK = "pk_parenttask";
    /**
     * 排程工期
     */
    String ORDERDURATION = "orderduration";
    /**
     * 估算金额
     */
    String ESTIMATE_MNY = "estimate_mny";
    /**
     * 估算金额(集团)
     */
    String ESTIMATE_GROUP = "estimate_group";
    /**
     * 概算金额
     */
    String GENERAL_MNY = "general_mny";
    /**
     * "概算金额(全局)",
     */
    String GENERAL_GLOBAL = "general_global";
    /**
     * "概算金额(集团)",
     */
    String GENERAL_GROUP = "general_group";
    /**
     * "WBS模板",
     */
    String PK_WBSTEMPLATE = "pk_wbstemplate";
    /**
     * 实际工期
     */
    String ACTUDURATION = "actuduration";
    /**
     * 年度计划明细
     */
    String PK_YEARPLAN_B = "pk_yearplan_b";
    /**
     * 来源交易类型主键
     */
    String SRC_PK_TRANSITYPE = "src_pk_transitype";
    /**
     * 来源单据主键
     */
    String SRC_PK_BILL = "src_pk_bill";
    /**
     * 项目类型,
     */
    String PK_PROJECTCLASS = "pk_projectclass";
    /**
     * 创建时项目状态
     */
    String CREATIONSTATE = "creationstate";
    /**
     * 需求工期
     */
    String REQDURATION = "reqduration";
    /**
     * 责任人
     */
    String PK_DUTIER = "pk_dutier";
    /**
     * 来源单据编码
     */
    String SRC_BILL_CODE = "src_bill_code";
    /**
     * 排程开始日期
     */
    String ORDER_START_DATE = "order_start_date";
    /**
     * 交易类型主键
     */
    String PK_TRANSITYPE = "pk_transitype";
    /**
     * 验收日期
     */
    String CHECK_DATE = "check_date";
    /**
     * N
     */
    String UPLOAD_FLAG = "upload_flag";
    /**
     * 业务流程
     */
    String PK_BUSITYPE = "pk_busitype";
    /**
     * 项目日历
     */
    String PK_WORKCALENDAR = "pk_workcalendar";
    /**
     * 编制人权限
     */
    String PLAN_AUTH = "plan_auth";
    /**
     * 来源单据类型
     */
    String SRC_BILL_TYPE = "src_bill_type";
    /**
     * 来源交易类型
     */
    String SRC_TRANSI_TYPE = "src_transi_type";
    /**
     * 计划编制人
     */
    String PK_PLANMAKER = "pk_planmaker";
    /**
     * 物资含税
     */
    String TAX_FLAG = "tax_flag";
    /**
     * 交易类型
     */
    String TRANSI_TYPE = "transi_type";
    /**
     * 参与组织
     */
    String BODYVOS = "bodyvos";

    public void setPk_group(String pk_group) {
        this.put(PK_GROUP, pk_group);
    }

    public void setPk_org(String pk_org) {
        this.put(PK_ORG, pk_org);
    }

    public void setPk_org_v(String pk_org_v) {
        this.put(PK_ORG_V, pk_org_v);
    }


    public void setProject_code(String project_code) {
        this.put(PROJECT_CODE, project_code);
    }


    public void setProject_name(String project_name) {
        this.put(PROJECT_NAME, project_name);
    }

    public void setPk_eps(String pk_eps) {
        this.put(PK_EPS, pk_eps);
    }


    public void setPk_parentpro(String pk_parentpro) {
        this.put(PK_PARENTPRO, pk_parentpro);
    }


    public void setProject_sh_name(String project_sh_name) {
        this.put(PROJECT_SH_NAME, project_sh_name);
    }


    public void setProject_ot_name(String project_ot_name) {
        this.put(PROJECT_OT_NAME, project_ot_name);
    }


    public void setPlan_start_date(String plan_start_date) {
        this.put(PLAN_START_DATE, plan_start_date);
    }


    public void setPlan_finish_date(String plan_finish_date) {
        this.put(PLAN_FINISH_DATE, plan_finish_date);
    }


    public void setActu_start_date(String actu_start_date) {
        this.put(ACTU_START_DATE, actu_start_date);
    }


    public void setActu_finish_date(String actu_finish_date) {
        this.put(ACTU_FINISH_DATE, actu_finish_date);
    }


    public void setPlanduration(String planduration) {
        this.put(PLANDURATION, planduration);
    }


    public void setStatus_date(String status_date) {
        this.put(STATUS_DATE, status_date);
    }


    public void setBegin_flag(String begin_flag) {
        this.put(BEGIN_FLAG, begin_flag);
    }


    public void setPk_duty_dept(String pk_duty_dept) {
        this.put(PK_DUTY_DEPT, pk_duty_dept);
    }


    public void setPk_duty_org_v(String pk_duty_org_v) {
        this.put(PK_DUTY_ORG_V, pk_duty_org_v);
    }


    public void setPk_duty_org(String pk_duty_org) {
        this.put(PK_DUTY_ORG, pk_duty_org);
    }


    public void setPk_duty_dept_v(String pk_duty_dept_v) {
        this.put(PK_DUTY_DEPT_V, pk_duty_dept_v);
    }


    public void setOrder_finish_date(String order_finish_date) {
        this.put(ORDER_FINISH_DATE, order_finish_date);
    }


    public void setReq_start_date(String req_start_date) {
        this.put(REQ_START_DATE, req_start_date);
    }


    public void setReq_finish_date(String req_finish_date) {
        this.put(REQ_FINISH_DATE, req_finish_date);
    }


    public void setStart_work_date(String start_work_date) {
        this.put(START_WORK_DATE, start_work_date);
    }


    public void setFinish_work_date(String finish_work_date) {
        this.put(FINISH_WORK_DATE, finish_work_date);
    }

    public void setMemo(String memo) {
        this.put(MEMO, memo);
    }


    public void setBill_type(String bill_type) {
        this.put(BILL_TYPE, bill_type);
    }


    public void setPk_parenttask(String pk_parenttask) {
        this.put(PK_PARENTTASK, pk_parenttask);
    }


    public void setOrderduration(String orderduration) {
        this.put(ORDERDURATION, orderduration);
    }


    public void setEstimate_mny(String estimate_mny) {
        this.put(ESTIMATE_MNY, estimate_mny);
    }


    public void setEstimate_group(String estimate_group) {
        this.put(ESTIMATE_GROUP, estimate_group);
    }


    public void setGeneral_mny(String general_mny) {
        this.put(GENERAL_MNY, general_mny);
    }

    public void setGeneral_global(String general_global) {
        this.put(GENERAL_GLOBAL, general_global);
    }


    public void setGeneral_group(String general_group) {
        this.put(GENERAL_GROUP, general_group);
    }


    public void setPk_wbstemplate(String pk_wbstemplate) {
        this.put(PK_WBSTEMPLATE, pk_wbstemplate);
    }


    public void setActuduration(String actuduration) {
        this.put(ACTUDURATION, actuduration);
    }


    public void setPk_yearplan_b(String pk_yearplan_b) {
        this.put(PK_YEARPLAN_B, pk_yearplan_b);
    }


    public void setSrc_pk_transitype(String src_pk_transitype) {
        this.put(SRC_PK_TRANSITYPE, src_pk_transitype);
    }

    public void setSrc_pk_bill(String src_pk_bill) {
        this.put(SRC_PK_BILL, src_pk_bill);
    }


    public void setPk_projectclass(String pk_projectclass) {
        this.put(PK_PROJECTCLASS, pk_projectclass);
    }


    public void setCreationstate(String creationstate) {
        this.put(CREATIONSTATE, creationstate);
    }


    public void setReqduration(String reqduration) {
        this.put(REQDURATION, reqduration);
    }


    public void setPk_dutier(String pk_dutier) {
        this.put(PK_DUTIER, pk_dutier);
    }

    public void setSrc_bill_code(String src_bill_code) {
        this.put(SRC_BILL_CODE, src_bill_code);
    }


    public void setOrder_start_date(String order_start_date) {
        this.put(ORDER_START_DATE, order_start_date);
    }


    public void setPk_transitype(String pk_transitype) {
        this.put(PK_TRANSITYPE, pk_transitype);
    }


    public void setCheck_date(String check_date) {
        this.put(CHECK_DATE, check_date);
    }


    public void setUpload_flag(String upload_flag) {
        this.put(UPLOAD_FLAG, upload_flag);
    }


    public void setPk_busitype(String pk_busitype) {
        this.put(PK_BUSITYPE, pk_busitype);
    }


    public void setPk_workcalendar(String pk_workcalendar) {
        this.put(PK_WORKCALENDAR, pk_workcalendar);
    }


    public void setPlan_auth(String plan_auth) {
        this.put(PLAN_AUTH, plan_auth);
    }


    public void setSrc_bill_type(String src_bill_type) {
        this.put(SRC_BILL_TYPE, src_bill_type);
    }


    public void setSrc_transi_type(String src_transi_type) {
        this.put(SRC_TRANSI_TYPE, src_transi_type);
    }


    public void setPk_planmaker(String pk_planmaker) {
        this.put(PK_PLANMAKER, pk_planmaker);
    }

    public void setTax_flag(String tax_flag) {
        this.put(TAX_FLAG, tax_flag);
    }

    public void setTransi_type(String transi_type) {
        this.put(TRANSI_TYPE, transi_type);
    }

    public JSONObject getBodyvos() {
        return this.getJSONObject(BODYVOS);
    }

    public void setBodyvos(JSONObject bodyvos) {
        this.put(BODYVOS, bodyvos);
    }
}
