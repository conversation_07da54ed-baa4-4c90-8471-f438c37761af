package com.yonyou.ucf.mdf.rbsm.service.itf;

import java.util.List;
import java.util.Map;

import com.yonyou.ucf.mdf.rbsm.constants.PayTypeEnum;
import com.yonyou.ucf.mdf.rbsm.model.PayOrgConfig;

/**
 * <AUTHOR>
 *
 *         2025年4月7日
 */
public interface IPayOrgConfigQryService {

	/**
	 * 获取所有缴交机构财务配置
	 * 
	 * @return
	 */
	Map<String, PayOrgConfig> queryAllForMap();

	/**
	 * 根据人力支付类型获取
	 * 
	 * @param payType
	 * @return
	 */
	List<Map<String, Object>> queryByPayType(PayTypeEnum payType);

}
