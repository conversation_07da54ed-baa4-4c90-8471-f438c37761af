package com.yonyou.aipoerp.controller;

import java.util.List;
import java.util.Map;

import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.IStaffService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import com.yonyou.ucf.mdf.service.impl.NCOpenApiConfig;
import com.yonyou.ucf.mdf.utils.openApi.SHA256Util;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

	@Autowired
	AIPORepository aipoRepository;
	@Autowired
	NCOpenApiService ncOpenApiService;
	@Autowired
	IStaffService staffService;
	@Autowired
	NCOpenApiConfig ncOpenApiConfig;
	@Autowired
	private IBillQueryRepository billQueryRepository;

	@RequestMapping("/ok")
	public JSONObject ok() {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("ok", "ok");
		return jsonObject;
	}

	@RequestMapping("/queryOrg")
	public Object queryOrg(@RequestParam String id) {
		return aipoRepository.queryOrgInfoById(id);
	}

	@RequestMapping("/queryStaff")
	public List<JSONObject> queryStaff(@RequestBody JSONObject body) {
		return staffService.queryStaffByPubtsRange(body.getDate("beginTime"), body.getDate("endTime"));
	}

	@RequestMapping("/getToken")
	public JSONObject getToken() {
		return ncOpenApiService.getToken();
	}

	@PostMapping("/doCommon")
	public JSONObject doCommonApi(@RequestBody JSONObject body) {
		String uri = body.getString("uri");
		String reqBody = body.getJSONObject("req").toJSONString();
		return ncOpenApiService.doCommonApi(reqBody, uri);
	}

	@PostMapping("/getHeaderInfos")
	public JSONObject getSignature(@RequestBody JSONObject body) {
		JSONObject realBody = body.getJSONObject("realBody");
		JSONObject json = new JSONObject();
		json.put("signature", SHA256Util.getSHA256(
				ncOpenApiConfig.getClient_id() + JSONObject.toJSONString(realBody) + ncOpenApiConfig.getPub_key(),
				ncOpenApiConfig.getPub_key()));
		return json;
	}

	@RequestMapping("/common/receive")
	public JSONObject commonApi(@RequestBody JSONObject body) {
		log.error("进入了通用接收api，这个地址什么都不会做，只会把请求体默默返回，并打印日志");
		log.error("body--->{}", JSONObject.toJSONString(body));

		return body;
	}

	@RequestMapping("/schema/query")
	public JSONObject schemaQuery(@RequestParam String billCode) {
		log.error("进入了schemaQueryapi，这个地址用于schema查询封装测试，并打印日志");
		log.error("billCode--->{}", billCode);

		try {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("id");
			// 关联交易类型
			schema.addSelect(new QueryField("bustype", "bustype", null, "bd.bill.TransType/id"));
			schema.addSelect("bustype.code"); // 查交易类型编码

			// 指定子表（费用分摊）
			QuerySchema sonSchema = QuerySchema.create().name("expapportions").addSelect("*");
			sonSchema.addSelect(new QueryField("pk_busimemo", "pk_busimemo", null, "bd.expenseitem.ExpenseItem/id"));
			sonSchema.addSelect("pk_busimemo.character");
			sonSchema.addSelect("pk_busimemo.name");
			sonSchema.addSelect("pk_busimemo.code");
			sonSchema.addSelect("pk_busimemo.character.BX59");
			sonSchema.addSelect(
					new QueryField("pk_busimemo.character.BX59", "fundUsage", null, "bd.customerdoc_0001.0001/id"));
			sonSchema.addSelect("pk_busimemo.character.BX59.name as fundName");

			schema.addCompositionSchema(sonSchema);

			schema.addCondition(QueryConditionGroup.and(QueryCondition.name("code").eq(billCode)));

			List<Map<String, Object>> maps = billQueryRepository
					.queryMapBySchema("znbzbx.commonexpensebill.CommonExpenseBillVO", schema, "znbzbx");

			log.error("maps: {}", JSONObject.toJSONString(maps));

			JSONObject json = new JSONObject();
			json.put("result", maps);

			return json;
		} catch (Exception e) {
			log.error("error: {}", e.getMessage(), e);
		}
		return null;
	}

	@RequestMapping("/schema/query2")
	public JSONObject schemaQuery2(@RequestParam String id) {
		log.error("进入了schemaQueryapi，这个地址用于schema查询封装测试，并打印日志");
		log.error("id--->{}", id);

		try {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("*");
			// 关联交易类型
			schema.addSelect(
					new QueryField("character.FUND_USAGE", "fundUsage", null, "unitfyEnum.KKDOC.FundUsageDoc/id"));
			schema.addSelect("character.FUND_USAGE");
			schema.addSelect("character.FUND_USAGE.code");
			schema.addSelect("character.FUND_USAGE.name");

			schema.addCondition(QueryConditionGroup.and(QueryCondition.name("id").eq(id)));

			List<Map<String, Object>> maps = billQueryRepository.queryMapBySchema("bd.expenseitem.ExpenseItem", schema,
					"c-yy-gz-aipoerp");

			log.error("maps: {}", JSONObject.toJSONString(maps));

			JSONObject json = new JSONObject();
			json.put("result", maps);

			return json;
		} catch (Exception e) {
			log.error("error: {}", e.getMessage(), e);
		}
		return null;
	}
}
