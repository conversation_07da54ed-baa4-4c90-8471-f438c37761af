{"version": 3, "file": "./javascripts/extend.850.606d82f8.min.js", "mappings": "spDAEqB,IAEAA,EAAI,SAAAC,GAAA,SAAAD,IAAA,O,4FAAAE,CAAA,KAAAF,GAAAG,EAAA,KAAAH,EAAAI,UAAA,Q,qRAAAC,CAAAL,EAAAC,G,EAAAD,G,EAAA,EAAAM,IAAA,SAAAC,MACvB,WACE,OACEC,IAAAA,cAAA,WAAK,gBAET,M,yFAAC,CALsB,CAASC,EAAAA,U,+ICKlC,KACEC,MAAAA,EACAC,UAAAA,EACAC,KAAAA,EACAC,KAAAA,EACAC,MAAAA,EACAC,QAAAA,EACAC,OAAAA,EACAC,QAAAA,E,2GChBF,IAAMC,EAAkBC,EAAAA,KACxBC,GAAGC,OAAOC,gBAAgBC,kBAA2BL,GAKnD,IAAMM,EAAmBL,EAAAA,KAAAA,EACzBC,GAAGC,OAAOI,mBAAmBF,kBAA2BC,GAI1D,IAAME,EAAiBP,EAAAA,KAAAA,EACvBC,GAAGC,OAAOM,iBAAiBJ,kBAA2BG,GAGtD,IAAME,EAAeT,EAAAA,KAAAA,EACrBC,GAAGC,OAAOQ,eAAeN,kBAA2BK,GAGpD,IAAME,EAAkBX,EAAAA,KAAAA,EACxBC,GAAGC,OAAOU,kBAAkBR,kBAA2BO,GAGvDV,GAAGC,OAAOW,YAAYT,kBAA2B,CAC/CU,WAAY,UAIdb,GAAGc,KAAKC,aAAaZ,kBAA2BJ,EAAQ,KAAe,G,gDC1BvE,KAAiBnB,KAAAA,EAAAA,E,gDCCjB,KACE,CAAEoC,OAAO,EAAMC,KAAM,QAASC,UAAWtC,EAAAA,M", "sources": ["webpack://c-yy-gz-aipoerp/./src/client/mobile/components/basic/demo/index.jsx", "webpack://c-yy-gz-aipoerp/./src/client/mobile/components/index.jsx", "webpack://c-yy-gz-aipoerp/./src/client/mobile/index.jsx", "webpack://c-yy-gz-aipoerp/./src/client/mobile/redux/reducers.jsx", "webpack://c-yy-gz-aipoerp/./src/client/mobile/routes/index.jsx"], "sourcesContent": ["import React, { Component } from 'react';\n\nimport './index.less'\n\nexport default class Demo extends Component {\n  render() {\n    return (\n      <div>这是一个移动组件Demo!</div>\n    )\n  }\n}", "import * as basic from './basic'              // 用于扩展基础组件（一般是表单组件）\nimport * as formatter from './formatter'      // 用于扩展表格格式化列组件\nimport * as home from './home'                // 用于扩展首页组件\nimport * as meta from './meta'                // 用于扩展业务组件（一般是容器组件）\nimport * as modal from './modal'              // 用于扩展弹窗组件\nimport * as popover from './popover'          // 用于扩展弹出菜单组件\nimport * as portal from './portal'            // 用于扩展门户组件\nimport * as toolbar from './toolbar'          // 用于扩展工具栏组件\n\nexport default {\n  basic,\n  formatter,\n  home,\n  meta,\n  modal,\n  popover,\n  portal,\n  toolbar\n}\n", "// 注册扩展脚本\nconst businessContext = require.context(\"business\");\ncb.extend.registerScripts(process.env.__DOMAINKEY__, businessContext);\n\n// 本地调试和普通打包时加载扩展组件，组件单独打包时扩展组件不在此处注册\nif (!process.env.__EXTENDCOMP__) {\n  // 注册扩展组件\n  const extendComponents = require(\"./components\").default;\n  cb.extend.registerComponents(process.env.__DOMAINKEY__, extendComponents);\n}\n\n// 注册reducer\nconst extendReducers = require(\"./redux/reducers\").default;\ncb.extend.registerReducers(process.env.__DOMAINKEY__, extendReducers);\n\n// 注册router\nconst extendRoutes = require(\"./routes\").default;\ncb.extend.registerRoutes(process.env.__DOMAINKEY__, extendRoutes);\n\n// 注册扩展action\nconst extendBizAction = require(\"../common/biz/actions\").default;\ncb.extend.registerBizAction(process.env.__DOMAINKEY__, extendBizAction);\n\n// 注册变量（框架使用的变量前后添加__）此方式和在Consul中配置等效且优先级大于Consul\ncb.extend.registerEnv(process.env.__DOMAINKEY__, { // registerEnv的第2个参数可以是个方法，接收一个当前环境env参数\n  currentEnv: \"daily\" // 领域自定义变量示例（小驼峰规范），变量使用：viewmodel.getEnv('currentEnv')\n});\n\n// 注册多语\ncb.lang.registerLang(process.env.__DOMAINKEY__, require(\"../../pack\"), \"\");\n\n", "// >>>>>>>>>>>>>>>>>>>> 示例开始 >>>>>>>>>>>>>>>>>>>> //\nimport Demo from \"../../common/redux/modules/demo\";\n\nexport default { Demo };\n// <<<<<<<<<<<<<<<<<<<< 示例结束 <<<<<<<<<<<<<<<<<<<< //", "// >>>>>>>>>>>>>>>>>>>> 示例开始 >>>>>>>>>>>>>>>>>>>> //\nimport { Demo } from \"../components/basic\";\n\n// 目前支持参数 exact | path | component 支持嵌套路由属性children\nexport default [\n  { exact: true, path: \"/demo\", component: Demo }\n];\n// <<<<<<<<<<<<<<<<<<<< 示例结束 <<<<<<<<<<<<<<<<<<<< //"], "names": ["Demo", "_Component", "_classCallCheck", "_callSuper", "arguments", "_inherits", "key", "value", "React", "Component", "basic", "formatter", "home", "meta", "modal", "popover", "portal", "toolbar", "businessContext", "require", "cb", "extend", "registerScripts", "process", "extendComponents", "registerComponents", "extendReducers", "registerReducers", "extendRoutes", "registerRoutes", "extendBizAction", "registerBizAction", "registerEnv", "currentEnv", "lang", "registerLang", "exact", "path", "component"], "sourceRoot": ""}