package com.yonyou.ucf.mdf.product.util;

/**
 * <AUTHOR>
 * @date 2025/3/14 13:47
 * @DESCRIPTION 类描述
 */
public class ObjectUtil {
    public static String toStr(Object obj) {
        if (obj != null) return obj.toString();
        return null;
    }
    public static Long toLon(Object obj) {
        if (obj != null) {
            return obj instanceof String ? Long.valueOf((String) obj) : (Long) obj;
        }
        return null;
    }
}
