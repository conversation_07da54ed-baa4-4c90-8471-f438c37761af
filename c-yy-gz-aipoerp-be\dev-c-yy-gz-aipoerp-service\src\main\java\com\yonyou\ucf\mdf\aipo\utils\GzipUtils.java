package com.yonyou.ucf.mdf.aipo.utils;

import com.google.common.io.BaseEncoding;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.Inflater;

public class GzipUtils {

    public static final int BUFFER = 1024;

    private static final byte[] EMPTY_BYTES = new byte[0];

    public static byte[] gzipByte(byte[] value) throws IOException {
        if(value == null) {
            return EMPTY_BYTES;
        }
        try(ByteArrayInputStream in = new ByteArrayInputStream(value); ByteArrayOutputStream out = new ByteArrayOutputStream(); GZIPOutputStream gzipOut = new GZIPOutputStream(out);) {
            int count;
            byte[] data = new byte[BUFFER];
            while((count = in.read(data, 0, BUFFER)) != -1) {
                gzipOut.write(data, 0, count);
            }
            gzipOut.finish();
            gzipOut.flush();
            return out.toByteArray();
        }
    }

    public static byte[] unGzipByte(byte[] bytes) throws IOException {
        if(bytes == null) {
            return EMPTY_BYTES;
        }

        try(ByteArrayOutputStream out = new ByteArrayOutputStream(); ByteArrayInputStream in = new ByteArrayInputStream(bytes); GZIPInputStream gzipIn = new GZIPInputStream(in);) {
            int count;
            byte[] data = new byte[BUFFER];
            while((count = gzipIn.read(data, 0, BUFFER)) != -1) {
                out.write(data, 0, count);
            }
            out.flush();
            return out.toByteArray();
        }
    }

    public static String unGzip(byte[] bytes) throws IOException {
        if(bytes == null) {
            return null;
        }

        try(ByteArrayOutputStream out = new ByteArrayOutputStream(); ByteArrayInputStream in = new ByteArrayInputStream(bytes); GZIPInputStream gzipIn = new GZIPInputStream(in);) {
            int count;
            byte[] data = new byte[BUFFER];
            while((count = gzipIn.read(data, 0, BUFFER)) != -1) {
                out.write(data, 0, count);
            }
            out.flush();
            return out.toString("utf-8");
        }
    }

    /**
     * 解压缩
     *
     * @param data 待压缩的数据
     * @return byte[] 解压缩后的数据
     */
    public static byte[] decompress(byte[] data) throws DataFormatException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream(data.length);
        Inflater decompresser = new Inflater();
        decompresser.reset();
        decompresser.setInput(data);

        byte[] bResult = null;
        try {
            byte[] buf = new byte[1024 * 1024];
            while(!decompresser.finished()) {
                int count = decompresser.inflate(buf);
                bos.write(buf, 0, count);
                if(count <= 0) {
                    break;
                }
            }

            bResult = bos.toByteArray();
        } catch(DataFormatException e) {
            //logger.error("decompress error", e);
            throw e;
        } finally {
            decompresser.end();
            try {
                bos.close();
            } catch(IOException e) {
                //logger.error("stream close exception!", e);
            }
        }

        return bResult;
    }

    public static String decompress(String data) {
        String result = null;
        try {
            byte[] bData = BaseEncoding.base64().decode(data);
            byte[] nResult = unGzipByte(bData);
            result = new String(nResult, StandardCharsets.UTF_8);
        } catch(IOException e) {
            //logger.error("decompress error", e);
        }
        return result;
    }

    public static byte[] compress(byte[] data) {

        Deflater def = new Deflater();
        def.reset();
        def.setInput(data);
        def.finish();

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        byte[] result = null;
        try {
            byte[] buf = new byte[1024 * 1024];
            while(!def.finished()) {
                int readCount = def.deflate(buf);
                if(readCount <= 0) {
                    break;
                }
                bos.write(buf, 0, readCount);
            }

            result = bos.toByteArray();
        } catch(Exception ex) {
            //logger.error("compress error", ex);
        } finally {
            try {
                bos.close();
            } catch(IOException e) {
                //logger.error("stream close exception!", e);
            }

            def.end();
        }

        return result;
    }

    public static String compress(String data) throws IOException {
        byte[] bData;
        bData = data.getBytes(StandardCharsets.UTF_8);

        byte[] bResult = gzipByte(bData);
        if(bResult == null) {
            return data;
        }

        return BaseEncoding.base64().encode(bResult);
    }
}
