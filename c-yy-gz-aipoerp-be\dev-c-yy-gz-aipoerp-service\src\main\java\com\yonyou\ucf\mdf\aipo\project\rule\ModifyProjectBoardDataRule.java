package com.yonyou.ucf.mdf.aipo.project.rule;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.bill.dto.BillDataDto;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 *         2025年5月23日
 */
@Slf4j
@Component("modifyProjectBoardDataRule")
public class ModifyProjectBoardDataRule implements IYpdCommonRul {

	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> map) {
		log.error("进入规则ModifyProjectBoardDataRule-----------");
		log.error("输出参数：{}", JSONObject.toJSONString(map));
		BillDataDto param = (BillDataDto) map.get("param");
		String action = param.getAction();
		if (StringUtils.isEmpty(action) || !"view".equals(action)) {
			return new RuleExecuteResult(map.get("boardData"));
		}
		Map<String, String> parameters = param.getParameters();
		if (parameters == null) {
			return new RuleExecuteResult(map.get("boardData"));
		}
		// 标段ID
		String bidSectionId = MapUtils.getString(parameters, "bidSectionId");
		// 看板的billnum
		String billnum = MapUtils.getString(parameters, "billnum");
		if (StringUtils.isEmpty(bidSectionId) || StringUtils.isEmpty(billnum)) {
			return new RuleExecuteResult(map.get("boardData"));
		}

		// 看板节点数据
		List<Map<String, Object>> boardDatas = (List<Map<String, Object>>) map.get("boardData");

		if ("lawbid_project_board".equals(billnum)) {
			/**
			 * TODO: 对看板进行修改名称，添加删除节点等操作
			 **/
			/**
			 *  1：修改节点信息，遍历看板数据，根据看板数据中的billno过滤出需要修改节点，对节点属性进行调整
			 **/
			for (Map<String, Object> boardData : boardDatas) {
				String procode = MapUtils.getString(boardData, "procode");
				if (StringUtils.isNotEmpty(procode) && StringUtils.equals(procode, "ND007")) { // 修改节点属性
					boardData.put("label", "采购结果");
				}
			}

			/**
			 *  2：删除节点，遍历看板数据，根据看板数据中的billno过滤出需要修改节点，删除节点  
			 **/
			List<Map<String, Object>> newBoardData = new ArrayList<>();
			List<String> nodes = Arrays.asList("lawbid_problem_list", "lawbid_question_list");
			for (Map<String, Object> boardData : boardDatas) {
				String billno = MapUtils.getString(boardData, "billno");
				if (StringUtils.isEmpty(billno) || !nodes.contains(billno)) {
					newBoardData.add(boardData);
				}
			}
			boardDatas = newBoardData;

			/**
			 *  3：添加节点，在指定位置添加客开单据节点
			 **/
//			Map<String, Object> nodeData = new HashMap<>();
//			nodeData.put("label", "节点名称");
//			nodeData.put("procode", "节点编码");
//			nodeData.put("billno", "单据编码");
//			nodeData.put("icon", "节点图标");
//			nodeData.put("nodeOrder", "节点顺序");
//			nodeData.put("externalData", "扩展参数");
//			nodeData.put("isExtend", "是否客开扩展单据，true/false");
//			boardDatas.add(nodeData);
		}

		// 将修改后的看板信息返回
		return new RuleExecuteResult(boardDatas);
	}

}
