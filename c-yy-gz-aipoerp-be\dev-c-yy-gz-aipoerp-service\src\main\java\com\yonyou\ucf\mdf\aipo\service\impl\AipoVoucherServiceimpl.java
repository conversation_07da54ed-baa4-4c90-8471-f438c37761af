package com.yonyou.ucf.mdf.aipo.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.dao.BaseDAO;
import com.yonyou.iuap.yms.id.generator.YmsOidGenerator;
import com.yonyou.iuap.yms.lock.YmsLock;
import com.yonyou.iuap.yms.lock.YmsLockFactory;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.ColumnProcessor;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.iuap.yms.processor.MapProcessor;
import com.yonyou.ucf.mdf.aipo.dto.MsgForwardDto;
import com.yonyou.ucf.mdf.aipo.model.EventContent;
import com.yonyou.ucf.mdf.aipo.model.EventType;
import com.yonyou.ucf.mdf.aipo.service.IAipoVoucherService;
import com.yonyou.ucf.mdf.aipo.service.IMsgForwardService;
import com.yonyou.ucf.mdf.aipo.utils.BipApiInvokerUtils;
import com.yonyou.ucf.mdf.aipo.utils.CommonMethodOperation;
import com.yonyou.ucf.mdf.aipo.utils.DateTimeFormatterUtil;
import com.yonyou.ucf.mdf.aipo.vo.AIPOVoucherSyncLog;
import iuap.yms.thread.api.YmsExecutors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class AipoVoucherServiceimpl extends CommonMethodOperation implements IAipoVoucherService {


    @Resource
    public BaseDAO baseDAO;

    @Autowired
    private IMsgForwardService msgForwardService;

    @Autowired
    YmsLockFactory ymsLockFactory;

    @Autowired
    private YmsOidGenerator ymsOidGenerator;

    public String SjTenantId ;

    @Override
    public void addData(String contentMsg, String eventId, Object o, EventType contentType) throws Exception {

        JSONObject contentJSONObject = JSONObject.parseObject(contentMsg);//凭证入参
        JSONArray voucehrVOArray = contentJSONObject.getJSONArray("voucherVO");
        LocalDateTime localDateTime = LocalDateTime.now();
        String hmsDate = DateTimeFormatterUtil.hmsDate(localDateTime);//获取当前时间
        String ywDate = hmsDate;
        if(o !=null && o instanceof EventContent){
            EventContent eventContent = (EventContent) o;
            Long timestamp = eventContent.getTimestamp();
            if(timestamp!=null){
                ywDate = DateTimeFormatterUtil.ymdSpeDate(timestamp);
            }
            setSjTenantId(eventContent);
            //String tenantId = eventContent.getTenantId();
        }
        if (voucehrVOArray != null && voucehrVOArray.size() > 0) {
            for (int i = 0; i < voucehrVOArray.size(); i++) {
                //{"accbook":"1891421434679394322","periodunion":"2024-12","billcode":1,"id":"2152837376534118409"}
                AIPOVoucherSyncLog logVO = new AIPOVoucherSyncLog();
                String errorMsg = "";
                String isSuccess = "Y";
                logVO.setIssuccess("Y");
                logVO.setEntertime(hmsDate);
                logVO.setYtenantId(getTenantId());

                JSONObject voucehrJSONObejct = voucehrVOArray.getJSONObject(i);//凭证数据
                String voucherId = voucehrJSONObejct.getString("id");//凭证ID

                try {

                    String accbookId = voucehrJSONObejct.getString("accbook");//账簿ID
                    String periodunion = voucehrJSONObejct.getString("periodunion");//期间
                    String billCode = voucehrJSONObejct.getString("billcode");//凭证号
                    //拼接日志数据
                    logVO.setDef1(ywDate);//业务日期
                    logVO.setEventid(eventId);
                    logVO.setBillid(voucherId);
                    logVO.setBillcode(billCode);
                    logVO.setVoucherperiod(periodunion);
                    logVO.setSynctype("凭证同步-保存");
                    logVO.setSyncdata(contentType.toString());


                    log.error("同步总账凭证开始：accbookId = {},billCode = {}", accbookId, billCode);
                    //if(1==1){
                    //    throw new RuntimeException("模拟返回错误：发放");
                    //}
                    List<Map> voucherMap = getVoucherMap(voucherId);
                    if (voucherMap != null && !voucherMap.isEmpty()) {
                        Map voucherDataMap = voucherMap.get(0);//拿到凭证的map数据
                        String voucherTypeCode = voucherDataMap.get("vouchertypecode").toString();
                        String accCode = voucherDataMap.get("acccode").toString();
                        String accName = voucherDataMap.get("acccname").toString();

                        //拼接日志数据
                        logVO.setAcccode(accCode);
                        logVO.setAccname(accName);
                        logVO.setVouchertypecode(voucherTypeCode);


                        //拼接查询参数
                        JSONObject voucherQueryMap = new JSONObject();
                        List<String> typeList = new ArrayList<>();
                        typeList.add(voucherTypeCode);
                        voucherQueryMap.put("voucherTypeCodeList", typeList);//凭证类型

                        voucherQueryMap.put("accbookCode", accCode);//账簿代码
                        voucherQueryMap.put("billcodeMin", billCode);//凭证号最小
                        voucherQueryMap.put("billcodeMax", billCode);//凭证号最大
                        voucherQueryMap.put("periodStart", periodunion);//期间开始
                        voucherQueryMap.put("periodEnd", periodunion);//期间结束

                        //查询凭证数据
                        String voucherQueryResultStr = BipApiInvokerUtils.postSystemBipApi("/yonbip/fi/ficloud/openapi/voucher/queryVouchers", voucherQueryMap.toJSONString());

                        JSONObject jsonQueryRes = JSONObject.parseObject(voucherQueryResultStr);//凭证查询结果
                        if (ObjectUtil.isNotEmpty(jsonQueryRes)
                                && ObjectUtil.isNotEmpty(jsonQueryRes.getJSONObject("data"))
                                && ObjectUtil.isNotEmpty(jsonQueryRes.getJSONObject("data").getJSONArray("recordList"))) {
                            JSONObject recordJSONObject = jsonQueryRes.getJSONObject("data").getJSONArray("recordList").getJSONObject(0);
                            //拿到真正的值
                            //处理制单人手机号问题
                            dealMakerMobile(recordJSONObject);

                            //处理业务伙伴辅助问题
                            JSONArray bodyArray = recordJSONObject.getJSONArray("body");
                            if(bodyArray!=null && bodyArray.size()>0){
                                for (int j = 0; j < bodyArray.size(); j++) {
                                    JSONObject bodyJSON = bodyArray.getJSONObject(j);
                                    //处理业务伙伴
                                    dealClientauxiliary(bodyJSON);

                                }
                            }

                            //组装数据调用凭证接口
                            MsgForwardDto msgForwardDto = new MsgForwardDto();
                            msgForwardDto.setReqType("post");
                            msgForwardDto.setUrl("pz_add");
                            msgForwardDto.setData(recordJSONObject);

                            logVO.setSendmsg(recordJSONObject.toJSONString());
                            //调用接口 拿到返回值

                            String req = msgForwardService.msgForward(msgForwardDto);

                            logVO.setRetmsg(req);

                            log.error("调用nc65接口结果为：{}", req);
                            //解析返回值
                            JSONObject jsonObject = JSONObject.parseObject(req);
                            //判断是否成功
                            if (!"200".equals(jsonObject.getString("code"))) {
                                throw new RuntimeException("同步失败:" + jsonObject.getString("message"));
                            }
                            //凭证调用至此完成

                        }

                    }
                } catch (Exception e) {
                    log.error("凭证数据解析异常：{}", e.getMessage());
                    errorMsg = e.getMessage();

                    if (errorMsg != null && !errorMsg.contains("加锁")) {
                        isSuccess = "N";
                    } else {
                        errorMsg = "";
                    }
                    logVO.setIssuccess("N");
                    logVO.setErrormsg(errorMsg);

                    String retmsg = logVO.getRetmsg();
                    String stackTrace = ExceptionUtils.getStackTrace(e);
                    if (stackTrace != null && stackTrace.length() > 2000) {
                        stackTrace = stackTrace.substring(0, 1998);
                    }
                    String retmsgNew = retmsg == null ? "" : retmsg;

                    logVO.setRetmsg(retmsgNew + stackTrace);

                    // throw new RuntimeException(e);
                } finally {
                    long nextId = ymsOidGenerator.nextId();
                    logVO.setId(nextId + "");
                    try {
                        String sendmsg = logVO.getSendmsg();
                        String retmsg = logVO.getSendmsg();

                        // 如果发送消息为没数据，或者返回消息包含加锁，则不记录日志
                        if ((!("没数据".equals(sendmsg) || (retmsg != null && retmsg.contains("加锁")))) && StringUtils.isNotEmpty(voucherId)) {

                            try {
                                String updateSql = "UPDATE figl.fi_voucher set def25 = '" + errorMsg + "',def26 = '" + isSuccess + "' " +
                                        " WHERE id = '" + voucherId + "' ";

                                baseDAO.update(updateSql);
                            }catch (Exception e){
                                e.printStackTrace();
                            }

                        }

                        //LocalDateTime localDateTimeEnd = LocalDateTime.now();
                        //String hmsDateEnd = DateTimeFormatterUtil.hmsDate(localDateTimeEnd);//获取当前时间
                     //   logVO.setPubts(new Date());
                       // baseDAO.insert(logVO);
                        //记录日志
                        SQLParameter parameter = new SQLParameter();
                        String insertLogSql = buildInsertSql(logVO, parameter);

                        baseDAO.update(insertLogSql, parameter);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e);
                    }


                }


            }
        }

    }

    private void dealMakerMobile(JSONObject recordJSONObject) {
        JSONObject header = recordJSONObject.getJSONObject("header");
        JSONObject maker = header.getJSONObject("maker");
        String makerId = maker.getString("id");
        Object makerMobile = baseDAO.queryForObject("  select mobile from iuap_apcom_auth.ba_user where id = '" + makerId + "' ", new ColumnProcessor());
        recordJSONObject.getJSONObject("header").getJSONObject("maker").put("mobile",makerMobile);
    }

    private void dealClientauxiliary(JSONObject bodyJSON) {
        JSONArray clientauxiliaryJsonArray = bodyJSON.getJSONArray("clientauxiliary");
        if(clientauxiliaryJsonArray!=null && clientauxiliaryJsonArray.size()>0){
            for (int k = 0; k < clientauxiliaryJsonArray.size(); k++) {
                JSONObject clientauxiliaryJsonObject = clientauxiliaryJsonArray.getJSONObject(k);
                String clientauxiliaryCode = clientauxiliaryJsonObject.getString("code");
                String clientauxiliaryValue = clientauxiliaryJsonObject.getString("value");
                if("0004".equals(clientauxiliaryCode)){//业务伙伴
                    //用业务伙伴的主键去查询客户的主键和编码信息
                    String queryMerSql = "select partnerid,CAST( merchant_id AS CHAR)  as merid,mt.cCode as code ,mt.cName as name from iuap_apdoc_coredoc.base_partnermerchantcomparison bn\n" +
                            "left join iuap_apdoc_coredoc.merchant  mt on mt.id = bn.merchant_id\n" +
                            "where partnerId = '"+clientauxiliaryValue+"' and bn.dr = 0\n" +
                            "union all\n" +
                            "select partnerid,CAST(vendor_id AS CHAR)  as merid,ar.code,ar.name from iuap_apdoc_coredoc.base_partnervendorcomparison vn\n" +
                            "left join iuap_apdoc_coredoc.aa_vendor ar on ar.id = vn.vendor_id\n" +
                            "where partnerId = '"+clientauxiliaryValue+"' and vn.dr = 0\n";
                    List<Map> merMapList = baseDAO.queryForObject(queryMerSql, new MapListProcessor());
                    if(merMapList!=null && merMapList.size()>0){
                        Map map = merMapList.get(0);
                        Object partnerid = map.get("partnerid");
                        Object mercode = map.get("code");
                        Object mername = map.get("name");
                        Object merid = map.get("merid");
                        clientauxiliaryJsonObject.getJSONObject("data").put("code",mercode);
                        clientauxiliaryJsonObject.getJSONObject("data").put("name",mername);
                        clientauxiliaryJsonObject.getJSONObject("data").put("id",merid);
                    }else{
                        log.error("未找到对应的业务伙伴信息");
                        throw new RuntimeException("未找到对应的业务伙伴数据:"+queryMerSql);
                    }

                }
            }
        }
    }


    @Override
    public void delData(String contentMsg, String eventId, EventType contentType, EventContent eventContent) throws Exception {

        // 删除数据
        //{"data":{"period":"2024-12","voucher_ids":["2153572443006959621"],"accbook":"1891421434679394322"}}
        JSONObject jsonObject = JSONObject.parseObject(contentMsg);
        JSONObject dataJsonObject = jsonObject.getJSONObject("data");
        String period = dataJsonObject.getString("period");
        String accbook = dataJsonObject.getString("accbook");
        JSONArray voucherArray = dataJsonObject.getJSONArray("voucher_ids");
        LocalDateTime localDateTime = LocalDateTime.now();
        String hmsDate = DateTimeFormatterUtil.hmsDate(localDateTime);//获取当前时间
        String ywDate = hmsDate;
        if(eventContent!=null){
            Long timestamp = eventContent.getTimestamp();
            if(timestamp!=null){
                ywDate = DateTimeFormatterUtil.ymdSpeDate(timestamp);
            }
            setSjTenantId(eventContent);
            //String tenantId = eventContent.getTenantId();
        }
        Map accbookMap = getAccbookMap(accbook);
        if (voucherArray != null && voucherArray.size() > 0) {
            //账簿
            String acccode = (String) accbookMap.get("code");
            String accname = (String) accbookMap.get("name");
            for (int i = 0; i < voucherArray.size(); i++) {

                AIPOVoucherSyncLog logVO = new AIPOVoucherSyncLog();
                String errorMsg = "";

                logVO.setIssuccess("Y");
                logVO.setEntertime(hmsDate);
                logVO.setYtenantId(getTenantId());
                logVO.setAccname(accname);
                logVO.setAcccode(acccode);
                logVO.setDef1(ywDate);

                String voucherId = voucherArray.getString(i);
                try {
                    //拼接日志数据
                    String eventStr = contentType.toString();
                    logVO.setEventid(eventId);
                    logVO.setBillid(voucherId);
                    //logVO.setBillcode(billCode);
                    logVO.setVoucherperiod(period);
                    logVO.setSynctype("凭证同步-删除");
                    logVO.setSyncdata(eventStr);


                    JSONObject delJsonObject = new JSONObject();
                    delJsonObject.put("voucherId", voucherId);
                    delJsonObject.put("acccode", acccode);
                    delJsonObject.put("accname", accname);
                    delJsonObject.put("period", period);
                    MsgForwardDto msgForwardDto = new MsgForwardDto();
                    msgForwardDto.setUrl("pz_onedel");//一步删除
                    msgForwardDto.setReqType("post");
                    msgForwardDto.setData(delJsonObject);

                    //入参
                    logVO.setSendmsg(delJsonObject.toJSONString());

                    String req = msgForwardService.msgForward(msgForwardDto);
                    log.error("调用ncc接口结果为：{}", req);
                    JSONObject retJSONObject = JSONObject.parseObject(req);
                    //返回值
                    logVO.setRetmsg(req);
                    if (!"200".equals(retJSONObject.getString("code"))) {
                        throw new RuntimeException("同步失败:" + retJSONObject.get("message"));
                    }

                } catch (Exception e) {
                    log.error("凭证数据解析异常：{}", e.getMessage());
                    errorMsg = e.getMessage();
                    if (errorMsg != null && !errorMsg.contains("加锁")) {

                    } else {
                        errorMsg = "";
                    }
                    logVO.setIssuccess("N");
                    logVO.setErrormsg(errorMsg);

                    String retmsg = logVO.getRetmsg();
                    String stackTrace = ExceptionUtils.getStackTrace(e);
                    if (stackTrace != null && stackTrace.length() > 2000) {
                        stackTrace = stackTrace.substring(0, 1998);
                    }
                    String retmsgNew = retmsg == null ? "" : retmsg;
                    logVO.setRetmsg(retmsgNew + stackTrace);
                    //throw new RuntimeException(e);
                } finally {

                    try {
                        //LocalDateTime localDateTimeEnd = LocalDateTime.now();
                        //String hmsDateEnd = DateTimeFormatterUtil.hmsDate(localDateTimeEnd);//获取当前时间
                        //logVO.setTs(hmsDateEnd);
                        //记录日志
                        SQLParameter parameter = new SQLParameter();
                        String insertLogSql = buildInsertSql(logVO, parameter);
                        long nextId = ymsOidGenerator.nextId();
                        logVO.setId(nextId + "");
                        baseDAO.update(insertLogSql, parameter);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e);
                    }


                }


            }
        }


    }

    private Map getAccbookMap(String accbook) {


        String tenantId = getTenantId();
        String sql = " select * from fiepub.epub_accountbook aa where aa.id = '" + accbook + "' and aa.ytenant_id = '" + tenantId + "' ";
        Map accbookMap = baseDAO.queryForObject(sql, new MapProcessor());

        return accbookMap;

    }


    @Override
    public void actionVoucherOpertionBatch(String content, EventType type, String eventId, Object o) throws Exception {
        //凭证批量操作
        JSONArray voucherArray = JSONArray.parseArray(content);
        LocalDateTime localDateTime = LocalDateTime.now();
        String hmsDate = DateTimeFormatterUtil.hmsDate(localDateTime);//获取当前时间
        String ywDate = hmsDate;
        if(o !=null && o instanceof EventContent){
            EventContent eventContent = (EventContent) o;
            Long timestamp = eventContent.getTimestamp();
            if(timestamp!=null){
                ywDate = DateTimeFormatterUtil.ymdSpeDate(timestamp);
            }
            setSjTenantId(eventContent);
            //String tenantId = eventContent.getTenantId();
        }
        String eventStr = type.toString();
        String syncName = "";
        switch (eventStr){
            case "GL_VOUCHER_EVENT_AUDIT_AFTER":
                syncName = "凭证同步-审核";
                break;
            case "GL_VOUCHER_EVENT_UNAUDIT_AFTER":
                syncName = "凭证同步-取消审核";
                break;
            case "GL_VOUCHER_EVENT_TALLY_AFTER":
                syncName = "凭证同步-记账";
                break;
            case "GL_VOUCHER_EVENT_UNTALLY_AFLTER":
                syncName = "凭证同步-取消记账";
                break;
        }


        if (voucherArray != null && voucherArray.size() > 0) {
            List<Map> voucherActionMapList = getVoucherActionMap(voucherArray);
            if (voucherActionMapList != null && voucherActionMapList.size() > 0) {
                // 遍历List<Map>

                for (int i = 0; i < voucherActionMapList.size(); i++) {
                    final int index = i; // 捕获索引的局部变量
                    //长连接线程
                    String finalSyncName = syncName;
                    String finalhmsDate= hmsDate;
                    String finalYwDate = ywDate;
                    YmsExecutors.getLongTaskContextExecutor().execute(() -> {
                                Map<String, Object> row = voucherActionMapList.get(index);
                               // EventSyncLogExtendVO logVO = new EventSyncLogExtendVO();
                                AIPOVoucherSyncLog logVO = new AIPOVoucherSyncLog();
                                logVO.setYtenantId(getTenantId());
                                String errorMsg = "";
                                String isSuccess = "Y";
                                logVO.setIssuccess("Y");
                                logVO.setEntertime(finalhmsDate);
                                logVO.setYtenantId(getTenantId());
                                logVO.setDef1(finalYwDate);
                                String voucherid = (String) row.get("voucherid");
                                 logVO.setSynctype(finalSyncName);
                                try {
                                    //加锁
                                    YmsLock lock = ymsLockFactory.getLock(voucherid + "_" + eventStr);
                                    if (lock.isLocked()) {
                                        Thread.sleep(3000);
                                    }

                                    Object billcode = row.get("billcode");
                                    String periodunion = (String) row.get("period");
                                    String vouchertypecode = (String) row.get("vouchertypecode");
                                    String acccode = (String) row.get("acccode");
                                    String acccname = (String) row.get("acccname");
                                    String auditorcode = (String) row.get("auditorcode");
                                    String auditorid = (String) row.get("auditorid");
                                    String auditstaffcode = (String) row.get("auditstaffcode");
                                    String auditstaffid = (String) row.get("auditstaffid");
                                    String tallymancode = (String) row.get("tallymancode");
                                    String tallymanid = (String) row.get("tallymanid");
                                    String tallymanstaffcode = (String) row.get("tallymanstaffcode");
                                    String tallymanstaffid = (String) row.get("tallymanstaffid");


                                    //拼接日志数据
                                    logVO.setEventid(eventId);
                                    logVO.setBillid(voucherid);
                                    logVO.setBillcode(billcode + "");
                                    logVO.setVoucherperiod(periodunion);

                                    logVO.setSyncdata(eventStr);

                                    //拼接日志数据
                                    logVO.setAcccode(acccode);
                                    logVO.setAccname(acccname);
                                    logVO.setVouchertypecode(vouchertypecode);


                                    if ("GL_VOUCHER_EVENT_AUDIT_AFTER".equals(eventStr) && StringUtils.isEmpty(auditorid)) {
                                        logVO.setSendmsg("BIP未审核，不推送审核状态！");
                                        return;
                                    }
                                    if ("GL_VOUCHER_EVENT_TALLY_AFTER".equals(eventStr) && StringUtils.isEmpty(tallymanid)) {
                                        logVO.setSendmsg("BIP未记账，不推送记账状态！");
                                        return;
                                        //  continue;
                                    }

                                    boolean isSendNCC = queryIsSendNCC(voucherid, eventId, eventStr);
                                    if (isSendNCC) {
                                        logVO.setSendmsg("事件重复发送不再进行处理！");
                                        return;
                                    }

                                    JSONObject recordJSONObject = new JSONObject(row);

                                    recordJSONObject.put("action", eventStr);

                                    //组装数据调用凭证接口
                                    MsgForwardDto msgForwardDto = new MsgForwardDto();
                                    msgForwardDto.setReqType("post");
                                    msgForwardDto.setUrl("pz_action");
                                    msgForwardDto.setData(recordJSONObject);

                                    logVO.setSendmsg(recordJSONObject.toJSONString());

                                    //调用接口 拿到返回值
                                    String req = msgForwardService.msgForward(msgForwardDto);
                                    logVO.setRetmsg(req);
                                    log.error("调用nc65接口结果为：{}", req);
                                    //解析返回值
                                    JSONObject jsonObject = JSONObject.parseObject(req);
                                    //判断是否成功
                                    if (!"200".equals(jsonObject.getString("code"))) {
                                        throw new RuntimeException("同步失败:" + jsonObject.getString("message"));
                                    }
                                } catch (Exception e) {
                                    log.error("凭证数据解析异常：{}", e.getMessage());
                                    errorMsg = e.getMessage();
                                    if (errorMsg != null && !errorMsg.contains("加锁")) {
                                        isSuccess = "N";
                                    } else {
                                        errorMsg = "";
                                    }
                                    logVO.setIssuccess("N");
                                    logVO.setErrormsg(errorMsg);

                                    String retmsg = logVO.getRetmsg();
                                    String stackTrace = ExceptionUtils.getStackTrace(e);
                                    if (stackTrace != null && stackTrace.length() > 2000) {
                                        stackTrace = stackTrace.substring(0, 1998);
                                    }
                                    String retmsgNew = retmsg == null ? "" : retmsg;
                                    logVO.setRetmsg(retmsgNew + stackTrace);
                                    //throw new RuntimeException(e);
                                } finally {

                                    try {
                                        String sendmsg = logVO.getSendmsg();
                                        String retmsg = logVO.getSendmsg();

                                        // 如果发送消息为没数据，或者返回消息包含加锁，则不记录日志
                                        if ((!("没数据".equals(sendmsg) || (retmsg != null && retmsg.contains("加锁")))) && StringUtils.isNotEmpty(voucherid)) {

                                            try {
                                                String updateSql = "UPDATE figl.fi_voucher set def25 = '" + errorMsg + "',def26 = '" + isSuccess + "' " +
                                                        " WHERE id = '" + voucherid + "' ";

                                                baseDAO.update(updateSql);
                                            }catch (Exception e){
                                                e.printStackTrace();
                                            }


                                        }
                                        long nextId = ymsOidGenerator.nextId();
                                        logVO.setId(nextId + "");
                                        //LocalDateTime localDateTimeEnd = LocalDateTime.now();
                                        //String hmsDateEnd = DateTimeFormatterUtil.hmsDate(localDateTimeEnd);//获取当前时间
                                        //logVO.setPubts(new Date(hmsDateEnd));
                                        //logVO.setDr(0);
                                        //记录日志
                                        SQLParameter parameter = new SQLParameter();
                                        String insertLogSql = buildInsertSql(logVO, parameter);
                                        baseDAO.update(insertLogSql, parameter);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        throw new RuntimeException(e);
                                    }


                                }


                            }
                    );


                }

            }

            for (int i = 0; i < voucherArray.size(); i++) {
                String voucherId = voucherArray.getString(i);//凭证ID

            }
        }


    }

    private boolean queryIsSendNCC(String voucherid, String eventId, String eventStr) {
        String isSendSql = " select * from  c_yy_gz_aipoerp_db.event_sync_data_log where eventid = '" + eventId + "' " +
                " and billid = '" + voucherid + "' and syncdata = '" + eventStr + "' and issuccess = 'Y'  ";//
        List<Map> voucherList = baseDAO.queryForObject(isSendSql, new MapListProcessor());
        if (voucherList != null && voucherList.size() > 0) {
            return true;
        }
        return false;
    }


    public List<Map> getVoucherActionMap(JSONArray voucherArray) {
        // 使用join方法将JSONArray转换为以逗号分隔的字符串
        String inClause = voucherArray.toJavaList(String.class).stream()
                .map(String::toString)
                .reduce((a, b) -> a + "," + b)
                .orElse("");

        String tenantId = getTenantId();
        String sql = "select  aa.id,aa.id AS voucherid,aa.billcode,aa.period,ee.code AS vouchertypecode,ea.code AS acccode,ea.name as acccname  ,\n" +
                "ff.code AS auditorcode,ff.user_id AS auditorid,ff.mobile as auditmobile,gg.code AS auditstaffcode,gg.id AS auditstaffid," +
                " aa.audittime ,aa.tallytime ,\n" +
                "hh.code AS tallymancode,hh.user_id AS tallymanid,hh.mobile as tallymanmobile,jj.code AS tallymanstaffcode,jj.id AS tallymanstaffid\n" +
                "from figl.fi_voucher aa inner JOIN FIEPUB.epub_accountbook ea ON ea.id = aa.accbook   \n" +
                "INNER join FIEPUB.epub_vouchertype ee ON ee.id = aa.vouchertype  \n" +
                "left join IUAP_APCOM_AUTH.ba_user ff ON aa.auditor = ff.user_id\n" +
                "LEFT JOIN IUAP_APDOC_BASEDOC.bd_staff gg ON gg.user_id  = ff.user_id\n" +
                "left join IUAP_APCOM_AUTH.ba_user hh ON aa.tallyman = hh.user_id\n" +
                "LEFT JOIN IUAP_APDOC_BASEDOC.bd_staff jj ON jj.user_id  = hh.user_id\n" +
                "WHERE aa.dr = 0 and aa.tenantid = '" + tenantId + "'  AND ea.dr = 0 AND ee.dr = 0  and  aa.id IN  (" + inClause+")";

        List<Map> voucherList = baseDAO.queryForObject(sql, new MapListProcessor());

        return voucherList;
    }

    public List<Map> getVoucherMap(String voucherId) {

        String tenantId = getTenantId();
        String sql = " select cc.code as vouchertypecode,aa.id as voucherid ,bb.code as acccode,bb.name as acccname ,aa.billcode    from figl.fi_voucher aa " +
                " left join fiepub.epub_accountbook bb on aa.accbook = bb.id \n" +
                " left join fiepub.epub_vouchertype cc on cc.id = aa.vouchertype where aa.id = '" + voucherId + "' and aa.tenantid = '" + tenantId + "' ";
        List<Map> voucherList = baseDAO.queryForObject(sql, new MapListProcessor());

        return voucherList;
    }

    public String getTenantId() {
        if(StringUtils.isEmpty(SjTenantId)){
            SjTenantId = InvocationInfoProxy.getTenantid()==null?"e4lxhnr7":InvocationInfoProxy.getTenantid();
        }

        return SjTenantId;
    }
    private void setSjTenantId(EventContent eventContent) {
        SjTenantId = eventContent.getTenantId()==null?InvocationInfoProxy.getTenantid():eventContent.getTenantId();

    }


}
