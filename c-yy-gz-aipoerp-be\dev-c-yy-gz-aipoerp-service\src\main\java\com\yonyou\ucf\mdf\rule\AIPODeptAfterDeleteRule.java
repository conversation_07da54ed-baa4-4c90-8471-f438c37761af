package com.yonyou.ucf.mdf.rule;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IDeptService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("aipoDeptAfterDeleteRule")
public class AIPODeptAfterDeleteRule implements IYpdCommonRul {

	@Autowired
	IDeptService deptService;

	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
		RuleExecuteResult result = new RuleExecuteResult();

		String jsonStr = JSON.toJSONString(params);
		log.error("部门删除后规则：{}", jsonStr);
		JSONObject deleteParams = JSON.parseObject(jsonStr);
		JSONObject param = deleteParams.getJSONObject("param");
		if (param == null) {
			log.error("无法获取到部门删除的详细信息已删除高级版部门，请重试 jsonStr-->{}", jsonStr);
			throw new BusinessException("无法获取到部门删除的详细信息已删除高级版部门，请重试");
		} else {
			JSONArray data = param.getJSONArray("data");
			for (Object object : data) {
				JSONObject dept = JSON.parseObject(JSON.toJSONString(object));
				String id = dept.getString("id");
				deptService.deleteDeptFromNC(id);
			}
		}

		return result;
	}
}
