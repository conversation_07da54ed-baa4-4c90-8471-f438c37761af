package com.yonyou.ucf.mdf.purchase.rule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/7 16:31
 * @DESCRIPTION 采购订单提交前
 */
@Component("purchaseOrderBeforeSubmitRule")
public class PurchaseOrderBeforeSubmitRule implements IYpdCommonRul {
    @Autowired
    private IBillQueryRepository billQueryRepository;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        try {
            List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
            BizObject bizObject = bills.get(0);
            Object orderId = bizObject.getId();
            List<Map<String, Object>> purchaseOrdersParallels = getPurchaseOrdersParallel(orderId);
            List<String> rowNos = purchaseOrdersParallels.stream().map(map -> {
                //合同编号
                Object parallelContractBillNo = map.get("bodyParallel_contractBillno");
                if (ObjectUtil.isEmpty(parallelContractBillNo) && map.get("lineno") != null) {
                    BigDecimal bigDecimal = new BigDecimal(map.get("lineno").toString());
                    return (bigDecimal.intValue() / 10) + "";
                }
                return null;
            }).filter(StringUtils::isNotBlank).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(rowNos)) {
                String rowNoStr = String.join("、", rowNos);
                throw new BusinessException("物料信息中第[" + rowNoStr + "]行没有关联合同");
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return null;
    }

    private List<Map<String, Object>> getPurchaseOrdersParallel(Object orderId) {
        QuerySchema querySchema = QuerySchema.create()
                .fullname("pu.purchaseorder.PurchaseOrders")
                .addSelect("id,lineno,bodyParallel.contractBillno, bodyParallel.contractName")
                .addCondition(QueryConditionGroup.and(QueryCondition.name("mainid").eq(orderId)));
        return billQueryRepository.queryMapBySchema("pu.purchaseorder.PurchaseOrders", querySchema, "upu");
    }
}
