package com.yonyou.ucf.mdf.equip.bill.plugin;

import cn.hutool.core.collection.CollUtil;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.equip.model.AssetNameLibraryVo;
import com.yonyou.ypd.bill.annotation.BillPlugin;
import com.yonyou.ypd.bill.context.YpdBillContext;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.plugin.AbstractBillPlugin;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/4 16:43
 * @DESCRIPTION 资产名称库插件
 */
@BillPlugin(busiObj = "AssetNameLibraryVo")
public class AssetNameLibraryPlugin extends AbstractBillPlugin {
    @Autowired
    private IBillQueryRepository repository;

    @Override
    public void beforeDelete(YpdBillContext billContext) throws Exception {
        AssetNameLibraryVo libraryVo = (AssetNameLibraryVo) billContext.getBillDO();
        String id = libraryVo.getId();
        List<Map<String, Object>> equipCardList = getEquipCardList(id);
        if (CollUtil.isNotEmpty(equipCardList)) {
            throw new BusinessException("编码为"+ libraryVo.getCode()+"已被下游资产卡片引用不允许删除");
        }
        super.beforeDelete(billContext);
    }

    private List<Map<String, Object>> getEquipCardList(String assetNameLibraryId) {
        QuerySchema querySchema = QuerySchema.create().addSelect("id,userDefines.asset_anme_library").addCondition(QueryConditionGroup.and(
                QueryCondition.name("userDefines.asset_anme_library").eq(assetNameLibraryId), QueryCondition.name("dr").eq(0)));
        return repository.queryMapBySchema("aim.equip.EquipHeadVO", querySchema, "ucf-amc-ambd");
    }

}
