package com.yonyou.ucf.mdf.product.rule;

import cn.hutool.core.collection.CollUtil;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.equip.service.AssetNameLibraryService;
import com.yonyou.ucf.mdf.product.service.ProductService;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/4 17:53
 * @DESCRIPTION 物料保存规则
 */
@Component("productBeforeSaveRule")
public class ProductBeforeSaveRule implements IYpdCommonRul {
    @Autowired
    private AssetNameLibraryService nameLibraryService;

    @Autowired
    private ProductService productService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        try {
            List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
            BizObject bizObject = bills.get(0);
            Object id = bizObject.getId();
            Object[] productIds = new Object[]{id};
            //根据物料ID查询是否存在在资产名称库中
            List<Map<String, Object>> assentNameList = nameLibraryService.getAssentNameListByProductId(productIds);
            if (CollUtil.isNotEmpty(assentNameList)){
                nameLibraryService.updateAssentName(assentNameList.get(0),bizObject);
            } else {
                List<Map<String, Object>> productListByIds = productService.getProductListById(id);
                if (CollUtil.isNotEmpty(productListByIds)) {
                    nameLibraryService.addAssentName(productListByIds);
                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return null;
    }
}
