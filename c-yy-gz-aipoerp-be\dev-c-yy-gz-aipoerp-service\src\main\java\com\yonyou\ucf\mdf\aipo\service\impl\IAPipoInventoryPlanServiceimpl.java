package com.yonyou.ucf.mdf.aipo.service.impl;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONArray;
import com.yonyou.iuap.yms.processor.ColumnListProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.dao.BaseDAO;
import com.yonyou.iuap.yms.processor.ColumnProcessor;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.iuap.yms.processor.MapProcessor;
import com.yonyou.uap.billcode.BillCodeComponentParam;
import com.yonyou.uap.billcode.BillCodeObj;
import com.yonyou.uap.billcode.service.IBillCodeComponentService;
import com.yonyou.ucf.mdf.aipo.service.IAPipoInventoryPlanService;
import com.yonyou.ucf.mdf.aipo.vo.CommonVOs;
import com.yonyou.ucf.mdf.aipo.vo.ConditionVO;
import com.yonyou.ucf.mdf.aipo.vo.DataContainerVO;
import com.yonyou.ucf.mdf.aipo.vo.MapConditionVO;
import com.yonyou.ucf.mdf.aipo.vo.RootVO;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class IAPipoInventoryPlanServiceimpl implements IAPipoInventoryPlanService {

	@Resource
	public BaseDAO baseDAO;
	@Resource
	private IBillCodeComponentService iBillCodeComponentService;

	@Value("${app.tenantId}")
	private String tenantId;

	@Autowired
	private BipOpenApiRequest apiRequest;

	@Override
	@Transactional
	public void IAPipoInventoryPlan() throws Exception {

		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// 查询数据库获取单据信息
		String Sql = "SELECT distinct pi2.*,pip.* FROM amc_aum.pam_invplan pi2  LEFT JOIN amc_aum.pam_invplan_parallel pip ON pi2.id = pip.id LEFT JOIN amc_aum.pam_invplan_b pib ON pi2.id = pib.main_id  WHERE pi2.dr = 0 and pip.planflag=1 and (pip.isCreatePlan is null or pip.isCreatePlan <>'1') and pi2.verifystate=2";
		List<Map<String, Object>> planMapList = baseDAO.queryForObject(Sql, new MapListProcessor());
		
		if (planMapList != null && planMapList.size() > 0) {
			// 处理数据并生成新的数据列表
			List<Map<String, Object>> newDataList = processAndGenerateNewData(planMapList, formatter);
			if (newDataList!=null && newDataList.size() > 0) {
				for (Map<String, Object> map : newDataList) {
					log.error("开始新增重复计划： map:{}", map.toString());
					String billCode = map.get("bill_code").toString();
					String billName = map.get("bill_name").toString();
					String rangeDetail = map.get("range_detail").toString();
					String sfgz = map.get("sfgz").toString();
					String startDateStr = map.get("start_date").toString();
					LocalDate startDate = LocalDate.parse(startDateStr, formatter);

					String endDateStr = map.get("end_date").toString();
					LocalDate endDate = LocalDate.parse(endDateStr, formatter);

					String pkOrg = map.get("pk_org") == null ? null : map.get("pk_org").toString();
					String pkOrgV = map.get("pk_org_v") == null ? null : map.get("pk_org_v").toString();
					String pkGroup = map.get("pk_group").toString();
					String memo = map.get("memo").toString();
					String dr = map.get("dr").toString();
					Integer isWfControlled = 1;
					String verifystate = "0";
					String returncount = map.get("returncount").toString();
					String tenantid = map.get("tenantid").toString();
					String creator = map.get("creator").toString();
					String billStatus = "0";
					String billType = map.get("bill_type").toString();
					String pkTransitype = map.get("pk_transitype").toString();
					String transiType = map.get("transi_type").toString();
					String billmaker = map.get("billmaker").toString();
					Date date = new Date();
					String billmaketime = sdf.format(date);
					String creationtime = billmaketime;
					String pkId = map.get("id").toString();
					String id = generateNextNumber();
					String pubts = billmaketime;
					String sysid = map.get("sysid").toString();
					String iscreate = "0";
					String orgType = map.get("org_type").toString();
					String ytenantId = map.get("ytenant_id").toString();



					// pam_invplan_parallel
					String planflag = map.get("planflag").toString();
					String startTime = map.get("startTime").toString();
					String sourceID = map.get("sourceID").toString();
					String IntervalPeriod = map.get("IntervalPeriod").toString();
					String generationTime = map.get("generationTime").toString();
					String pamInvplanSql = "INSERT INTO amc_aum.pam_invplan "
							+ "(bill_code, bill_name, range_detail, start_date, end_date, pk_org, pk_org_v, "
							+ "pk_group, memo, dr,isWfControlled, verifystate, returncount, tenantid, creator, creationtime,"
							+ " bill_status, bill_type,pk_transitype, transi_type, billmaker, billmaketime, "
							+ " id, pubts, sysid,iscreate, org_type, ytenant_id) " + "VALUES('"
							+ billCode + "','" + billName + "','" + rangeDetail + "','" + startDate + "','" + endDate + "',"
							+ pkOrg + "," + pkOrgV + ",'" + pkGroup + "','" + memo + "'," + dr + ", " + isWfControlled
							+ " ," + verifystate + "," + returncount + ",'" + tenantid + "','" + creator + "','"
							+ creationtime + "'," + billStatus + ",'" + billType + "','" + pkTransitype + "','" + transiType
							+ "','" + billmaker + "','" + billmaketime + "','" + id
							+ "','" + pubts + "','" + sysid + "'," + iscreate + ",'" + orgType + "','" + ytenantId + "')";

					baseDAO.update(pamInvplanSql);
					//查询子表  pam_invplan_b
					String sql_b = "SELECT pib.* FROM amc_aum.pam_invplan pi2  LEFT JOIN amc_aum.pam_invplan_parallel pip ON pi2.id = pip.id LEFT JOIN amc_aum.pam_invplan_b pib ON pi2.id = pib.main_id  WHERE pi2.dr = 0 and pip.planflag=1 and (pip.isCreatePlan is null or pip.isCreatePlan <>'1') and pi2.verifystate=2 and pi2.id='"+pkId+"'";
					List<Map<String, Object>> planBMapList = baseDAO.queryForObject(sql_b, new MapListProcessor());
					for (Map<String, Object> planBMap : planBMapList) {
						String pkOrgInventory = planBMap.get("pk_org_inventory").toString();
						String pkOrg_b = planBMap.get("pk_org_b")==null?"":planBMap.get("pk_org_b").toString();
						String pamInvplanBSQL = "INSERT INTO amc_aum.pam_invplan_b"
								+ "(main_id,pk_org_inventory,pk_org,pk_group,dr,tenantid,pubts,ytenant_id,id) " + "VALUES ('"
								+ id + "','" + pkOrgInventory + "','" + pkOrg_b + "','" + pkGroup + "',0,'" + tenantid + "','"
								+ pubts + "','" + ytenantId + "','" + generateNextNumber() + "')";
						log.error("====pamInvplanBSQL :" + pamInvplanBSQL);
						baseDAO.update(pamInvplanBSQL);
					}
					//主表平行表
					String pamInvplanParallelSql = "INSERT INTO "
							+ "amc_aum.pam_invplan_parallel (id, dr, planflag  ,startTime,ytenant_id ,sourceID ,IntervalPeriod, generationTime,sfgz) "
							+ "VALUES('" + id + "'," + dr + ",'" + planflag + "','" + startTime + "','" + ytenantId + "','"
							+ sourceID + "','"+IntervalPeriod+"','"+generationTime+"','"+sfgz+"')";

					log.error("====pamInvplanParallelSql :" + pamInvplanParallelSql);

					baseDAO.update(pamInvplanParallelSql);

					//盘点计划类别子表 pam_invplan_c
					String invplanCSql = "select category_id from amc_aum.pam_invplan_c where main_id = '" + pkId + "'";
					List<Map<String, Object>> planCMapList = baseDAO.queryForObject(invplanCSql, new MapListProcessor());
					for (Map<String, Object> planCMap : planCMapList) {
						String category_id = planCMap.get("category_id").toString();
						String invplanCSQL = "INSERT INTO amc_aum.pam_invplan_c  ( main_id , category_id , ytenant_id , dr , tenantid , id , pubts  )"
								+ "VALUES('" + id + "','" + category_id + "','" + ytenantId + "',0,'" + tenantid + "','"
								+ generateNextNumber() + "','" + pubts + "')";
						log.error("====invplanCSQL :" + invplanCSQL);
						baseDAO.update(invplanCSQL);
					}

					//更新为已生成重复盘点计划
					baseDAO.update("update amc_aum.pam_invplan_parallel set isCreatePlan='1' where id='"+sourceID+"'");
				}
			}
		}


	}

	@Override
	@Transactional
	public void inventoryPlanSubmit() throws Exception {
		//盘点计划是重复计划 开立态
		String Sql2 = "SELECT distinct pi2.id FROM amc_aum.pam_invplan pi2  LEFT JOIN amc_aum.pam_invplan_parallel pip ON pi2.id = pip.id LEFT JOIN amc_aum.pam_invplan_b pib ON pi2.id = pib.main_id  WHERE pi2.dr = 0 and pip.planflag=1  and pi2.verifystate=0";
		List<String> planpks = baseDAO.queryForObject(Sql2, new ColumnListProcessor());
		if(planpks!=null && planpks.size()>0){
			for(String id : planpks){
				submitPlan(id);
			}
		}
	}

	@Override
	@Transactional
	public void planToInventory() throws Exception {
		//盘点计划是重复计划 审批通过 未生成盘点单 自动生成盘点单
		String Sql2 = "SELECT distinct pi2.id FROM amc_aum.pam_invplan pi2  LEFT JOIN amc_aum.pam_invplan_parallel pip ON pi2.id = pip.id LEFT JOIN amc_aum.pam_invplan_b pib ON pi2.id = pib.main_id  WHERE pi2.dr = 0 and pip.planflag=1  and pi2.verifystate=2  and pi2.isCreate<>1";
		List<String> planpks = baseDAO.queryForObject(Sql2, new ColumnListProcessor());
		if(planpks!=null && planpks.size()>0){
			for(String id : planpks){
				assemblyJsonData(id);
			}
		}

	}

	/**
	 * 盘点计划提交
	 * @param id
	 */
	private void submitPlan(String id)  throws Exception {
		Map<String, String> headerMap = new HashMap<>();
		JSONObject transformedJsonObject = new JSONObject();
		headerMap.put("domain-key", "ucf-amc-aum");
		transformedJsonObject.put("billnum", "aum_inventory_plan_list");
		JSONArray jsonArray = new JSONArray();
		JSONObject bodyData = new JSONObject();
		bodyData.put("id", id);
		jsonArray.add(bodyData);
		transformedJsonObject.put("data", jsonArray.toString());
		String url = String.format("/%s/current_yonbip_default_sys/aipo/assest/auminventoryplan/submit", tenantId);
		url = url+"?action=commit&cmdname=cmdCommit&businessActName=%E7%9B%98%E7%82%B9%E8%AE%A1%E5%88%92-%E6%8F%90%E4%BA%A4&terminalType=1&serviceCode=AMC512004";
		log.error("开始提交盘点计划：url "+url+"----"+transformedJsonObject.toJSONString());
		String submitJson = apiRequest.doPost(url, transformedJsonObject,headerMap);
		log.error("提交盘点计划返回submitJson:{}", submitJson);
		JSONObject parse = (JSONObject) JSONObject.parse(submitJson);
		JSONObject dataObject = parse.getJSONObject("data");
		if (parse.get("code").toString().equals("200")) {
			int failCount = (int)dataObject.get("failCount");
			if(failCount==1){
				throw new RuntimeException("提交失败："+dataObject.getJSONArray("failInfos").toJSONString());
			}
		}else{
			throw new RuntimeException("提交失败："+parse.getString("errorDetail"));
		}
	}

	private void assemblyJsonData(String pkId) throws Exception {
		log.error("开始生成盘点单："+pkId);
		try {
			// 盘点计划主表
			String invplanSQL = "select * from amc_aum.pam_invplan where id = '" + pkId + "'";
			Map invplanSQLMap = baseDAO.queryForObject(invplanSQL, new MapProcessor());
			//盘点计划子表
			String invplanBSQL = "select * from amc_aum.pam_invplan_b where main_id = '" + pkId + "'";
			List<Map<String, Object>> invplanBMaps = baseDAO.queryForObject(invplanBSQL, new MapListProcessor());
			String orgname_pd = "";
			List<String> inventory = new ArrayList<>();
			for(Map<String, Object> invplanBMap:invplanBMaps){
				String pk_org_inventory = (String) invplanBMap.get("pk_org_inventory");
				inventory.add(pk_org_inventory);
				String orgName = baseDAO.queryForObject("select name from iuap_apdoc_basedoc.org_assets where id='" + pk_org_inventory + "'", new ColumnProcessor());
				orgname_pd = orgname_pd + orgName +",";
			}
			orgname_pd = orgname_pd.substring(0, orgname_pd.length()-1);
//			String pk_org = invplanSQLMap.get("pk_org").toString();
			String pk_org = "2132582826152296451";
			String orgname = baseDAO.queryForObject("select name from iuap_apdoc_basedoc.org_assets where id='" + pk_org + "'", new ColumnProcessor());
			StringBuilder inventory_range = new StringBuilder();//盘点范围
			//资产组织:业务单元001 盘点开始日期:2025-03-21 盘点组织范围:业务单元001 资产类别:测试 资产状态:在用 盘点组织类型:所有权 盘点计划:PDJH202503210002
			inventory_range.append("资产组织:" + orgname + " \n盘点开始日期:" + invplanSQLMap.get("start_date"));
			String rangeDetail = invplanSQLMap.get("range_detail").toString();
			log.error("盘点计划主表：range_detail{}",rangeDetail);
			String category_names = "";
			String category_status = "";
			List<String> category_list = new ArrayList<>();
			List<String> category_status_list = new ArrayList<>();
			if (rangeDetail != null) {
				JSONObject jsonObject = (JSONObject) JSONObject.parse(rangeDetail);
				if (jsonObject != null) {
					JSONArray categoryVO = jsonObject.getJSONArray("ampub_ambase_CategoryVO");
					if (categoryVO != null) {
						for (int i = 0; i < categoryVO.size(); i++) {
							String category = categoryVO.get(i).toString();
							String[] categorys = category.split(",");
							for(String category_id:categorys){
								category_list.add(category_id);
								String categorySql = "select category_name from amc_ambd.pam_category where id='" + category_id + "'";
								String category_name = baseDAO.queryForObject(categorySql, new ColumnProcessor());
								category_names = category_names + category_name + ",";
							}
						}
						category_names = category_names.substring(0, category_names.length() - 1);
						JSONArray statusVO = jsonObject.getJSONArray("ampub_equipbase_StatusVO");
						for (int i = 0; i < statusVO.size(); i++) {
							String status = statusVO.get(i).toString();
							String[] statuss = status.split(",");
							for(String status_id:statuss){
								category_status_list.add(status_id);
								String status_name = baseDAO.queryForObject("select status_name from amc_ambd.pam_status where id='" + status_id + "'", new ColumnProcessor());
								category_status = category_status + status_name + ",";
							}
						}
						category_status = category_status.substring(0, category_status.length() - 1);
					}
				}
			}
			String org_type = (String) invplanSQLMap.get("org_type");
			String org_type_name = "";
			if (org_type != null) {
				if ("0".equals(org_type)) {
					org_type_name = "资产管理组织";
				} else if ("1".equals(org_type)) {
					org_type_name = "使用单位";
				} else if ("2".equals(org_type)) {
					org_type_name = "所有权";
				}
			}
			inventory_range.append(" \n盘点组织范围:" + orgname_pd + " \n资产类别:" + category_names);
			inventory_range.append(" \n资产状态:" + category_status + " \n盘点组织类型:" + org_type_name);
			inventory_range.append(" \n盘点计划:" + invplanSQLMap.get("bill_code"));

			ObjectMapper objectMapper = new ObjectMapper();
			DataContainerVO dataContainer = objectMapper.readValue(rangeDetail, DataContainerVO.class);
//			// //使用状态结果集
//			List<String> statusVO = dataContainer.getStatusVO();
//			// 类目PK结果集
//			List<String> category = dataContainer.getCategoryVO();
			List<String> itemNamesList = new ArrayList<>();
			List<CommonVOs> commonVOsList = new ArrayList<>();

			Map<String, String> map = getinventoryRange(inventory_range.toString());

			RootVO rootVO = new RootVO();
			ConditionVO conditionVO = new ConditionVO();

			MapConditionVO mapConditionVO = new MapConditionVO();

			CommonVOs commonVOs1 = new CommonVOs();
			commonVOs1.setItemName("pk_org");
			commonVOs1.setValue1(pk_org);
			commonVOsList.add(commonVOs1);

			CommonVOs commonVOs2 = new CommonVOs();
			commonVOs2.setItemName("audittime");
			String audittime = invplanSQLMap.get("audittime").toString();
			commonVOs2.setValue1(audittime.substring(0,10)+" 23:59:59");
			commonVOsList.add(commonVOs2);

			CommonVOs commonVOs3 = new CommonVOs();
			commonVOs3.setItemName("pk_ownerorg");
			commonVOs3.setValue1(inventory);
			commonVOsList.add(commonVOs3);

			CommonVOs commonVOs4 = new CommonVOs();
			commonVOs4.setItemName("pk_category");
			commonVOs4.setValue1(Arrays.asList(category_list));
			commonVOsList.add(commonVOs4);

			CommonVOs commonVOs5 = new CommonVOs();
			commonVOs5.setItemName("pk_used_status");
			commonVOs5.setValue1(Arrays.asList(category_status_list));
			commonVOsList.add(commonVOs5);

			CommonVOs commonVOs6 = new CommonVOs();
			commonVOs6.setItemName("org_type");
			commonVOs6.setValue1(invplanSQLMap.get("org_type").toString());
			commonVOsList.add(commonVOs6);

			CommonVOs commonVOs7 = new CommonVOs();
			commonVOs7.setItemName("pk_plan");
			commonVOs7.setValue1(invplanSQLMap.get("id").toString());
			commonVOsList.add(commonVOs7);

			conditionVO.setCommonVOs(commonVOsList);
			conditionVO.setFiltersId("24");
			conditionVO.setSolutionId(24);

			mapConditionVO.setPk_org_value(pk_org);
			mapConditionVO.setPk_org_text(map.get("资产组织"));
			mapConditionVO.setPk_org_title("资产组织");
			mapConditionVO.setPk_org_op("eq");
			mapConditionVO.setAudittime_value(map.get("盘点开始日期"));
			mapConditionVO.setAudittime_title("盘点开始日期");
			mapConditionVO.setAudittime_op("elt");
			mapConditionVO.setPk_ownerorg_value(inventory);
			mapConditionVO.setPk_ownerorg_text(map.get("盘点组织范围"));
			mapConditionVO.setPk_ownerorg_title("盘点组织范围");
			mapConditionVO.setPk_ownerorg_op("in");
			mapConditionVO.setPk_category_value(category_list);
			mapConditionVO.setPk_category_text(map.get("资产类别"));
			mapConditionVO.setPk_category_title("资产类别");
			mapConditionVO.setPk_category_op("in");
			mapConditionVO.setPk_used_status_value(category_status_list);
			mapConditionVO.setPk_used_status_text(map.get("资产状态"));
			mapConditionVO.setPk_used_status_title("资产状态");
			mapConditionVO.setPk_used_status_op("in");
			mapConditionVO.setOrg_type_value(invplanSQLMap.get("org_type").toString());
			mapConditionVO.setOrg_type_title("盘点组织类型");
			mapConditionVO.setOrg_type_op("eq");
			mapConditionVO.setPk_plan_value(invplanSQLMap.get("id").toString());
			mapConditionVO.setPk_plan_text(invplanSQLMap.get("bill_code").toString());
			mapConditionVO.setPk_plan_title("盘点计划");
			mapConditionVO.setPk_plan_op("eq");
			itemNamesList.add("pk_org");
			itemNamesList.add("audittime");
			itemNamesList.add("pk_ownerorg");
			itemNamesList.add("pk_category");
			itemNamesList.add("pk_used_status");
			itemNamesList.add("org_type");
			itemNamesList.add("pk_plan");
			mapConditionVO.setItemNames(itemNamesList);
			mapConditionVO.setRange(inventory_range.toString());

			rootVO.setBillnum("aum_inventory_card");
			rootVO.setFrom("aum_inventory_add");
			rootVO.setCondition(conditionVO);
			rootVO.setMapCondition(mapConditionVO);

			String jsonString = objectMapper.writeValueAsString(rootVO);

			Map<String, String> headerMap = new HashMap<>();
			JSONObject transformedJsonObject = new JSONObject();
			headerMap.put("domain-key", "ucf-amc-aum");
			log.error("--------------获取plan参数 rootVO:"+JSONObject.toJSONString(rootVO));
			String queryJson = apiRequest.doPost(
					String.format("/%s/current_yonbip_default_sys/aipo/assest/auminventory/add", tenantId), rootVO,
					headerMap);
			log.error("--------------queryJson:{}", queryJson);
			// 盘点单新增查询API接口
			JSONObject parse = (JSONObject) JSONObject.parse(queryJson);
			String dataObject = parse.getJSONObject("data").toString();
			if (parse.get("code").toString().equals("200")) {
				transformedJsonObject.put("billnum", "aum_inventory_card");
				transformedJsonObject.put("data", dataObject);
				log.error("--------------盘点单新增参数："+transformedJsonObject.toJSONString());
				String saveJson = apiRequest.doPost(
						String.format("/%s/current_yonbip_default_sys/aipo/assest/auminventory/save", tenantId),
						transformedJsonObject, headerMap);
				log.error("--------------盘点单saveJson:{}", saveJson);
				// 取code有问题
				JSONObject saveParse = (JSONObject) JSONObject.parse(saveJson);

				if (!saveParse.get("code").toString().equals("200")) {
					throw new RuntimeException("新增盘点单失败："+saveJson);
				}
			}
		} catch (Exception e) {
			log.error("生成盘点单失败",e);
			throw new RuntimeException(e);
		}
	}

	public static void main(String[] args) {
		List<String> category = new ArrayList<>();
		category.add("11111");
		category.add("22222");
		CommonVOs commonVOs4 = new CommonVOs();
		commonVOs4.setItemName("pk_category");
		commonVOs4.setValue1(Arrays.asList(category));
		System.out.println(JSONObject.toJSONString(commonVOs4));
	}

	public List<Map<String, Object>> processAndGenerateNewData(List<Map<String, Object>> list,
															   DateTimeFormatter formatter) {
		List<Map<String, Object>> newDataList = new ArrayList<>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String nowDate = sdf.format(new Date());
		LocalDate today = LocalDate.now();

		for (Map<String, Object> map : list) {
			/**
			 * 盘点开始日期 = 原盘点开始日期+间隔周期（月）
			 * 盘点结束日期 = 原盘点结束日期+间隔周期（月）
			 * 开始日期 = 原开始日期+间隔周期（月）
			 * 到期月生成时间 = 原开始日期+间隔周期（月） + 一天
			 */
			try {
				String startTimeStr = map.get("startTime").toString();
				String intervalPeriod = map.get("IntervalPeriod").toString();//间隔周期（月）
				String generationTime = map.get("generationTime").toString();//到期月生成时间 xx日
				String start_timeStr = map.get("start_date").toString();
				String endTimeStr = map.get("end_date").toString();
				//计算制单时间 = 原开始日期+间隔周期（月） + 一天
				String nowStartTime = getNewDateAndAddOneDay(startTimeStr, intervalPeriod);
				//判断是否到期 与当期日期比较
				String toDayStr = today.format(formatter);
				if (toDayStr.equals(nowStartTime)) {
					//盘点开始日期 = 原盘点开始日期+间隔周期（月）
					String newPlanStartDate = getNewDate(start_timeStr, intervalPeriod);
					//盘点结束日期 = 原盘点结束日期+间隔周期（月）
					String newPlanEndDate = getNewDate(endTimeStr, intervalPeriod);
					//开始日期
					String newStartTime = getNewDate(startTimeStr, intervalPeriod);
					Map<String, Object> newMap = new HashMap<>(map);

					String contractCode = "PDJH"+nowDate+getHouseContractCode("PlanHeadVO", "PlanHeadVO");
					// 盘点开始日期
					newMap.put("start_date", newPlanStartDate);
					// 盘点结束日期
					newMap.put("end_date", newPlanEndDate);
					// 是否重复计划 1
					newMap.put("planflag", "1");
					// 来源ID为上一条数据的ID
					newMap.put("sourceID", map.get("id"));
					// 计划编码
					newMap.put("bill_code", contractCode);
					// 计划名称 日期+原计划名称
					newMap.put("bill_name", today.toString().substring(0, 7) + map.get("bill_name"));
					//开始日期
					newMap.put("startTime", newStartTime);
					// 间隔周期
					newMap.put("IntervalPeriod", intervalPeriod);
					// 到期月生成时间
					newMap.put("generationTime", generationTime);

					newDataList.add(newMap);
				}
			}catch (Exception e) {
				log.error("--------------map:{}", map.toString());
				log.error("处理待复制盘点计划失败："+e.getMessage(),e);
			}

		}
		return newDataList;
	}

	private String getNewDateAndAddOneDay(String TimeStr, String generationTime) {

		// 计算盘点计划开始日期与间隔时间后的日期
		LocalDate originalDate = LocalDate.parse(TimeStr);
		// 增加月份（自动处理月末问题）
		LocalDate newTime = originalDate.plusMonths(Long.parseLong(generationTime));
		//增加天
		LocalDate newTimeDay = newTime.plusDays(1);
		// 转换后的日期
		return newTimeDay.format(DateTimeFormatter.ISO_LOCAL_DATE);
	}

	private String getNewDate(String TimeStr, String generationTime) {

		// 计算盘点计划开始日期与间隔时间后的日期
		LocalDate originalDate = LocalDate.parse(TimeStr);
		// 增加月份（自动处理月末问题）
		LocalDate newTime = originalDate.plusMonths(Long.parseLong(generationTime));
		// 转换后的日期
		return newTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
	}

	private String getHouseContractCode(String bizObj, String cBillon) {

		String TENANT_ID = InvocationInfoProxy.getTenantid();

		BillCodeComponentParam billCodeComponentParam = new BillCodeComponentParam("ucf-amc-aum.aum_inventory_plan_card", "aum_inventory_plan_card", TENANT_ID, "-1",
				"aum.inventory." + bizObj, new BillCodeObj[] { new BillCodeObj() });
		String[] batchBillCodes = iBillCodeComponentService.getBatchBillCodes(billCodeComponentParam);
		return batchBillCodes[0];
	}

	// 同步方法确保线程安全
	public String generateNextNumber() {
		// 创建一个Random实例
		Random random = new Random();

		// 生成一个19位的随机数
		// 1018是19位数的最小值（1后面跟18个0），1019-1是19位数的最大值（9后面跟18个9）
		long min = 1000000000000000000L; // 1018
		long max = 9000000000000000000L; // 1019 - 1

		// 使用Random的nextLong方法，但是需要注意这个方法可能返回负数，所以我们需要取绝对值并调整范围
		long randomNumber;
		do {
			randomNumber = Math.abs(random.nextLong());
		} while (randomNumber < min || randomNumber > max);

		return String.valueOf(randomNumber);
	}

	private Map<String, String> getinventoryRange(String inventoryRange) {

		// 创建一个HashMap来存储键值对
		Map<String, String> resultMap = new HashMap<>();

		// 使用换行符将字符串分割成多行
		String[] lines = inventoryRange.split("\n");

		// 遍历每一行
		for (String line : lines) {
			// 查找冒号的位置
			int colonIndex = line.indexOf(':');

			// 确保找到了冒号，并且冒号不是第一个或最后一个字符
			if (colonIndex > 0 && colonIndex < line.length() - 1) {
				// 提取key和value，并去除前后的空格
				String key = line.substring(0, colonIndex).trim();
				String value = line.substring(colonIndex + 1).trim();

				// 将键值对添加到HashMap中
				resultMap.put(key, value);
			}
		}

		return resultMap;
	}
}
