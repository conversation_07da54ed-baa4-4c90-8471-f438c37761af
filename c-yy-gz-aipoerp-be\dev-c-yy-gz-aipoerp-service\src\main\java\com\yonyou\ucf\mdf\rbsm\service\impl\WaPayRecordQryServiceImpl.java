package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.rbsm.service.itf.IWaPayRecordQryService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 *         2025年3月30日
 */
@Service
public class WaPayRecordQryServiceImpl implements IWaPayRecordQryService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@Override
	public List<? extends IBillDO> queryWaPayRecord(QuerySchema schema) {
		List<? extends IBillDO> result = billQryRepository.queryBySchema("SMR002.SMR002.WaPayRecord", schema);
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyList();
		}
		return result;
	}

}
