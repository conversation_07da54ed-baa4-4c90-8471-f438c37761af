package com.yonyou.ucf.mdf.rbsm.utils;

import java.math.BigDecimal;

public class NumEncryptUtils {

	/**
	 * 数字加密
	 *
	 * @param bigDecimal 要加密的数据
	 * @param secretKey  加密的key
	 * @return 加密完的数字
	 */
	public static BigDecimal encrypt(BigDecimal bigDecimal, String secretKey) {

		return secret(bigDecimal, secretKey, true);
	}

	/**
	 * 数字解密
	 *
	 * @param bigDecimal 要解密的数据
	 * @param secretKey  加密的key
	 * @return 解密完数字
	 */
	public static BigDecimal decode(BigDecimal bigDecimal, String secretKey) {

		return secret(bigDecimal, secretKey, false);
	}

	/**
	 * 整数加密
	 *
	 * @param integer   要加密整数原值
	 * @param secretKey 秘钥 加密的key
	 * @return 加密完的数字
	 */
	public static Integer encrypt(Integer integer, String secretKey) {

		return new Integer(secret(integer, secretKey, true));
	}

	/**
	 * 整数加密解密
	 *
	 * @param integer   要解密的整数原值
	 * @param secretKey 秘钥 加密的key
	 * @return 解密完数字
	 */
	public static Integer decode(Integer integer, String secretKey) {

		return secret(integer, secretKey, false);
	}

	/**
	 * 加解密处理类
	 *
	 * @param bigDecimal 元数据
	 * @param secretKey  加密key
	 * @param isEncrypt  是否加密方法
	 * @return 处理后的数据
	 */
	private static BigDecimal secret(BigDecimal bigDecimal, String secretKey, boolean isEncrypt) {
		String numString;
		if (isEncrypt)
			numString = bigDecimal.toPlainString();
		else
			numString = bigDecimal.stripTrailingZeros().toPlainString(); // 如果解密去掉数据结尾的0
		String secretNumKey = String.valueOf(Math.abs(secretKey.hashCode()));
		return new BigDecimal(secret(numString, secretNumKey, isEncrypt));
	}

	/**
	 * 整数加解密处理类
	 *
	 * @param integer   整数对象
	 * @param secretKey 加密key
	 * @param isEncrypt 是否加密方法
	 * @return 处理后的数据
	 */
	private static Integer secret(Integer integer, String secretKey, boolean isEncrypt) {
		String numString = String.valueOf(integer);
		String secretNumKey = String.valueOf(Math.abs(secretKey.hashCode()));
		return new Integer(secret(numString, secretNumKey, isEncrypt));
	}

	/**
	 * 对字符串数字加解密处理类
	 *
	 * @param numString    数值字符串
	 * @param secretNumKey 秘钥，为数字型字符串
	 * @param isEncrypt    是否为加密还是解密 是否加密方法
	 * @return 处理后的数据
	 */
	private static String secret(String numString, String secretNumKey, boolean isEncrypt) {
		boolean negative = numString.startsWith("-");
		if (negative)
			numString = numString.substring(1);
		char[] numChars = numString.toCharArray();
		int offset = isEncrypt ? 0 : 1;
		int numLength = numChars.length - offset;
		if (!isEncrypt && numLength < 2) {
			throw new RuntimeException("decode fail, This number is illegal!");
		}
		char[] keyChars = secretNumKey.toCharArray();
		StringBuffer newValue = new StringBuffer();
		int keyLength = keyChars.length + offset;
		int tmpNum;
		for (int i = offset; i < numLength; i++) {
			if ('.' == numChars[i] || i >= keyLength)
				newValue.append(numChars[i]);
			else {
				tmpNum = Integer.valueOf(numChars[i]) ^ Integer.valueOf(keyChars[i - offset]);
				tmpNum = tmpNum < 10 ? tmpNum : Integer.valueOf(numChars[i]) - 48;
				newValue.append(tmpNum);
			}
		}
		// 防止首尾数字为0时失真
		if (isEncrypt) {
			newValue.insert(0, keyChars[0]);
			newValue.append(keyChars[0]);
		}
		if (negative)
			newValue.insert(0, "-");
		return String.valueOf(newValue);
	}
}
