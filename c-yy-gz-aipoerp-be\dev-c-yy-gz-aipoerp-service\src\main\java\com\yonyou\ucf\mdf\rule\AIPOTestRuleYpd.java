package com.yonyou.ucf.mdf.rule;

import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("aipoTestRuleYpd")
@Slf4j
public class AIPOTestRuleYpd implements IYpdCommonRul {
    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        log.error("保存完毕后拿到的实体 rulCtxVO-->{}",rulCtxVO);
        log.error("保存完毕后拿到的实体 params -- >{}",params);

        return result;
    }
}
