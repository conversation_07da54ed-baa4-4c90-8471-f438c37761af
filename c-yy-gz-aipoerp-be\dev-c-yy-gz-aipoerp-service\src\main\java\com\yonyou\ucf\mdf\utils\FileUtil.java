package com.yonyou.ucf.mdf.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @date 2025/2/11 15:21
 * @DESCRIPTION 类描述
 */
public class FileUtil {
    public static String readText(InputStream input, String charset) throws IOException {
        try {
            InputStreamReader reader = new InputStreamReader(input, charset);
            BufferedReader br = new BufferedReader(reader);
            StringBuffer sb = new StringBuffer();
            String tmp;
            while ((tmp = br.readLine()) != null) {
                sb.append(tmp);
                sb.append("\n");
            }
            br.close();
            return sb.toString();
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (Exception ignored) {
                }
            }
        }
    }
}
