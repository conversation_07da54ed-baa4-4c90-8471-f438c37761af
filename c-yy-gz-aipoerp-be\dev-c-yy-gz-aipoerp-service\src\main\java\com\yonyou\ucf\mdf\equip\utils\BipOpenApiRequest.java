package com.yonyou.ucf.mdf.equip.utils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.ucf.mdf.equip.model.AccessToken;
import com.yonyou.ucf.mdf.equip.model.GaUrl;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/17 10:53
 * @DESCRIPTION 类描述
 */
@Slf4j
@Component
public class BipOpenApiRequest {
	private static final ObjectMapper mapper = new ObjectMapper();
	@Value("${app.open-api.appKey}")
	private String appKey;

	@Value("${app.open-api.appSecret}")
	private String appSecret;

	@Autowired
	private GatewayService gatewayService;

	@SneakyThrows
	public <T> T doPost(String requestUrl, Object param, Class<T> type) {
		return mapper.readValue(doPost(requestUrl, param), type);
	}

	@SneakyThrows
	public <T> T doPost(String requestUrl, Object param, TypeReference<T> typeReference) {
		return mapper.readValue(doPost(requestUrl, param), typeReference);
	}

	public String doPost(String requestUrl, Object param) {
		String url = buildUrl(requestUrl);
		HttpRequest post = HttpUtil.createPost(url);
		if (ObjectUtil.isNotEmpty(param)) {
			post.body(JSONObject.toJSONString(param));
		}
		return post.execute().body();
	}

	public String doPostForm(String requestUrl, Map<String, Object> param) {
		String url = buildUrl(requestUrl);
		return HttpUtil.post(url, param);
	}

	@SuppressWarnings("rawtypes")
	public String doPostByFormData(String requestUrl, Map<String, Object> queryParams) {
		HttpURLConnection conn = null;
		// boundary就是request头和上传文件内容的分隔符
		String BOUNDARY = "---------------------------123821742118716";
		String result = "";
		try {
			String url = buildUrl(requestUrl);
			URL fullUrl = new URL(url);
			conn = (HttpURLConnection) fullUrl.openConnection();
			conn.setConnectTimeout(5000);
			conn.setReadTimeout(30000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
			OutputStream out = new DataOutputStream(conn.getOutputStream());
			StringBuffer strBuf = new StringBuffer();

			Iterator iter = queryParams.entrySet().iterator();
			while (iter.hasNext()) {
				Map.Entry entry = (Map.Entry) iter.next();
				String inputName = (String) entry.getKey();
				String inputValue = (String) entry.getValue();
				if (inputValue == null) {
					continue;
				}
				strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
				strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"\r\n\r\n");
				strBuf.append(inputValue);
			}
			out.write(strBuf.toString().getBytes());
			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();
			// 读取返回数据
			StringBuffer resBuf = new StringBuffer();
			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line = null;
			String NL = System.getProperty("line.separator");
			while ((line = reader.readLine()) != null) {
				resBuf.append(line).append(NL);
			}
			result = resBuf.toString();
			reader.close();
			log.error("调用API接口地址:{}, queryString:{}, resultString: {}", url, strBuf.toString(), result);
		} catch (Exception e) {
			// 其他异常
			log.error("调用API接口内部代码异常: {}", requestUrl, e);
			throw new RuntimeException(e);
		} finally {
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
		}
		return result;
	}

	public String doPost(String requestUrl, Object param, Map<String, String> headerMap) {
		String url = buildUrl(requestUrl);
		HttpRequest post = HttpUtil.createPost(url);
		if (ObjectUtil.isNotEmpty(param)) {
			post.body(JSONObject.toJSONString(param));
		}
		post.addHeaders(headerMap);
		return post.execute().body();
	}

	@SneakyThrows
	public <T> T doGet(String requestUrl, Object param, Class<T> type) {
		return mapper.readValue(doGet(requestUrl, param), type);
	}

	@SneakyThrows
	public <T> T doGet(String requestUrl, Object param, TypeReference<T> typeReference) {
		return mapper.readValue(doGet(requestUrl, param), typeReference);
	}

	@SneakyThrows
	public String doGet(String requestUrl, Object param) {
		String url = buildUrl(requestUrl);
		if (ObjectUtil.isNotEmpty(param)) {
			Map<String, Object> paramMap = mapper.readValue(JSONObject.toJSONString(param),
					new TypeReference<Map<String, Object>>() {
					});
			url = UrlUtil.addUrlParams(url, paramMap);
		}
		HttpRequest httpRequest = HttpUtil.createGet(url);
		return httpRequest.execute().body();
	}

	protected String buildUrl(String requestUrl) {
		GaUrl gateway = gatewayService.getGateway();
		String openUrl = gateway.getGatewayUrl() + requestUrl;
		String tokenUrl = gateway.getTokenUrl() + "/open-auth/selfAppAuth/getAccessToken";
		String accessToken = getAccessToken(tokenUrl);
		return UrlUtil.addUrlParams(openUrl, "access_token", accessToken);
	}

	@SneakyThrows
	protected String getAccessToken(String tokenUrl) {
		Map<String, Object> params = new TreeMap<>();
		params.put("appKey", appKey);
		params.put("timestamp", System.currentTimeMillis());
		params.put("signature", SignUtil.sign(params, appSecret));
		String url = UrlUtil.addUrlParams(tokenUrl, params);
		String body = HttpUtil.createGet(url).execute().body();
		if (StringUtils.isBlank(body)) {
			throw new RuntimeException("accessToken 获取失败，返回信息为空");
		}
		ResponseResult<AccessToken> result = mapper.readValue(body, new TypeReference<ResponseResult<AccessToken>>() {
		});
		if (!result.isSuccess()) {
			throw new RuntimeException("accessToken 获取失败，code:" + result.getCode() + "message:" + result.getMessage());
		}
		return result.getData().getAccessToken();
	}
}
