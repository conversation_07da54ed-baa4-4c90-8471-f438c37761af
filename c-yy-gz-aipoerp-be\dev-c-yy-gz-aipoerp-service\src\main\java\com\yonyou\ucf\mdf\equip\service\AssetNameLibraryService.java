package com.yonyou.ucf.mdf.equip.service;

import org.imeta.orm.base.BizObject;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/28 11:09
 * @DESCRIPTION 资产名称库接口
 */
public interface AssetNameLibraryService {
    /**
     * 从物料档案中拉取
     *
     * @return 拉取结果
     */
    Integer pullFromMaterial();

    /**
     * 根据物料ID获取名称库信息
     *
     * @param productIds 编码
     * @return list
     */
    List<Map<String, Object>> getAssentNameListByProductId(Object[] productIds);

    /**
     * 批量更新
     *
     * @param assentNameMap assentNameMap
     * @param bizObject bizObject
     */
    void updateAssentName(Map<String, Object> assentNameMap, BizObject bizObject);

    /**
     * 启用
     * @param ids id
     */
    void enable(List<String> ids);

    /**
     * 停用
     * @param ids id
     */
    void stop(List<String> ids);

    /**
     * 添加物料至名称库
     *
     * @param productList 物料信息list
     */
    void addAssentName(List<Map<String, Object>> productList);
}
