package com.yonyou.ucf.mdf.dept.service;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.dept.entity.DeptSyncLog;

/**
 * 部门同步服务接口
 */
public interface DeptSyncService {
    
    /**
     * 根据时间范围同步部门数据到NCC高级版
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param forceSync 是否强制同步（true：强制同步所有数据，false：过滤已同步成功的数据）
     * @param deptCode 指定部门编码（如果不为空，则只同步该部门，忽略时间范围和强制同步参数）
     * @return 同步结果统计信息
     */
    JSONObject syncDeptToNC(Date beginTime, Date endTime, boolean forceSync, String deptCode);
    
    /**
     * 根据时间范围查询部门数据
     * 
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 部门数据列表
     */
    List<JSONObject> queryDeptsByPubtsRange(Date beginTime, Date endTime);
    
    /**
     * 推送单个部门到NCC高级版
     * 
     * @param deptInfo 部门信息
     * @return 推送结果
     */
    DeptSyncLog pushSingleDeptToNC(JSONObject deptInfo);
    
    /**
     * 批量推送部门到NCC高级版
     * 
     * @param deptList 部门信息列表
     * @return 推送结果列表
     */
    List<DeptSyncLog> batchPushDeptToNC(List<JSONObject> deptList);
}
