package com.yonyou.ucf.mdf.equip.service;

import com.yonyou.ucf.mdf.equip.model.BatchResult;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import org.imeta.orm.base.BizObject;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/18 9:37
 * @DESCRIPTION 资产卡片导入数据接口类
 */
public interface EquipDataImportService {
    /**
     * 导入数据下推
     *
     * @param bizObjects 选中的数据
     */
    ResponseResult<BatchResult> pushDown(List<BizObject> bizObjects);
}
