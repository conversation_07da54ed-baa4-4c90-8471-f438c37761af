package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseBillVO;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseSaveParam;
import com.yonyou.ucf.mdf.rbsm.service.itf.ICommonExpenseService;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年3月19日
 */
@Slf4j
@Service
public class CommonExpenseServiceImpl implements ICommonExpenseService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@Autowired
	private BipOpenApiRequest apiRequest;

	private String expensebillUrl = "/yonbip/znbz/rbsm/api/bill/expensebill/save";

	@Override
	public CommonExpenseBillVO saveCommonExpense(CommonExpenseSaveParam param) {

		ResponseResult<CommonExpenseBillVO> resp = null;
		try {
			resp = apiRequest.doPost(expensebillUrl, param, new TypeReference<ResponseResult<CommonExpenseBillVO>>() {
			});
		} catch (Exception e) {
			log.error("调用通用报销单保存接口报错：参数{},异常：{}", JSONUtil.toJson(param), e);
			throw new RuntimeException("调用通用报销单保存接口报错:" + resp.getMessage());
		}

		if (!resp.isSuccess2()) {
			log.error("调用通用报销单保存接口报错:参数：{}", JSONUtil.toJson(param));
			throw new RuntimeException("调用通用报销单保存接口报错:" + resp.getMessage());
		}

		CommonExpenseBillVO result = resp.getData();
		if (result == null) {
			log.error("调用通用报销单保存接口结果返回空，参数：{}", param);
			throw new RuntimeException("调用通用报销单保存接口返回结果为空");
		}

		return result;
	}

	@Override
	public List<? extends IBillDO> queryCommonExpenseByIds(List<Object> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return Collections.emptyList();
		}
		List<? extends IBillDO> result = billQryRepository.findByIds("znbzbx.commonexpensebill.CommonExpenseBillVO",
				ids, 0);
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyList();
		}
		return result;
	}

}
