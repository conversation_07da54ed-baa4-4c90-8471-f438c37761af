package com.yonyou.ucf.mdf.rbsm.model;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Expsettleinfo {
	private String id;// 否 结算信息id(_status为Update更新时必填)
	private String vbankaccount;// 是 收款方帐号 示例：999999
	private String vbankaccname;// 是 收款方户名 示例：合法集资办公室
	private String pk_bankdoc;// 否 收款方开户行(支持id和code) 示例：****************
	private String pk_banktype;// 是 收款银行类别(支持id和code) 示例：****************
	private String pk_currency = "CNY";// 否 收款账户币种(支持id和code) 示例：****************
	private String pk_cusdocbank;// 否 供应商银行账户id(付款类型igathertype为1时，id和编码必填一项) 示例：****************
	private String pk_cusdocbank_code;// 否 供应商银行账户编码(付款类型igathertype为1时，id和编码必填一项)
	private String accttype;// 否 账号类型, 0:对公账号、1:对私账号
	private String pk_cusdoc;// 否 供应商id(付款类型igathertype为1时，id和编码必填一项) 示例：****************
	private String pk_cusdoc_code;// 否 供应商编码(付款类型igathertype为1时，id和编码必填一项)
	private String igathertype = "1";// 是 收款类型(0:个人;1:供应商;2:客户) 示例：1
	private String pk_balatype;// 否 结算方式id(结算方式编码和id必填一项) 示例：****************
	private String pk_balatype_code;// 否 结算方式编码(结算方式编码和id必填一项)
	private String balatypesrvattr = "0";// 是 结算方式业务属性(0:银行业务;1:现金业务） 示例：0
	private String centerpriseorg;// 是 资金组织(支持id和code) 示例：****************
	private String pk_currency_opp = "CNY";// 否 付款账户币种(支持id和code) 示例：****************
	private String vmemo_opp;// 否 付款账户备注
	private String pk_enterprisebankacct;// 否 企业银行账户(支持id和code，结算方式为银行转账时必填) 示例：****************
	private String vbankaccount_opp;// 否 付款银行账号(结算方式为银行转账时必填) 示例：1
	private String accttype_opp;// 否 付款账户类型（0:基本;1:一般;2:临时;3:专用） 示例：0
	private String vbankaccname_opp;// 否 付款账户户名 示例：1
	private String pk_banktype_opp;// 否 付款银行类别(支持id和code，结算方式为银行转账时必填) 示例：****************
	private String pk_bankdoc_opp;// 否 付款开户行(支持id和code，结算方式为银行转账时必填) 示例：****************
	private String pk_enterprisecashacct;// 否 企业现金账户(支持id和code，结算方式为现金时必填)
	private String vcurrency = "CNY";// 是 报销币种(支持id和code) 示例：****************
	private String vnatcurrency = "CNY";// 是 组织本币(支持id和code) 示例：****************
	private String vnatexchratetype = "01";// 否 组织本币汇率类型(支持id和code，启用多币种必填) 示例：py7y8nze
	private String dnatexchratedate;// 否 组织本币汇率日期(格式：yyyy-MM-dd，启用多币种必填) 示例：2021-09-26
	private String nnatbaseexchrate = "1";// 否 组织本币企业汇率，启用多币种必填 示例：1
	private String nnatexchrate = "1";// 是 组织本币汇率 示例：1
	private String vsettlecurrency = "CNY";// 是 结算币种(支持id和code) 示例：****************
	private String nsummny;// 是 付款金额 示例：1
	private String nsettlesummny;// 是 期望收款金额 示例：1
	private String nnatsettlesummny;// 是 期望收款金额-本币 示例：1
	private String _status = "Insert";// 否 操作标识, Insert:新增、Update:更新 示例:Insert 示例：Insert

	private String nsettlebaseexchrate = "1";
	private String nsettleexchrate = "1";

	private JSONObject expsettleinfoDcs; // 特征组自定义字段
}
