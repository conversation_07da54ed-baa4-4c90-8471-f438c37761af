{"default": {"base_url": "${backEndUrl}", "tpl_url": "${tpl_url}", "report_url": "${report_url}", "print_url": "${backEndUrl}", "workflow_url": "${bpmrestServer}", "customize_button_url": "${customize_button_url}", "file_url": "${file_url}", "cooperation_url": "${cooperation_url}", "static_url": "${report_url}"}, "daily": {"base_url": "${backEndUrl}", "tpl_url": "${tpl_url}", "report_url": "${report_url}", "print_url": "${backEndUrl}", "workflow_url": "${bpmrestServer}", "customize_button_url": "${customize_button_url}", "file_url": "${file_url}", "cooperation_url": "${cooperation_url}", "static_url": "${report_url}"}, "iteration": {"base_url": "${backEndUrl}", "tpl_url": "${tpl_url}", "report_url": "${report_url}", "print_url": "${backEndUrl}", "workflow_url": "${bpmrestServer}", "customize_button_url": "${customize_button_url}", "file_url": "${file_url}", "cooperation_url": "${cooperation_url}", "static_url": "${report_url}"}, "test": {"base_url": "${backEndUrl}", "tpl_url": "${tpl_url}", "report_url": "${report_url}", "print_url": "${backEndUrl}", "workflow_url": "${bpmrestServer}", "customize_button_url": "${customize_button_url}", "file_url": "${file_url}", "cooperation_url": "${cooperation_url}", "static_url": "${report_url}"}, "pre": {"base_url": "${backEndUrl}", "tpl_url": "${tpl_url}", "report_url": "${report_url}", "print_url": "${backEndUrl}", "workflow_url": "${bpmrestServer}", "customize_button_url": "${customize_button_url}", "file_url": "${file_url}", "cooperation_url": "${cooperation_url}", "static_url": "${report_url}"}, "prod": {"base_url": "${backEndUrl}", "tpl_url": "${tpl_url}", "report_url": "${report_url}", "print_url": "${backEndUrl}", "workflow_url": "${bpmrestServer}", "customize_button_url": "${customize_button_url}", "file_url": "${file_url}", "cooperation_url": "${cooperation_url}", "static_url": "${report_url}"}, "domainKey": "c-yy-gz-aipoerp"}