package com.yonyou.ucf.mdf.sample.controller;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.sample.service.ISmsMessageSender;

import lombok.extern.slf4j.Slf4j;

/**
 * 双因子认证-阿里云短信验证
 */
@Controller
@RequestMapping("/sendphonemessage")
@Slf4j
public class SendPhoneMessageController {

    @Autowired
    ISmsMessageSender IMessageSender;

    @ResponseBody
    @RequestMapping(value = "/send")
    public JSONObject send (HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsonObj){
        JSONObject result = new JSONObject();
        String mobile = jsonObj.get("phone").toString();
        String msg = jsonObj.get("msg").toString();
        log.error("SendPhoneMessageController入参：" + jsonObj.toString());
        try {
			String[] code = new String[] { getAnalysisVarCode(msg) };
//            if (StringUtils.isNotEmpty(msg)) {
//                String[] splitArr = msg.split("验证码：");
//                if (splitArr.length > 1) {
//                    String codes = splitArr[1];
//                    code[0] = codes.substring(0, 6);
//                }
//            }
            boolean success = IMessageSender.send(mobile, msg, code,"");
            if(success){
                result.put("success",true);
            }else{
                result.put("success",false);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.put("success",false);
        }
        return result;
    }

	private String getAnalysisVarCode(String msg) {
		String code = "";
		// 定义正则表达式模式，匹配验证码部分
		String regex = "(验证码|Code)：(\\d+)";

		// 创建Pattern对象
		Pattern pattern = Pattern.compile(regex);

		// 创建Matcher对象
		Matcher matcher = pattern.matcher(msg);
		if (matcher.find()) {
			code = matcher.group(2);
		}
		return code;
	}
}
