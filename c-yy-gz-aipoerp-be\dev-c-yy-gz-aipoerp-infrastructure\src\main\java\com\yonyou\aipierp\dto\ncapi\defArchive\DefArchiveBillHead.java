package com.yonyou.aipierp.dto.ncapi.defArchive;

import com.alibaba.fastjson.JSONObject;

public class DefArchiveBillHead extends JSONObject {

    /**
     * "0001"（集团code）,
     */
    String PK_GROUP = "pk_group";
    /**
     * DEF_DA_test01-1"（编码）,
     */
    String CODE ="code";
    /**
     * "DEF_DA_test011-1"（名字）,
     */
    String NAME = "name";
    /**
     *  "否-1"（备注）,
     */
    String MEMO = "memo";
    /**
     * ""（父级档案id，传bip旗舰版的id，不传就是最上级）,
     */
    String PID = "pid";
    /**
     * "DEF_DA_test01"（助记码）,
     */
    String MNECODE = "mnecode";
    /**
     * "DEF_DA"（档案类型编码 用于区分 软件名称，使用权资产档案）,
     */
    String PK_DEFDOCLIST = "pk_defdoclist";
    /**
     * "DEF_DA_test01"（简称）,
     */
    String SHORTNAME = "shortname";
    /**
     * "0001"（如果传集团的code就是集团级别的，否则传对应旗舰版组织id）,
     */
    String PK_ORG = "pk_org";
    /**
     *"def1"（传旗舰版id）(def2~def20 看需求使用)
     */
    String DEF1 = "def1";

    public void setPk_group(String pk_group) {
        this.put(PK_GROUP, pk_group);
    }

    public void setCode(String code) {
        this.put(CODE, code);
    }

    public void setName(String name) {
        this.put(NAME, name);
    }

    public void setMemo(String memo) {
        this.put(MEMO, memo);
    }

    public void setPid(String pid) {
        this.put(PID, pid);
    }

    public void setMnecode(String mnecode) {
        this.put(MNECODE, mnecode);
    }

    public void setPk_defdoclist(String pk_defdoclist) {
        this.put(PK_DEFDOCLIST, pk_defdoclist);
    }

    public void setShortname(String shortname) {
        this.put(SHORTNAME, shortname);
    }

    public void setPk_org(String pk_org) {
        this.put(PK_ORG, pk_org);
    }

    public void setDef1(String def1) {
        this.put(DEF1, def1);
    }
}
