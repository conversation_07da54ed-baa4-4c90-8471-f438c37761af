package com.yonyou.ucf.mdf.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.enums.TaskTypeEnum;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.MapProcessor;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.utils.AIPODaoHelper;
import com.yonyou.ypd.bill.basic.entity.WeakTypingDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import com.yonyou.iuap.yms.api.IYmsJdbcApi;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class AIPORepository {

    // 业务单元元数据实体uri
    public static final String BASEORGVO_URI = "org.func.BaseOrg";
    // 部门元数据实体uri
    public static final String ADMINORGVO_URI = "bd.adminOrg.AdminOrgVO";
    // 复杂版员工信息元数据实体uri
    public static final String STAFF_URI = "hred.staff.Staff";
    // 复杂版员工信息任职信息元数据实体uri
    public static final String STAFFJOB_URI = "hred.staff.StaffJob";
    // 复杂版员工其他 其他任职信息 元数据实体uri
    public static final String STAFFPART_URI = "hred.staff.StaffPart";
    // 企业银行账户实体uri
    public static final String ENTERBANKACC_URI = "bd.enterprise.OrgFinBankacctVO";
    // 企业银行账户-币种uri
    public static final String ENTERBANKACC_CURRENCY_URI = "bd.enterprise.BankAcctCurrencyVO";
    // 币种档案-uri
    public static final String CURRENCYTENANT_URI = "bd.currencytenant.CurrencyTenantVO";
    // 账户用途uri
    public static final String ACCOUNTPURPOSEVO_URI = "bd.enterprise.AccountPurposeVO";
    // 开户行uri
    public static final String BANKDOTVO_URI = "bd.bank.BankDotVO";
    // 区域行政类型 uri(地区)
    public static final String BASEREGIONVO_URI = "bd.region.BaseRegionVO";
    // 项目uri
    public static final String PROJECTVO_URI = "bd.project.ProjectVO";
    // 项目适用范围实体uri
    public static final String PROJECTAPPLYRANGE_URI = "bd.project.ProjectApplyRange";
    // 项目类别实体uri
    public static final String PROJECTCLASSVO_URI = "bd.project.ProjectClassVO";

    // 客户档案实体uri
    public static final String MERCHANT_URI = "aa.merchant.Merchant";
    // 客户档案适用范围实体uri
    public static final String MERCHANTAPPLYRANGE_URI = "aa.merchant.MerchantApplyRange";
    // 客户分类实体uri
    public static final String CUSTCATEGORY_URI = "aa.custcategory.CustCategory";
    // 供应商实体uri
    public static final String VENDOR_URI = "aa.vendor.Vendor";
    // 供应商适用范围实体uri
    public static final String VENDORORG_URI = "aa.vendor.VendorOrg";
    // 供应商分类实体uri
    public static final String VENDORCLASS_URI = "aa.vendorclass.VendorClass";

    @Autowired
    AIPODaoHelper aipoDaoHelper;

    @Qualifier(value = "baseDAO")
    @Autowired
    IYmsJdbcApi ymsJdbcApi;




    /**
     * 通过组织id获取组织信息
     *
     * @param id 组织id
     */
    public WeakTypingDO queryOrgInfoById(String id) {
        QuerySchema querySchema = QuerySchema.create().fullname(BASEORGVO_URI).addSelect("*");
        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(id)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> orgInfo = (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(orgInfo)) {
                return orgInfo.get(0);
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("根据id查询业务单元信息失败--->{}", e);
            throw new BusinessException("根据id查询业务单元信息失败，请稍后再试");
        }
    }

    public JSONObject getDeptInfoById(String id) {
        QuerySchema querySchema = QuerySchema.create().fullname(ADMINORGVO_URI).addSelect("*");
        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(id)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> deptInfo = (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(deptInfo)) {
                return (JSONObject) JSON.toJSON(deptInfo.get(0).toMap());
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("根据id查询部门信息失败--->{}", e);
            throw new BusinessException("根据id查询部门信息失败，请稍后再试");
        }
    }



    /**
     * 根据时间戳范围查询员工完整信息
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    public List<WeakTypingDO> queryStaffsByPubts(Date beginTime, Date endTime) {
        // certType.code:证件类型-编码
        QuerySchema querySchema = QuerySchema.create().fullname(STAFF_URI).addSelect("*,certType.code");
        // psnclId_code:人员类别-编码
        // jobId.code: 职务-编码
        // postId.code : 岗位编码
        querySchema.addCompositionSchema(QuerySchema.create().fullname(STAFFJOB_URI).addSelect("*,psnclId.code,jobId.code,postId.code").name("staffJob"));
        querySchema.addCompositionSchema(QuerySchema.create().fullname(STAFFPART_URI).addSelect("*").name("staffPart"));

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("pubts").between(beginTime,endTime)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            return  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
        } catch (Exception e) {
            log.error("根据时间戳范围查询员工信息失败--->{}", e);
            throw new BusinessException("根据时间戳范围查询员工信息失败，请稍后再试");
        }
    }

    /**
     * 根据id查询员工完整信息
     *
     * @param id 员工id
     */
    public WeakTypingDO queryStaffsById(String id) {
        // certType.code:证件类型-编码
        QuerySchema querySchema = QuerySchema.create().fullname(STAFF_URI).addSelect("*,certType.code");
        // psnclId_code:人员类别-编码
        // jobId.code: 职务-编码
        // postId.code : 岗位编码
        querySchema.addCompositionSchema(QuerySchema.create().fullname(STAFFJOB_URI).addSelect("*,psnclId.code,jobId.code,postId.code").name("staffJob"));
        querySchema.addCompositionSchema(QuerySchema.create().fullname(STAFFPART_URI).addSelect("*").name("staffPart"));

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(id)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return list.get(0);
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询员工信息失败--->{}", e);
            throw new BusinessException("根据id查询员工信息失败，请稍后再试");
        }
    }

    /**
     * 按id查询企业银行账户
     *
     * @param id 身份证
     * @return {@link WeakTypingDO }
     */
    public WeakTypingDO queryEnterBankAccById(String id) {
        QuerySchema querySchema = QuerySchema.create().fullname(ENTERBANKACC_URI).addSelect("*");
        // 币种子表
        querySchema.addCompositionSchema(QuerySchema.create().fullname(ENTERBANKACC_CURRENCY_URI).addSelect("*,currency.code").name("currencyList"));

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(id)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return list.get(0);
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询企业银行账户信息失败--->{}", e);
            throw new BusinessException("根据id查询企业银行账户信息失败，请稍后再试");
        }
    }

    /**
     * 按 ID 查询币种
     *
     * @param currencyId 货币 ID
     * @return {@link WeakTypingDO }
     */
    public WeakTypingDO queryCurrencyById(String currencyId) {
        QuerySchema querySchema = QuerySchema.create().fullname(CURRENCYTENANT_URI).addSelect("*");
        querySchema.appendQueryCondition(
                QueryConditionGroup.and(
                        QueryCondition.name("id").eq(currencyId)
                )
        );
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return list.get(0);
            }else return null;
        } catch (Exception e) {
            log.error("查询币种信息失败：{}", e);
            throw new RuntimeException(e.getMessage(), e.getCause());
        }
    }


    /**
     * 按 ID 查询账户用途
     *
     * @param purposeId 用途 ID
     * @return {@link WeakTypingDO }
     */
    public WeakTypingDO queryAccountPurposeById(String purposeId) {
        if (StringUtils.isEmpty(purposeId)) return null;
        QuerySchema querySchema = QuerySchema.create().fullname(ACCOUNTPURPOSEVO_URI).addSelect("*");
        querySchema.appendQueryCondition(
                QueryConditionGroup.and(
                        QueryCondition.name("id").eq(purposeId)
                )
        );
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return list.get(0);
            }else return null;
        } catch (Exception e) {
            log.error("查询账户用途信息失败：{}", e);
            throw new RuntimeException(e.getMessage(), e.getCause());
        }
    }

    /**
     * 按 ID 查询开户行信息
     *
     * @param id 开户行id
     * @return {@link JSONObject }
     */
    public JSONObject queryBankDotById(String id) {
        if (StringUtils.isEmpty(id)) return null;
        QuerySchema querySchema = QuerySchema.create().fullname(BANKDOTVO_URI).addSelect("*");
        querySchema.appendQueryCondition(
                QueryConditionGroup.and(
                        QueryCondition.name("id").eq(id)
                )
        );
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("查询开户行信息失败：{}", e);
            throw new RuntimeException(e.getMessage(), e.getCause());
        }
    }

    /**
     * 按 ID 查询区域信息
     *
     * @param id 区域id
     * @return {@link JSONObject }
     */
    public JSONObject queryRegionById(String id) {
        if (StringUtils.isEmpty(id)) return null;
        QuerySchema querySchema = QuerySchema.create().fullname(BASEREGIONVO_URI).addSelect("*");
        querySchema.appendQueryCondition(
                QueryConditionGroup.and(
                        QueryCondition.name("id").eq(id)
                )
        );
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("查询区域信息失败：{}", e);
            throw new RuntimeException(e.getMessage(), e.getCause());
        }
    }


    /**
     * 按id查询项目信息
     *
     * @param id 项目id
     * @return {@link WeakTypingDO }
     */
    public JSONObject queryProjectById(String id) {
        QuerySchema querySchema = QuerySchema.create().fullname(PROJECTVO_URI).addSelect("*");
        // 项目范围子表
        querySchema.addCompositionSchema(QuerySchema.create().fullname(PROJECTAPPLYRANGE_URI).addSelect("*").name("projectApplyRangeList"));

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(id)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询项目信息失败--->{}", e);
            throw new BusinessException("根据id查询项目信息失败，请稍后再试");
        }
    }

    /**
     * 按id查询项目类别信息
     *
     * @param classId 项目类别id
     * @return {@link WeakTypingDO }
     */
    public JSONObject queryProjectClassById(String classId) {
        if (StringUtils.isEmpty(classId)) return null;
        QuerySchema querySchema = QuerySchema.create().fullname(PROJECTCLASSVO_URI).addSelect("*");

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(classId)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询项目类别失败--->{}", e);
            throw new BusinessException("根据id查询项目类别失败，请稍后再试");
        }
    }

    /**
     * 按id客户档案信息
     *
     * @param id 项目id
     * @return {@link WeakTypingDO }
     */
    public JSONObject queryMerchantById(String id) {
        QuerySchema querySchema = QuerySchema.create().fullname(MERCHANT_URI).addSelect("*");
        // 客户档案-适用范围
        querySchema.addCompositionSchema(QuerySchema.create().fullname(MERCHANTAPPLYRANGE_URI).addSelect("*").name("merchantApplyRanges"));

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(id)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询客户档案信息失败--->{}", e);
            throw new BusinessException("根据id查询客户档案失败，请稍后再试");
        }
    }

    /**
     * 按id查询供应商档案信息
     *
     * @param id 供应商档案id
     * @return {@link JSONObject }
     */
    public JSONObject queryVendorById(String id) {
        QuerySchema querySchema = QuerySchema.create().fullname(VENDOR_URI).addSelect("*");
        // 供应商档案-适用范围
        querySchema.addCompositionSchema(QuerySchema.create().fullname(VENDORORG_URI).addSelect("*").name("vendorOrgs"));

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(id)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询供应商档案信息失败--->{}", e);
            throw new BusinessException("根据id查询供应商档案失败，请稍后再试");
        }
    }

    /**
     * 按id查询客户分类信息
     *
     * @return {@link JSONObject }
     */
    public JSONObject queryMerchantClassById(String classId) {
        if (StringUtils.isEmpty(classId)) return null;
        QuerySchema querySchema = QuerySchema.create().fullname(CUSTCATEGORY_URI).addSelect("*");

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(classId)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询客户分类信息失败--->{}", e);
            throw new BusinessException("根据id查询客户分类信息失败，请稍后再试");
        }
    }

    /**
     * 按id查询供应商分类信息
     *
     * @return {@link JSONObject }
     */
    public JSONObject queryVendorClassById(String classId) {
        if (StringUtils.isEmpty(classId)) return null;
        QuerySchema querySchema = QuerySchema.create().fullname(VENDORCLASS_URI).addSelect("*");

        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("id").eq(classId)
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        try {
            List<WeakTypingDO> list =  (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            if (CollectionUtils.isNotEmpty(list)){
                return (JSONObject) JSON.toJSON(list.get(0).toMap());
            }else return null;
        } catch (Exception e) {
            log.error("根据id查询供应商分类信息失败--->{}", e);
            throw new BusinessException("根据id查询供应商分类失败，请稍后再试");
        }
    }


    public Date getCurrentStaffTaskBeginTime() {
        SQLParameter parameter = new SQLParameter(true);
        StringBuilder sqlBuilder = new StringBuilder("SELECT MAX(data_sync_end_time) AS beginTime FROM aipo_ncstaffsync_tasklog where dr = 0 and task_type= ? and ytenant_id = ?");
        parameter.addParam(TaskTypeEnum.AUTO_SYNC.getValue());
        parameter.addParam(InvocationInfoProxy.getTenantid());
        JSONObject res = (JSONObject) JSON.toJSON(ymsJdbcApi.queryForObject(sqlBuilder.toString(), parameter, new MapProcessor()));
        if (res == null || res.isEmpty() || res.getDate("beginTime") == null) {
            return new Date();
        } else return res.getDate("beginTime");
    }

}
