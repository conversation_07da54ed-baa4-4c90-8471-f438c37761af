package com.yonyou.ucf.mdf.iris.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/16 11:29
 * @DESCRIPTION map工具
 */
public class MapUtil {
    public static Map<String, String> format(String res) {
        if (res == null) {
            return null;
        }
        if (res.length() < 1) {
            return null;
        }
        Map<String, String> map = new HashMap<String, String>();
        String[] params = res.split("&");
        for (String param : params) {
            if (param.startsWith("data")) {
                if (param.length() > 5)
                    map.put("data", param.substring(5));
                else
                    map.put("data", "");
            }
            if (param.startsWith("signature")) {
                if (param.length() > 10)
                    map.put("signature", param.substring(10));
                else
                    map.put("signature", "");
            }
        }
        return map;
    }
}
