package com.yonyou.ucf.mdf.aipo.model;

import java.io.Serializable;
import java.util.Date;

public class AddMessageModel implements Serializable {

    /**
     * 注册系统编码
     */
    private String registerCode ;

    /**
     * 业务系统待办主键
     */
    private String taskId;

    /**
     * 待办标题
     */
    private String title;

    /**
     * 待办发起人姓名
     */
    private String senderName;

    /**
     * 状态 0待办，1已办
     */
    private String state;

    /**
     * 业务系统发送者登录名
     */
    private String noneBindingSender;

    /**
     * 业务系统接收人登录名
     */
    private String noneBindingReceiver;

    /**
     * 待办发起日期  yyyy-MM-dd HH:mm
     */
    private String creationDate;

    /**
     * H5穿透地址
     */
    private String h5url;

    /**
     * PC穿透地址
     */
    private String url;


    //步骤名称（节点名称）
    private String nodeName;
    //业务类型（文字描述）
    private String businessType;
    //紧急类型0：一般，1:中急，2：紧急
    private String urgencyType;
    //待办发起人编号（手机号）
    private String creator;
    //接收人编号（手机号）
    private String receiver;

    //待办创建时间
    private String createDateTime;
    //处理后状态
    //0:同意，1:不同意，2:取消，3:驳回
    private String subState;
    //PC端穿透URL地址
    private String pcUrl;
    //移动端穿透URL地址
    private String appUrl;
    //原生应用穿透命令，穿透命令需要按这个顺序：iphone|ipad|android|wp
    private String appParam;
    //备注
    private String remarks;



    public AddMessageModel() {
    }

    public AddMessageModel(String registerCode, String taskId, String title, String senderName, String state, String noneBindingSender, String noneBindingReceiver, String creationDate, String h5url, String url, String nodeName, String businessType, String urgencyType, String creator, String receiver, String subState, String pcUrl, String appUrl, String appParam, String remarks) {
        this.registerCode = registerCode;
        this.taskId = taskId;
        this.title = title;
        this.senderName = senderName;
        this.state = state;
        this.noneBindingSender = noneBindingSender;
        this.noneBindingReceiver = noneBindingReceiver;
        this.creationDate = creationDate;
        this.h5url = h5url;
        this.url = url;
        this.nodeName = nodeName;
        this.businessType = businessType;
        this.urgencyType = urgencyType;
        this.creator = creator;
        this.receiver = receiver;
        this.subState = subState;
        this.pcUrl = pcUrl;
        this.appUrl = appUrl;
        this.appParam = appParam;
        this.remarks = remarks;
    }

    public String getRegisterCode() {
        return registerCode;
    }

    public void setRegisterCode(String registerCode) {
        this.registerCode = registerCode;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getNoneBindingSender() {
        return noneBindingSender;
    }

    public void setNoneBindingSender(String noneBindingSender) {
        this.noneBindingSender = noneBindingSender;
    }

    public String getNoneBindingReceiver() {
        return noneBindingReceiver;
    }

    public void setNoneBindingReceiver(String noneBindingReceiver) {
        this.noneBindingReceiver = noneBindingReceiver;
    }

    public String getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(String creationDate) {
        this.creationDate = creationDate;
    }

    public String getH5url() {
        return h5url;
    }

    public void setH5url(String h5url) {
        this.h5url = h5url;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getUrgencyType() {
        return urgencyType;
    }

    public void setUrgencyType(String urgencyType) {
        this.urgencyType = urgencyType;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getSubState() {
        return subState;
    }

    public void setSubState(String subState) {
        this.subState = subState;
    }

    public String getPcUrl() {
        return pcUrl;
    }

    public void setPcUrl(String pcUrl) {
        this.pcUrl = pcUrl;
    }

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    public String getAppParam() {
        return appParam;
    }

    public void setAppParam(String appParam) {
        this.appParam = appParam;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(String createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Override
    public String toString() {
        return "AddMessageModel{" +
                "registerCode='" + registerCode + '\'' +
                ", taskId='" + taskId + '\'' +
                ", title='" + title + '\'' +
                ", senderName='" + senderName + '\'' +
                ", state='" + state + '\'' +
                ", noneBindingSender='" + noneBindingSender + '\'' +
                ", noneBindingReceiver='" + noneBindingReceiver + '\'' +
                ", creationDate='" + creationDate + '\'' +
                ", h5url='" + h5url + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}
