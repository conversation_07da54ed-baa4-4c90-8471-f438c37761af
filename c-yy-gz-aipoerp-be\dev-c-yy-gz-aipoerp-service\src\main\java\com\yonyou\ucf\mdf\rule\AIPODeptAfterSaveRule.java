package com.yonyou.ucf.mdf.rule;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IDeptService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("aipoDeptAfterSaveRule")
public class AIPODeptAfterSaveRule implements IYpdCommonRul {

	@Autowired
	IDeptService deptService;

	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
		RuleExecuteResult result = new RuleExecuteResult();

		JSONObject jsonParams = (JSONObject) JSON.toJSON(params);

		log.error("组织机构部门卡片保存后规则：{}", jsonParams);

		JSONObject reqReturn = jsonParams.getJSONObject("return");
//		JSONObject requestData = jsonParams.getJSONObject("requestData");

		if (reqReturn.isEmpty()) {
			log.error("无法获取到部门保存结果同步高级版系统，请重试 params-->{}", params);
			throw new BusinessException("无法获取到部门保存结果同步高级版系统，请重试");
		} else {
			JSONObject bipDeptInfo = JSON.parseObject(JSON.toJSONString(reqReturn));
			deptService.pushDeptInfoToNC(bipDeptInfo);
		}

		return result;
	}
}
