package com.yonyou.ucf.mdf.utils;

import com.google.common.collect.ImmutableMap;
import com.yonyou.iuap.yms.http.*;
import org.imeta.core.utils.MetadataPropertyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.remoting.RemoteAccessException;
import org.springframework.util.ObjectUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Optional;

public class HttpUtil {

    private final static Logger logger = LoggerFactory.getLogger(HttpUtil.class);
    private static volatile YmsHttpClient ymsHttpClient;

    public static final Integer CODE_SUCCESS = 200;
    private static final Map<String, String> DEFAULT_JSON_UTF8_HEALDER = ImmutableMap.of("Content-Type", "application/json;charset=UTF-8");
    private static String ymsHttpClientBeanName = "aipoYMSHttpClient";

    /**
     * get请求无参数
     *
     * @param url
     * @return
     * @throws RuntimeException
     */
    public static String doGet(String url) throws RuntimeException {
        return doGet(url, null);
    }

    /**
     * get请求，可携带参数
     *
     * @param url      url
     * @param paramMap 请求参数
     * @return
     * @throws RuntimeException
     */
    public static String doGet(String url, Map<String, ?> paramMap) throws RuntimeException {
        return doGet(url, paramMap, null);
    }

    /**
     * get请求，携带参数与请求体
     *
     * @param url       url
     * @param paramMap  请求参数
     * @param headerMap 请求体
     * @return
     * @throws RuntimeException
     */
    public static String doGet(String url, Map<String, ?> paramMap, Map<String, String> headerMap) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.GET, headerMap, paramMap, null, null, false);
        return getBodyString(doRequest(request));
    }

    /**
     * get请求，携带参数与请求体
     *
     * @param url       url
     * @param paramMap  请求参数
     * @param headerMap 请求体
     * @return
     * @throws RuntimeException
     */
    public static String doGet(String url, Map<String, ?> paramMap, Map<String, String> headerMap, boolean needAuth) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.GET, headerMap, paramMap, null, null, needAuth);
        return getBodyString(doRequest(request));
    }

    /**
     * 可自定义返回类型 Map.class
     *
     * @param url          url
     * @param responseType 返回类型
     * @param <T>
     * @return
     * @throws RuntimeException
     */
    public static <T> T doGetWithResponseType(String url, Class<T> responseType) throws RuntimeException {
        return doGetWithResponseType(url, null, null, responseType);
    }

    public static <T> T doGetWithResponseType(String url, Map<String, ?> paramMap, Class<T> responseType) throws RuntimeException {
        return doGetWithResponseType(url, paramMap, null, responseType);
    }

    public static <T> T doGetWithResponseType(String url, Map<String, ?> paramMap, Map<String, String> headerMap, Class<T> responseType) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.GET, headerMap, paramMap, null, null, false);
        return getBody(doRequest(request), responseType);
    }

    /**
     * 异步get
     *
     * @param url       url
     * @param paramMap  请求参数
     * @param headerMap 请求头
     * @return
     * @throws RuntimeException
     */
    public static YmsHttpFuture<YmsHttpResponse> doAsyncGet(String url, Map<String, ?> paramMap, Map<String, String> headerMap) throws RuntimeException {
        return doAsyncGetWithCallback(url, paramMap, headerMap, null);
    }

    /**
     * 异步可回调
     *
     * @param url       url
     * @param paramMap  请求参数
     * @param headerMap 请求头
     * @param callback  回调函数
     * @return
     * @throws RuntimeException
     */
    public static YmsHttpFuture<YmsHttpResponse> doAsyncGetWithCallback(String url, Map<String, ?> paramMap, Map<String, String> headerMap, YmsHttpCallback<YmsHttpResponse> callback) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.GET, headerMap, paramMap, null, null, false);
        return doAsyncRequest(request, callback);
    }

    /**
     * post请求
     *
     * @param url
     * @param body 请求体
     * @return
     * @throws RuntimeException
     */
    public static String doPost(String url, Object body) throws RuntimeException {
        return doPost(url, body, null);
    }

    /**
     * post请求
     *
     * @param url
     * @param paramMap 请求参数
     * @return
     * @throws RuntimeException
     */
    public static String doPost(String url, Map<String, ?> paramMap) throws RuntimeException {
        return doPost(url, null, paramMap, null);
    }

    /**
     * post请求
     *
     * @param url       url
     * @param body      请求体
     * @param headerMap 请求头
     * @return
     * @throws RuntimeException
     */
    public static String doPost(String url, Object body, Map<String, String> headerMap) throws RuntimeException {
        return doPost(url, body, null, headerMap);
    }

    public static String doPost(String url, Map<String, ?> paramMap, Map<String, String> headerMap) throws RuntimeException {
        return doPost(url, null, paramMap, headerMap);
    }

    public static String doPost(String url, Object body, Map<String, ?> paramMap, Map<String, String> headerMap) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.POST, headerMap, paramMap, body, null, false);
        return getBodyString(doRequest(request));
    }

    public static String doPost(String url, Object body, Map<String, ?> paramMap, Map<String, String> headerMap, boolean needAuth) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.POST, headerMap, paramMap, body, null, needAuth);
        return getBodyString(doRequest(request));
    }

    public static String initTenantDispatchTask(String url, Map<String, String> formParam, Map<String, String> headerMap) {
        YmsHttpRequest ymsHttpRequest = new YmsHttpRequestBuilder().url(url).method(YmsHttpMethod.POST).addFormParam(formParam).useAuth(MetadataPropertyUtil.getAk(), MetadataPropertyUtil.getAs()).build();
        setHeaderMap(headerMap, ymsHttpRequest);
        return getBodyString(doRequest(ymsHttpRequest));
    }

    private static void setHeaderMap(Map<String, String> headerMap, YmsHttpRequest ymsHttpRequest) {
        YmsHttpHeader header = ymsHttpRequest.getHeader();
        if (headerMap != null) {
            for (Map.Entry<String, String> m : headerMap.entrySet()) {
                header.delete(m.getKey());
                ymsHttpRequest.setHeader(m.getKey(), m.getValue());
            }
        } else {
            ymsHttpRequest.setHeader("Content-Type", "application/json;charset=utf-8");
        }
    }
    /**
     * post请求自定义返回类型
     *
     * @param url          url
     * @param body         请求体
     * @param paramMap     请求参数
     * @param headerMap    请求头
     * @param responseType 返回类型
     * @param <T>
     * @return
     * @throws RuntimeException
     */
    public static <T> T doPostWithResponseType(String url, Object body, Map<String, ?> paramMap, Map<String, String> headerMap, Class<T> responseType) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.POST, headerMap, paramMap, body, null, false);
        return getBody(doRequest(request), responseType);
    }

    /**
     * 异步post
     *
     * @param url       url
     * @param body      请求体
     * @param paramMap  请求参数
     * @param headerMap 请求头
     * @return
     * @throws RuntimeException
     */
    public static YmsHttpFuture<YmsHttpResponse> doAsyncPost(String url, Object body, Map<String, ?> paramMap, Map<String, String> headerMap) throws RuntimeException {
        return doAsyncPostWithCallback(url, body, paramMap, headerMap, null);
    }

    /**
     * 异步post,带回调
     *
     * @param url       url
     * @param body      请求体
     * @param paramMap  请求参数
     * @param headerMap 请求头
     * @param callback  回调函数
     * @return
     * @throws RuntimeException
     */
    public static YmsHttpFuture<YmsHttpResponse> doAsyncPostWithCallback(String url, Object body, Map<String, ?> paramMap, Map<String, String> headerMap, YmsHttpCallback<YmsHttpResponse> callback) throws RuntimeException {
        YmsHttpRequest request = getRequest(url, YmsHttpMethod.POST, headerMap, paramMap, body, null, false);
        return doAsyncRequest(request, callback);
    }

    /**
     * 同步请求
     *
     * @param request
     * @return
     * @throws RuntimeException
     */
    private static YmsHttpResponse doRequest(YmsHttpRequest request) throws RuntimeException {
        try {
            return getYmsHttpClient().execute(request);
        } catch (Exception var4) {
            throw new RuntimeException("ymsHttpClient execute fail.", var4);
        }
    }

    /**
     * 异步request
     *
     * @param request
     * @param callback 回调函数
     * @return
     * @throws RuntimeException
     */
    private static YmsHttpFuture<YmsHttpResponse> doAsyncRequest(YmsHttpRequest request, YmsHttpCallback<YmsHttpResponse> callback) throws RuntimeException {
        YmsHttpClient httpClient = getYmsHttpClient();
        if (callback != null) {
            return httpClient.asyncExecute(request, callback);
        } else {
            return httpClient.asyncExecute(request);
        }
    }

    /**
     * 组装YmsHttpRequest
     *
     * @param url
     * @param ymsHttpMethod
     * @param headerMap              headerMap/存在默认值"Content-Type", "application/json;charset=UTF-8"
     * @param paramMap               请求参数
     * @param body
     * @param ymsHttpMultipartEntity ymsHttpMultipartEntity
     * @param needAuth               默认为false
     * @return
     */
    private static YmsHttpRequest getRequest(String url, YmsHttpMethod ymsHttpMethod, Map<String, String> headerMap, Map<String, ?> paramMap, Object body, YmsHttpMultipartEntity ymsHttpMultipartEntity, boolean needAuth) {
        YmsHttpRequestBuilder requestBuilder = (new YmsHttpRequestBuilder()).url(url).method(ymsHttpMethod);
        headerMap = addDefaultContentType(headerMap);
        YmsHttpHeader httpHeader = getHttpHeader(headerMap);

        requestBuilder.addHeader(httpHeader);
        addQueryParam(requestBuilder, paramMap);
        if (ymsHttpMethod == YmsHttpMethod.POST || ymsHttpMethod == YmsHttpMethod.PUT) {
            addBody(requestBuilder, body);
        }

        // 请求体为YmsHttpMultipartEntity
        if (!ObjectUtils.isEmpty(ymsHttpMultipartEntity)) {
            requestBuilder.multipartEntity(ymsHttpMultipartEntity);
        }

        // 加签
        if (needAuth) {
            requestBuilder.useAuth(MetadataPropertyUtil.getAk(), MetadataPropertyUtil.getAs());
        }

        return requestBuilder.build();

    }

    /**
     * 获取ymsHttpClient
     *
     * @return
     */
    private static YmsHttpClient getYmsHttpClient() {
        if (null == ymsHttpClient) {
            synchronized (HttpUtil.class) {
                if (null == ymsHttpClient) {
                    try {
                        // ypd280没有Mdd的AppContext
//                        ymsHttpClient = (YmsHttpClient) AppContext.getBean(ymsHttpClientBeanName, YmsHttpClient.class);
                        ymsHttpClient = (YmsHttpClient) AIPOSpringContextHolder.getBean(ymsHttpClientBeanName);
                        logger.info("Get YmsHttpClient instance from AppContext");
                    } catch (Exception var3) {
                        logger.error("Get YmsHttpClient instance from AppContext error.", var3);
                    }
                    if (ymsHttpClient == null) {
                        ymsHttpClient = YmsHttp.getInstance();
                        logger.error("Get YmsHttpClient instance from YmsHttp.");
                    }
                }
            }
        }
        return ymsHttpClient;
    }

    /**
     * 添加默认的content-type，调用方非必要无需header
     *
     * @param headerMap
     * @return
     */
    private static Map<String, String> addDefaultContentType(Map<String, String> headerMap) {
        Map<String, String> header = new HashMap();
        if (headerMap != null) {
            header.putAll(headerMap);
        }

        if (!header.containsKey("Content-Type")) {
            header.putAll(DEFAULT_JSON_UTF8_HEALDER);
        }

        return header;
    }

    /**
     * 处理请求体body
     *
     * @param requestBuilder
     * @param body
     */
    private static void addBody(YmsHttpRequestBuilder requestBuilder, Object body) {
        if (body != null) {
            if (body instanceof byte[]) {
                requestBuilder.body(((byte[]) body));
            } else if (body instanceof String) {
                requestBuilder.body(((String) body).getBytes(StandardCharsets.UTF_8));
            } else {
                requestBuilder.body(body);
            }
        }
    }

    /**
     * 添加请求参数
     *
     * @param requestBuilder
     * @param paramMap
     */
    private static void addQueryParam(YmsHttpRequestBuilder requestBuilder, Map<String, ?> paramMap) {
        if (paramMap != null) {
            Iterator var2 = paramMap.entrySet().iterator();

            while (var2.hasNext()) {
                Map.Entry<String, ?> entry = (Map.Entry) var2.next();
                requestBuilder.addQueryParam((String) entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
    }

    /**
     * 组装请求体
     *
     * @param headerMap
     * @return
     */
    private static YmsHttpHeader getHttpHeader(Map<String, String> headerMap) {
        YmsHttpHeader ymsHttpHeader = new YmsHttpHeader();
        Optional.ofNullable(headerMap).ifPresent((map) -> {
            map.entrySet().forEach((entry) -> {
                ymsHttpHeader.add((String) entry.getKey(), (String) entry.getValue());
            });
        });
        return ymsHttpHeader;
    }

    private static String getBodyString(YmsHttpResponse ymsHttpResponse) {
        isSuccessResponse(ymsHttpResponse);
        return ymsHttpResponse.getBodyString();
    }

    private static <T> T getBody(YmsHttpResponse ymsHttpResponse, Class<T> responseType) {
        isSuccessResponse(ymsHttpResponse);
        return ymsHttpResponse.getBody(responseType);
    }

    /**
     * 调用是否成功
     *
     * @param ymsHttpResponse
     * @return
     */
    private static boolean isSuccessResponse(YmsHttpResponse ymsHttpResponse) {
        if (CODE_SUCCESS.equals(ymsHttpResponse.getStatusCode())) {
            return true;
        } else {
            logger.error("访问外部系统异常,异常编码:{},exceptionMsg:{}", ymsHttpResponse.getStatusCode(), ymsHttpResponse.getBodyString());
            throw new RemoteAccessException("访问外部系统异常,异常编码:{" + ymsHttpResponse.getStatusCode() + "},exceptionMsg:{" + ymsHttpResponse.getBodyString() + "}");
        }
    }


}
