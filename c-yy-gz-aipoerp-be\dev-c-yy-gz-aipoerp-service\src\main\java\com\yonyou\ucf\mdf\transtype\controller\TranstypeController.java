package com.yonyou.ucf.mdf.transtype.controller;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.travelexpense.service.IBustypeQryService;
import com.yonyou.ypd.bill.response.ResultMessage;
import com.yonyou.ypd.mdf.adapter.controller.BaseController;

import lombok.extern.slf4j.Slf4j;

/**
 * 交易类型接口
 * 
 * <AUTHOR>
 *
 *         2025年4月3日
 */
@Slf4j
@RequestMapping("/transtype")
@RestController
public class TranstypeController extends BaseController {

	@Autowired
	private IBustypeQryService bustypeQryService;

	/**
	 * 获取交易类型编码
	 * 
	 * @param id       交易类型id
	 * @param response
	 */
	@GetMapping("/code")
	public void getTranstypeCode(@Param(value = "id") String id, HttpServletResponse response) {
		try {
			String result = bustypeQryService.queryBusTypeCodeById(id);
			renderJson(response, ResultMessage.data(result));
		} catch (Exception e) {
			log.error("id:{}", id, e);
			renderJson(response, ResultMessage.error(e.getMessage()));
		}

	}

}
