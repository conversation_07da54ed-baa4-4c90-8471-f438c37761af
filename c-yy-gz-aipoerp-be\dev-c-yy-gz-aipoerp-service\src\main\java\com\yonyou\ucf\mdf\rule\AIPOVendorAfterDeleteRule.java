package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IVendorService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component("aipoVendorAfterDeleteRule")
@Slf4j
public class AIPOVendorAfterDeleteRule implements IYpdCommonRul {

    @Autowired
    IVendorService vendorService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        JSONObject deleteParams = JSON.parseObject(JSON.toJSONString(params));
        if (MapUtils.isEmpty(deleteParams.getJSONObject("requestData"))) {
            log.error("无法获取到供应商档案删除的详细信息以删除客户申请单，请重试 deleteParams-->{}", deleteParams);
            throw new BusinessException("无法获取到供应商档案删除的详细信息以删除客户申请单，请重试");
        } else {
            JSONObject requestData = deleteParams.getJSONObject("requestData");
            vendorService.deleteVendorFromNC(requestData.getString("id"));
        }
        return result;
    }
}
