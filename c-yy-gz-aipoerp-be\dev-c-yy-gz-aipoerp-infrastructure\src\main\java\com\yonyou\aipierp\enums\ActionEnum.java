package com.yonyou.aipierp.enums;

/**
 * 动作对应数字
 */
public enum ActionEnum {

    /**
     * 不变
     */
    UNCHANGE("unchange", "0", 0),
    /**
     * 修改
     */
    UPDATE("update", "1", 1),
    /**
     * 新增
     */
    INSERT("insert", "2", 2),
    /**
     * 删除
     */
    DELETE("delete", "3", 3),

    ;

    private String code;
    private String valueStr;
    private Integer valueInt;

    ActionEnum(String code, String valueStr, Integer valueInt) {
        this.code = code;
        this.valueStr = valueStr;
        this.valueInt = valueInt;
    }

    public String getCode() {
        return this.code;
    }

    public String getValueStr() {
        return this.valueStr;
    }

    public Integer getValueInt() {
        return this.valueInt;
    }

    public static ActionEnum getByCode(String code) {
        for (ActionEnum actionEnum : values()) {
            if (actionEnum.code.equals(code)) {
                return actionEnum;
            }
        }
        return null;
    }

}
