package com.yonyou.ucf.mdf.utils;

import com.yonyou.iuap.yms.lock.YmsLock;
import com.yonyou.iuap.yms.lock.YmsLockFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Resource;

/**
 * redi分布式锁工具
 *
 */
@Slf4j
@Component
public class RedisLockHelper {

    private static final String LOCK_PREFIX = "AIPO_RLOCK";
    private static final String LOCK_SEPARATOR = ":";


    @Resource(name = "ymsDomainYmsLockFactory")
    private YmsLockFactory factory;


    /**
     * 尝试加锁
     *
     * @param bizPrefix 业务前缀
     * @param name      String
     * @return RLock
     */
    public YmsLock getLock(@Nonnull String bizPrefix, @Nonnull String name) {
        return factory.getLock(StringUtils.join(LOCK_PREFIX, bizPrefix, name, LOCK_SEPARATOR));
    }

}
