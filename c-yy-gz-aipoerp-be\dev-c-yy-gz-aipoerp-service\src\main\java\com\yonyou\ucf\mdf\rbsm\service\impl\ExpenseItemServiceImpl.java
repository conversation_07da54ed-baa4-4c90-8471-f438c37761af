package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.rbsm.model.ExpenseItem;
import com.yonyou.ucf.mdf.rbsm.service.itf.IExpenseItemService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

/**
 * <AUTHOR>
 *
 *         2025年3月10日
 */
@Service
public class ExpenseItemServiceImpl implements IExpenseItemService {

	@Autowired
	private IBillRepository billRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public Map<String, ExpenseItem> getAllExpenseItem() {
		String sql = createQueryExpenseItemSql();
		List<ExpenseItem> expenseItemList = billRepository.queryForDTOList(sql, null, ExpenseItem.class);
		if (CollectionUtils.isEmpty(expenseItemList)) {
			return Collections.emptyMap();
		}
		return expenseItemList.stream().collect(Collectors.toMap(ExpenseItem::getCode, v -> v));
	}

	/**
	 * 查询所有费用项目sql
	 * 
	 * @return
	 */
	private String createQueryExpenseItemSql() {
		StringBuilder sb = new StringBuilder();
		sb.append("select                           ");
		sb.append("	id,							    ");
		sb.append("	code,						    ");
		sb.append("	name,						    ");
		sb.append("	enabled,					    ");
		sb.append("	stopstatus,					    ");
		sb.append("	isEnd						    ");
		sb.append("from							    ");
		sb.append("	iuap_apdoc_finbd.bd_expenseitem ");
		sb.append("where						    ");
		sb.append("	ytenant_id = '%s'		        ");
		sb.append("	and enabled = '1'			    ");
		sb.append("	and stopstatus = '0'		    ");
		sb.append("	and isEnd = '1'				    ");
		return String.format(sb.toString(), tenantId);
	}

}
