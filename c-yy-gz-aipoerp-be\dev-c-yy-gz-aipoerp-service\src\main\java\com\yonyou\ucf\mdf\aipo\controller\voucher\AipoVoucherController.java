package com.yonyou.ucf.mdf.aipo.controller.voucher;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.aipo.crypto.EncryptionHolder;
import com.yonyou.ucf.mdf.aipo.crypto.EventCrypto;
import com.yonyou.ucf.mdf.aipo.crypto.PrivateAppCrypto;
import com.yonyou.ucf.mdf.aipo.model.EventContent;
import com.yonyou.ucf.mdf.aipo.model.EventType;
import com.yonyou.ucf.mdf.aipo.service.IVoucherEventProcessService;
import com.yonyou.ucf.mdf.aipo.service.VoucherEventProcessorFactory;
import com.yonyou.ypd.bill.utils.YpdAppContextUtil;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 事件订阅 凭证模块
 */

@RestController
@RequestMapping("rest/aipo/voucher")
@Slf4j
public class AipoVoucherController {
	// 配置项 ct d8b4a15621cf46a0ae566eb07cfc69f4
	// d1b4b623f8b564bbf39275ef05c9b15836cf9079
	private String voucherAppSecret = YpdAppContextUtil.getEnvSerivce().getProperty("YonBIP.Voucher.AppSecret",
			"9d4f30e426d3979c3390397c6e542c4a5b36908a");
	// private String voucherAppSecret =
	// YpdAppContextUtil.getEnvSerivce().getProperty("YonBIP.Voucher.AppSecret",
	// "74279371080a46c882d151bad54ddeff");
	private String voucherAppKey = YpdAppContextUtil.getEnvSerivce().getProperty("YonBIP.Voucher.AppKey",
			"74279371080a46c882d151bad54ddeff");
	// private String voucherAppKey =
	// YpdAppContextUtil.getEnvSerivce().getProperty("YonBIP.Voucher.AppKey",
	// "9d4f30e426d3979c3390397c6e542c4a5b36908a");
	private final ObjectMapper mapper = new ObjectMapper();

	@Autowired
	private VoucherEventProcessorFactory voucherEventProcessorFactory;

	@RequestMapping(value = "add", method = RequestMethod.POST)
	@Procedure(MediaType.APPLICATION_JSON_VALUE + ";charset = utf-8")
	@ResponseBody
	public String add(@RequestBody EncryptionHolder holder) throws Exception {
		if (ObjectUtil.isEmpty(holder)) {
			return "success";
		}
		log.error("凭证推送参数为：{}", JSONUtil.toJsonStr(holder));
		// 构建解密验签处理对象
		EventCrypto crypto = PrivateAppCrypto.newCrypto(voucherAppKey, voucherAppSecret);
		log.error("【构建解密验签处理对象】 {}", JSONUtil.toJsonStr(crypto));
		// 验签解密后的消息体
		String decryptMessage = crypto.decryptMsg(holder);
		log.error("【验签解密后的消息体】 {}", decryptMessage);
		// 反序列化后的消息内容对象
		EventContent content = mapper.readValue(decryptMessage, EventContent.class);
		log.error("【反序列化后的消息内容对象】 {}", JSONUtil.toJsonStr(content));
		EventType eventType = content.getType();

		// 事件订阅的接口没有认证信息，接口内没有上下文信息，使用robot执行
		RobotExecutors.runAs(content.getTenantId(), () -> {
			try {
				// 获取对应的事件处理器
				IVoucherEventProcessService processor = voucherEventProcessorFactory.getProcessor(eventType);
				if (processor != null) {
					try {
						log.error("事件类型: {}, 说明: 租户 {} 处理事件，信息: {}", content.getType(), content.getTenantId(),
								content.getContent());
						processor.processEvent(content);
					} catch (IllegalArgumentException e) {
						log.error("事件类型不匹配: {}", e.getMessage());
					}
				} else {
					log.error("未找到处理 {} 事件的处理器", eventType);
				}
			} catch (Exception e) {
				log.error("事件处理异常：" + e.getMessage(), e);
			}
		});

		return "success";
	}

}
