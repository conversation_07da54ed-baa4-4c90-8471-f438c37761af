package com.yonyou.ucf.mdf.rbsm.service.impl;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ucf.mdf.rbsm.model.WaData;
import com.yonyou.ucf.mdf.rbsm.model.WaDataQryParam;
import com.yonyou.ucf.mdf.rbsm.service.itf.IWaDataQueryService;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年3月19日
 */
@Slf4j
@Service
public class WaDataQueryServiceImpl implements IWaDataQueryService {

	@Autowired
	private BipOpenApiRequest apiRequest;

	private String waPayDetailUrl = "/yonbip/hrcloud/payfile/queryWaPayfileDetailByCond";

	@Override
	public WaData queryWaDataByParam(WaDataQryParam param) {

		ResponseResult<Object> resp = null;
		try {
			resp = apiRequest.doGet(waPayDetailUrl, param, new TypeReference<ResponseResult<Object>>() {
			});
			log.error("调用薪资发放单明细api接口返回:{}", JSONUtil.toJson(resp));
		} catch (Exception e) {
			log.error("调用薪资发放单明细api接口失败:{}", JSONUtil.toJson(param));
			throw new RuntimeException("调用薪资发放单明细api接口失败:" + resp.getMessage());
		}

		if (!resp.isSuccess2()) {
			log.error("调用薪资发放单明细api接口失败:{}", JSONUtil.toJson(param));
			if (resp.getData() != null) {
				throw new RuntimeException("调用薪资发放单明细api接口失败:" + resp.getData().toString());
			} else {
				throw new RuntimeException("调用薪资发放单明细api接口失败:" + resp.getMessage());
			}
		}

		WaData waData = JSONUtil.toBean(resp.getData(), WaData.class);

		if (waData == null || MapUtils.isEmpty(waData.getWaPayfileDetailList())) {
			log.error("调用薪资发放单明细api接口返回数据为空:{}", JSONUtil.toJson(param));
			throw new RuntimeException("调用薪资发放单明细api接口返回数据为空:" + resp.getMessage());
		}

		return waData;
	}

}
