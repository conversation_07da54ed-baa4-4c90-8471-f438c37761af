package com.yonyou.ucf.mdf.aipo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.dao.BaseDAO;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.aipo.service.IAipoQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description
 * @Package com.yonyou.ucf.mdf.service.Impl
 * @date 2023/11/10 17:33
 * @Copyright 用友网络-国资行业
 */
@Service
@Slf4j
public class AipoQueryServiceImpl implements IAipoQueryService {

    @Resource
    public BaseDAO baseDAO;

    private static String encryptInput = "ZXCVBNM_CDCT_Good";

    @Override
    public Object queryForeignBySql(JSONObject jsonObject) throws Exception {
        log.error("========查询入参=========" + jsonObject.toJSONString());
        String encrypt = jsonObject.getString("encrypt");//加密码
        String querySqlBase64 = jsonObject.getString("querySql");//加密后sql base64
        String sql = getBase64Decoded(querySqlBase64);
        boolean verifyMD5 = verifyMD5(encrypt);
        if (!verifyMD5) {
            throw new Exception("加密验证失败！请确认是否加密成功");
        }
//        if (sql != null && sql.contains("iuap_apcom_auth.ba_user")) {
//            return null;
//        }
        log.error("========查询sql=========" + sql);
        List<Map> objectList = baseDAO.queryForObject(sql, new MapListProcessor());
        log.error("========查询sql=========" + objectList == null || objectList.size() == 0 ? "空数据" : JSONObject.toJSONString(objectList));
        return objectList;
    }

    private static String getMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());

            StringBuilder sb = new StringBuilder();
            for (byte b : messageDigest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static boolean verifyMD5(String encrypted) {
        String encryptedInput = getMD5(encryptInput);
        return encryptedInput.equals(encrypted);
    }

    private static String getBase64Decoded(String input) {
        byte[] decodedBytes = Base64.decodeBase64(input.getBytes());
        String s = null;
        try {

            s = new String(decodedBytes, "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return s;
    }

    public String getTenantId() {
        String tenantid = InvocationInfoProxy.getTenantid();
        return tenantid;
    }
}
