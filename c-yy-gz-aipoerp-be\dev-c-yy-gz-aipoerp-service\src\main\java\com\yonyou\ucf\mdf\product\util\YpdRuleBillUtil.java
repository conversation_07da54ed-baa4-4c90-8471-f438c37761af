package com.yonyou.ucf.mdf.product.util;

import com.yonyou.ucf.mdd.ext.bill.rule.base.CommonRuleUtils;
import com.yonyou.ucf.mdd.ext.model.BillContext;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import org.imeta.orm.base.BizObject;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/5 15:31
 * @DESCRIPTION 工具类
 */
public class YpdRuleBillUtil {
    public static List<BizObject> getBills(RulCtxVO rulCtxVO, Map<String, Object> params) throws Exception {
        BillContext billContext = new BillContext(rulCtxVO.getBillnum(), rulCtxVO.getFullname());
        billContext.setAction(rulCtxVO.getAction());
        billContext.setDomain(rulCtxVO.getDomain());
        return CommonRuleUtils.getBills(billContext, params);
    }
}
