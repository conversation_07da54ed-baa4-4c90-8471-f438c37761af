package com.yonyou.ucf.mdf.aipo.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateTimeFormatterUtil {
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM = "yyyy-MM";
    public static final String YYYYMMdd = "yyyyMMdd";
    private static final DateTimeFormatter hms = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
    private static final DateTimeFormatter ymd = DateTimeFormatter.ofPattern(YYYY_MM_DD);
    private static final DateTimeFormatter ym = DateTimeFormatter.ofPattern(YYYY_MM);

    private static final DateTimeFormatter ymdSpe = DateTimeFormatter.ofPattern(YYYYMMdd);
    public static String hmsDate(LocalDateTime date) {
        return hms.format(date);
    }

    public static LocalDateTime hmsParse(String dateNow) {
        return LocalDateTime.parse(dateNow, hms);
    }

    public static String ymdSpeDate(long timestamp) {
        // 创建一个 SimpleDateFormat 对象，指定格式
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        // 将 Timestamp 转换为 String
        String formattedDate = sdf.format(timestamp);
        return formattedDate;
    }

    public static String ymdDate(LocalDateTime date) {
        return ymd.format(date);
    }
    public static String ymdDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        return sdf.format(date);
    }

    public static LocalDateTime ymdParse(String dateNow) {
        return LocalDate.parse(dateNow, ymd).atStartOfDay();
    }

    public static String ymDate(LocalDateTime date) {
        return ym.format(date);
    }

    public static LocalDateTime ymParse(String dateNow) {
        return LocalDateTime.parse(dateNow, ym);
    }

    public static String plus(String date, int n) {
        LocalDateTime currentDate = DateTimeFormatterUtil.ymdParse(date);
        LocalDateTime newDate = currentDate.plus(Period.ofMonths(n));
        return ymDate(newDate);
    }

    public static boolean after(String ym) throws ParseException {
        String ymNow = ymDate(LocalDateTime.now());
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM);
        Date parse = sdf.parse(ym);
        Date now = sdf.parse(ymNow);
        if (parse.after(now)) {
            return true;
        }
        return false;
    }

    public static boolean afterDays(String ymd) throws ParseException {
        String ymdNow = ymdDate(LocalDateTime.now());
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date parse = sdf.parse(ymd);
        Date now = sdf.parse(ymdNow);
        if (parse.after(now)) {
            return true;
        }
        return false;
    }

}
