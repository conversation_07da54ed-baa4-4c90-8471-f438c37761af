package com.yonyou.aipoerp.controller;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.IEnterBankAccService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/enterbank")
@Slf4j
public class AIPOEnterBankController {

    @Autowired
    IEnterBankAccService enterBankAccService;

    @RequestMapping("/nc/push")
    public JSONObject pushEnterBankToNc(@RequestBody JSONObject body){
        JSONObject resp = new JSONObject();
        log.error("成功进入了企业银行账户到nc的接口 body--->{}",body);
        body = JSONObject.parseObject(body.toJSONString()); // body 一定要再转一下
        try {
            enterBankAccService.pushEnterBankToNC(body, body);  // todo 远程规则改为后端函数，暂时这样写
        }catch (Exception e){
            resp.put("succeed",false);
            resp.put("message",e.getMessage());
            return resp;
        }
        resp.put("succeed",true);
        return resp;
    }

    @RequestMapping("/nc/delete")
    public JSONObject deleteEnterBankFromNc(@RequestBody JSONObject body){
        JSONObject resp = new JSONObject();
        log.error("成功进入了推送企业银行账户到nc的接口 body--->{}",body);
        try {
            enterBankAccService.deleteEnterBankFromNC(body.getString("id"));
        }catch (Exception e){
            resp.put("succeed",false);
            resp.put("message",e.getMessage());
            return resp;
        }
        resp.put("succeed",true);
        return resp;
    }

}
