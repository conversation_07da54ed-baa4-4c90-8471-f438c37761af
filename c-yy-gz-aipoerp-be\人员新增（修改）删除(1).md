# 1新增（修改）人员
根据ufinterface.bill.id 来判断，已经存在走修改，不存在走新增
### 1.1请求类型 
POST
### 1.2请求地址
 http://IP:port//nccloud/api/uapbd/psndocmanage/psndoc/add

### 1.3请求参数说明

#### 1.3.1参数说明
```
{
  "ufinterface": {
    "account": "账套编码（sm_busicenter的值）",
    "groupcode":"0001"(集团编码),
    "billtype": "psndoc"（固定值）,
    "isexchange": "Y"（一般默认为Y）,
    "replace": "Y"（一般默认为Y）,
    "roottag": "bill"（一般默认为bill）,
    "sender": "default"（一般默认为default）,
    "bill": {
      "id": "psndocbipid001"（旗舰版人员id唯一标识）,
      "billhead": {
        "pk_group": "0001"（集团编码）,
        "pk_org": "0000102"（旗舰版组织id）,
        "code": "test0001"（人员code）,
        "name": "测试人员1"（人员name）,
        "usedname": "测试1-update"（曾用名）,
        "birthdate": "2000-01-01"（生日）,
        "sex": "1"（1=男,2=女）,
        "idtype": "CN01"（证件类型CN01=身份证，CN02=护照，CN03=回乡证，CN04=外国人永久居留证，HK01=香港居民身份证，MO01=澳门居民身份证，TW01=台湾身份证，TW02=台胞证）,
        "id": "sfz123"（证件号）,
        "mnecode": "test0001"（助记码）,
        "joinworkdate": "2014-08-28"（参加工作日期）,
        "officephone": "35814"（办公电话）,
        "homephone": "35824"（家庭低昂）,
        "mobile": "35834"（手机）,
        "email": "<EMAIL>"（邮箱）,
        "psnjobs"（工作信息）: {
          "item": [
      {"pk_group": "0001"（集团编码）,
            "pk_org": "0000102"（bip旗舰版组织id）,
            "psncode": "test0001"（员工编码）,
            "pk_psncl": "0001"（人员类别编码）,
            "pk_dept": "dept1557"（旗舰版部门id）,
            "ismainjob": "Y"（是否主职责）,
            "indutydate": "2014-08-30"（任职开始日期）,
            "enddutydate":""(任职结束日期),
            "pk_job": "test-zw-1"(职务编码),
            "pk_post": "test-gw-1"(岗位编码)
          },      
          {"pk_group": "0001"（集团编码）,
            "pk_org": "0000102"（bip旗舰版组织id）,
            "psncode": "test0001"（员工编码）,
            "pk_psncl": "0001"（人员类别编码）,
            "pk_dept": "dept1557"（旗舰版部门id）,
            "ismainjob": "N"（是否主职责）,
            "indutydate": "2014-08-30"（任职开始日期）,
            "enddutydate":""(任职结束日期),
            "pk_job": "test-zw-1"(职务编码),
            "pk_post": "test-gw-1"(岗位编码)
          }
          ]
      
        }
      }
    }
  }
}
```
#### 1.3.2请求参数JSON示例

```
{
  "ufinterface": {
    "account": "0001",
    "groupcode":"0001",
    "billtype": "psndoc",
    "isexchange": "Y",
    "replace": "Y",
    "roottag": "bill",
    "sender": "default",
    "bill": {
      "id": "psndocbipid001",
      "billhead": {
        "pk_group": "0001",
        "pk_org": "0000102",
        "code": "test0001",
        "name": "测试人员1",
        "usedname": "测试1-update",
        "birthdate": "2000-01-01",
        "sex": "1",
        "idtype": "CN01",
        "id": "sfz123",
        "mnecode": "test0001",
        "joinworkdate": "2014-08-28",
        "officephone": "35814",
        "homephone": "35824",
        "mobile": "35834",
        "email": "<EMAIL>",
        "psnjobs": {
          "item": [{
            "pk_group": "0001",
            "pk_org": "0000102",
            "psncode": "test0001",
            "pk_psncl": "0001",
            "pk_dept": "dept1557",
            "ismainjob": "Y",
            "indutydate": "2014-08-30",
            "pk_job": "test-zw-1",
            "pk_post": "test-gw-1"
          }]
        }
      }
    }
  }
}
```

### 1.4返回示例
#### 1.4.1成功

```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "psndoc_1733828989969.xml",
            "billtype": "psndoc",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "psndocbipid001",
                    "filename": "psndoc_1733828989969.xml",
                    "resultdescription": "单据  psndocbipid001  开始处理...\r\n单据  psndocbipid001  处理完毕!\r\n",
                    "resultcode": "1",
                    "content": "1001ZZ1000000003HA21"
                }
            ],
            "successful": "Y"
        }
    },
    "code": "1000000000",
    "message": "0",
    "errorStack": null
}
```
#### 1.4.2失败
```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "psndoc_1733829444129.xml",
            "billtype": "psndoc",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "psndocbipid001",
                    "filename": "psndoc_1733829444129.xml",
                    "resultdescription": "单据  psndocbipid001  开始处理...\r\n单据  psndocbipid001  处理错误:业务插件处理错误：插件类=nc.bs.bd.pfxx.plugin.PsndocPfxxPlugin,异常信息:未找到code10000102对应组织\r\n",
                    "resultcode": "-32000",
                    "content": null
                }
            ],
            "successful": "N"
        }
    },
    "code": "1000000000",
    "message": "0",
    "errorStack": null
}

```



# 2删除人员

### 2.1请求类型 
POST
### 2.2请求地址
 http://IP:port/nccloud/api/uapbd/psndocmanage/psndoc/deletePsndocbyBipId

### 2.3请求参数说明
#### 2.3.1参数说明
  ```
{
  "bipid": "旗舰版人员id",
  "pk_org": "旗舰版组织id"
}

  ```
#### 2.3.2请求示例
  ```
  {
  "bipid": "psndocbipid001",
  "pk_org": "0000102"
}
  ```

### 2.4返回示例
#### 2.4.1成功
```
{
    "success": true,
    "data": "true",
    "code": "1000000000",
    "message": null,
    "errorStack": null
}
```
#### 2.4.2失败
```
{
    "success": false,
    "data": null,
    "code": "1000000001",
    "message": "无法查询到对应的人员",
    "errorStack": "报错堆栈"
}
```
