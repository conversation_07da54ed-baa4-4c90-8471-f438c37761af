
# 3新增自定义档案（软件名称，使用权资产档案）

### 3.1请求类型 
POST
### 3.2请求地址
 http://IP:port/nccloud/api/riamm/defdocmanage/defdoc/add

### 3.3请求参数说明

#### 3.3.1参数说明
  ```
{
    "ufinterface": {
        "billtype": "defdoc"（固定值）,
        "sender": "default"（固定值）,
        "replace": "Y"（固定值）,
        "isexchange": "Y"（固定值）,
        "account": "0001"（帐套编码）,
        "groupcode": "0001"(集团编码),
        "bill": {
            "billhead": {
                "pk_group": "0001"（集团code）,
                "code": "DEF_DA_test01-1"（编码）,
                "name": "DEF_DA_test011-1"（名字）,
                "memo": "否-1"（备注）,
                "pid": ""（父级档案id，传bip旗舰版的id，不传就是最上级）,
                "mnecode": "DEF_DA_test01"（助记码）,
                "pk_defdoclist": "DEF_DA"（档案类型编码 用于区分 软件名称，使用权资产档案）,
                "shortname": "DEF_DA_test01"（简称）,
                "pk_org": "0001"（如果传集团的code就是集团级别的，否则传对应旗舰版组织id）,
                "def1":"def1"（传旗舰版id）(def2~def20 看需求使用)
            },
            "id": "defdoc00001"（传旗舰版id）
        }
    }
}

  ```
#### 3.3.2请求示例
  ```
{
    "ufinterface": {
        "billtype": "defdoc",
        "sender": "default",
        "replace": "Y",
        "bill": {
            "billhead": {
                "pk_group": "0001",
                "code": "DEF_DA_test01-1",
                "name": "DEF_DA_test011-1",
                "memo": "否-1",
                "pid": "",
                "mnecode": "DEF_DA_test01",
                "pk_defdoclist": "DEF_DA",
                "shortname": "DEF_DA_test01",
                "pk_org": "0001",
                "def1":"defdoc00001"
                "def20":"def20"
            },
            "id": "defdoc00001"
        },
        "isexchange": "Y",
        "account": "0001",
        "groupcode": "0001"
    }
}
  ```

### 3.4返回示例
#### 3.4.1成功
```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "defdoc_1733884704856.xml",
            "billtype": "defdoc",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "defdoc00002",
                    "filename": "defdoc_1733884704856.xml",
                    "resultdescription": "单据  defdoc00002  开始处理...\r\n单据  defdoc00002  处理完毕!\r\n",
                    "resultcode": "1",
                    "content": "1001ZZ1000000003HPGV"
                }
            ],
            "successful": "Y"
        }
    },
    "code": "**********",
    "message": null,
    "errorStack": null
}
```
#### 3.4.2失败
```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "defdoc_1733884591397.xml",
            "billtype": "defdoc",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "defdoc00002",
                    "filename": "defdoc_1733884591397.xml",
                    "resultdescription": "单据  defdoc00002  开始处理...\r\n单据  defdoc00002  处理错误:业务插件处理错误：插件类=nc.bs.bd.pfxx.plugin.DefdocPfxxPlugin,异常信息:未找到code10000102对应组织\r\n",
                    "resultcode": "-32000",
                    "content": null
                }
            ],
            "successful": "N"
        }
    },
    "code": "**********",
    "message": null,
    "errorStack": null
}
```


# 4删除自定义档案

### 4.1请求类型 
POST
### 4.2请求地址
 http://IP:port/nccloud/api/riamm/defdocmanage/defdoc/deleteDefdocbyBipId

### 4.3请求参数说明

#### 4.3.1参数说明
  ```
{
  "bipid":"defdoc00001"（旗舰版档案id）,
  "version": "1"（固定值）
}

  ```
#### 4.3.2请求示例
  ```
{
  "bipid":"defdoc00001",
  "version": "1"
}
  ```

### 4.4返回示例
#### 4.4.1成功
```
{
    "success": true,
    "data": "success",
    "code": "**********",
    "message": null,
    "errorStack": null
}
```
#### 4.4.2失败
```
{
    "success": false,
    "data": null,
    "code": "1000000001",
    "message": "查询到def1 =1002A310000000036MN4的自定义档案维护信息异常不唯一或为空",
    "errorStack": "报错堆栈"
}
```

