package com.yonyou.ucf.mdf.rule;

import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.bill.rule.base.AbstractCommonRule;
import com.yonyou.ucf.mdd.ext.model.BillContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component("aipoTestRuleMdd")
@Slf4j
public class AIPOTestRuleMdd extends AbstractCommonRule {
    @Override
    public RuleExecuteResult execute(BillContext billContext, Map<String, Object> map) throws Exception {
        RuleExecuteResult result = new RuleExecuteResult();
        Map<String,Object> bill = getBills(billContext,map).get(0);
        log.error("保存完毕后拿到的实体 bill-->{}",bill);

        return result;
    }
}
