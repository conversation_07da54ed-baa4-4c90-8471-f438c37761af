package com.yonyou.ucf.mdf.aipo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class DataContainerVO {
    @JsonProperty("ampub_equipbase_StatusVO")
    private List<String> statusVO;

    @JsonProperty("ampub_ambase_CategoryVO")
    private List<String> categoryVO;

    // Getters and Setters
    public List<String> getStatusVO() {
        return statusVO;
    }

    public void setStatusVO(List<String> statusVO) {
        this.statusVO = statusVO;
    }

    public List<String> getCategoryVO() {
        return this.categoryVO;
    }

    public void setCategoryVO(List<String> categoryVO) {
        this.categoryVO = categoryVO;
    }
}
