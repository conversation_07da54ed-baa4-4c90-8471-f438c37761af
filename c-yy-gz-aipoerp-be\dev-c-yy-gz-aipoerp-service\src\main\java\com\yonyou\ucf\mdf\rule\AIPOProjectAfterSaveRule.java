package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.bill.rule.base.AbstractCommonRule;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdd.ext.model.BillContext;
import com.yonyou.ucf.mdf.api.IProjectService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Deprecated
@Slf4j
@Component("aipoProjectAfterSaveRule")
public class AIPOProjectAfterSaveRule implements IYpdCommonRul {

    @Autowired
    IProjectService projectService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        JSONObject jsonParams = (JSONObject) JSON.toJSON(params);
        JSONObject reqReturn = jsonParams.getJSONObject("return");
        JSONObject requestData = jsonParams.getJSONObject("requestData");

        if (MapUtils.isEmpty(reqReturn) || MapUtils.isEmpty(requestData)){
            log.error("无法获取到项目保存结果同步高级版系统，请重试 params-->{}",params);
            throw new BusinessException("无法获取到项目保存结果同步高级版系统，请重试");
        }else {

            if ("666666".equals(requestData.getString("orgid"))){
                log.error("所属组织为【企业账号级】不是公司级，不同步 bipRequestData-->{},bipSaveReturn-->{}", requestData,reqReturn);
            }else {
                projectService.pushProjectToNC(requestData, reqReturn);
            }
        }
        return result;
    }
}
