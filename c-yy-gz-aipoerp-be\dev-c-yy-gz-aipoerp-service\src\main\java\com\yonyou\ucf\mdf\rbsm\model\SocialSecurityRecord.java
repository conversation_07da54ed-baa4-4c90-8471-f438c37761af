package com.yonyou.ucf.mdf.rbsm.model;

import java.util.Date;
import java.util.List;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 社保缴交记录主表
 * @Date 2025-03-03 16:51:16
 * @since 2023/11/28
 **/
@YMSEntity(name = "SMR001.SMR001.SocialSecurityRecord", domain = "c-yy-gz-aipoerp")
public class SocialSecurityRecord extends SuperDO {
	public static final String ENTITY_NAME = "SMR001.SMR001.SocialSecurityRecord";
	public static final String PERIOD = "period";
	public static final String FINANCEORG = "financeOrg";
	public static final String FINANCEDEPT = "financeDept";
	public static final String PAYMENTORG = "paymentOrg";
	public static final String EXPENSEID = "expenseId";
	public static final String SOCIALTYPE = "socialType";
	public static final String SOCIALSECURITYDETAILLIST = "SocialSecurityDetailList";
	public static final String BUSINESSDATE = "businessDate";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 社保期间 */
	private String period;
	/* 财务组织 */
	private String financeOrg;
	/* 财务部门 */
	private String financeDept;
	/* 缴交机构 */
	private String paymentOrg;
	/* 对应报销单id */
	private String expenseId;
	/* 保险类型 */
	private String socialType;
	/* 社保缴交记录子表 */
	private List<SocialSecurityDetail> SocialSecurityDetailList;
	/* 单据日期 */
	private String businessDate;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setPeriod(String period) {
		this.period = period;
	}

	public void setFinanceOrg(String financeOrg) {
		this.financeOrg = financeOrg;
	}

	public void setFinanceDept(String financeDept) {
		this.financeDept = financeDept;
	}

	public void setPaymentOrg(String paymentOrg) {
		this.paymentOrg = paymentOrg;
	}

	public void setExpenseId(String expenseId) {
		this.expenseId = expenseId;
	}

	public void setSocialType(String socialType) {
		this.socialType = socialType;
	}

	public void setSocialSecurityDetailList(List<SocialSecurityDetail> SocialSecurityDetailList) {
		this.SocialSecurityDetailList = SocialSecurityDetailList;
	}

	public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getPeriod() {
		return period;
	}

	public String getFinanceOrg() {
		return financeOrg;
	}

	public String getFinanceDept() {
		return financeDept;
	}

	public String getPaymentOrg() {
		return paymentOrg;
	}

	public String getExpenseId() {
		return expenseId;
	}

	public String getSocialType() {
		return socialType;
	}

	public List<SocialSecurityDetail> getSocialSecurityDetailList() {
		return SocialSecurityDetailList;
	}

	public String getBusinessDate() {
		return businessDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
