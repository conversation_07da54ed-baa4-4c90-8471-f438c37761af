package com.yonyou.ucf.mdf.aipo.controller.voucher;

import com.yonyou.ucf.mdf.aipo.service.IAPipoInventoryPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/inventory")
@Slf4j
public class InventoryPlanController {

    @Autowired
    IAPipoInventoryPlanService inventoryPlanService;

    @PostMapping("/inventoryPlan")
    public Object InventoryPlan(){
        log.error("===========盘点计划新增定时任务开始=================");

        Map retMap = new HashMap();
        retMap.put("status","1");
        //发送信息内容
        retMap.put("content","盘点计划新增成功");
        //发送信息标题
        retMap.put("title","盘点计划");
        try{
            inventoryPlanService.IAPipoInventoryPlan();
        }catch (Exception e){
            e.printStackTrace();
            retMap.put("content","盘点计划新增失败："+e.getMessage());
            retMap.put("msg",e.getMessage());
            retMap.put("status","0");
        }
        return retMap;

    }

    @PostMapping("/submit")
    public Object submit() {
        log.error("===========盘点计划提交定时任务开始=================");
        Map retMap = new HashMap();
        retMap.put("status", "1");
        //发送信息内容
        retMap.put("content", "盘点计划提交成功");
        //发送信息标题
        retMap.put("title", "盘点计划提交");
        try{
            inventoryPlanService.inventoryPlanSubmit();
        }catch (Exception e){
            e.printStackTrace();
            retMap.put("content","盘点计划提交失败："+e.getMessage());
            retMap.put("msg",e.getMessage());
            retMap.put("status","0");
        }
        return retMap;
    }

    @PostMapping("/planToInventory")
    public Object planToInventory() {
        log.error("===========盘点计划生成盘点单定时任务开始=================");
        Map retMap = new HashMap();
        retMap.put("status", "1");
        //发送信息内容
        retMap.put("content", "盘点计划生成盘点单成功");
        //发送信息标题
        retMap.put("title", "盘点计划生成盘点单");
        try{
            inventoryPlanService.planToInventory();
        }catch (Exception e){
            e.printStackTrace();
            retMap.put("content","盘点计划生成盘点单失败："+e.getMessage());
            retMap.put("msg",e.getMessage());
            retMap.put("status","0");
        }
        return retMap;
    }

}
