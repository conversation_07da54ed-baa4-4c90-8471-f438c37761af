package com.yonyou.ucf.mdf.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.enums.NCApiUriEnum;
import com.yonyou.cloud.utils.StringUtils;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.utils.openApi.CompressUtil;
import com.yonyou.ucf.mdf.utils.HttpUtil;
import com.yonyou.ucf.mdf.utils.openApi.Encryption;
import com.yonyou.ucf.mdf.utils.openApi.SHA256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class NCOpenApiServiceImpl implements NCOpenApiService {


    NCOpenApiConfig ncOpenApiConfig;

    public NCOpenApiServiceImpl(NCOpenApiConfig ncOpenApiConfig) {
        this.ncOpenApiConfig = ncOpenApiConfig;
    }

    @Override
    public JSONObject getToken() {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.TOKEN_GET.getUri();
        // 必须手动拼接URL，否则会有转码问题
        StringBuilder urlBuilder = new StringBuilder(url);
        urlBuilder.append("?biz_center=").append(ncOpenApiConfig.getBiz_center())
                  .append("&client_id=").append(ncOpenApiConfig.getClient_id())
                  .append("&usercode=").append(ncOpenApiConfig.getUsercode())
                  .append("&grant_type=").append(ncOpenApiConfig.getGrant_type())
                  .append("&client_secret=").append(getClientSecret())
                  .append("&signature=").append(getSignature());
        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type","application/x-www-form-urlencoded");
        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(urlBuilder.toString(),null,headers));
        if (!resp.getBooleanValue("success")){
            log.error("获取token请求没有成功取到token信息 resp-->{}",resp);
            throw new BusinessException("获取ncToken失败");
        }
        return resp;
    }

    /**
     * 通用的高级版api测试方法
     *
     * @param reqStr req jsonString，用作请求体
     * @param uri    URI    请求的uri
     * @return {@link JSONObject }
     */
    public JSONObject doCommonApi(String reqStr, String uri){
        String url = ncOpenApiConfig.getNcDomain() + uri;
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");
        String handleRequest;
        try {
            handleRequest = dealRequestBody(reqStr, security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, reqStr);

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    /**
     * 保存部门信息至NC高级版
     *
     * @param ufinterface ufinterface 部门信息实体
     * @return {@link JSONObject }
     */
    @Override
    public JSONObject addDeptEx(UFinterface ufinterface) {
        JSONObject reqBody = new JSONObject();
        reqBody.put("ufinterface",ufinterface);
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.DEPT_SAVE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");
        String handleRequest;
        try {
            handleRequest = dealRequestBody(reqBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(reqBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }


    /**
     * 根据 BIP ID 删除NC高级版部门
     *
     * @param deleteBody 删除正文
     * @return {@link JSONObject }
     */
    @Override
    public JSONObject deleteDeptByBipId(JSONObject deleteBody) {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.DEPT_DELETE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            handleRequest = dealRequestBody(deleteBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(deleteBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject savePsn(UFinterface ufinterface) {
        JSONObject reqBody = new JSONObject();
        reqBody.put("ufinterface",ufinterface);
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.PSN_SAVE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            // requestBody 需加密
            handleRequest = dealRequestBody(reqBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(reqBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject deletePsnByBipId(JSONObject deleteBody) {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.PSN_DELETE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            handleRequest = dealRequestBody(deleteBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(deleteBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject saveEnterBank(UFinterface ufinterface) {
        JSONObject reqBody = new JSONObject();
        reqBody.put("ufinterface",ufinterface);
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.BANK_SAVE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            // requestBody 需加密
            handleRequest = dealRequestBody(reqBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(reqBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject deleteEnterBankBipId(JSONObject deleteBody) {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.BANK_DELETE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            handleRequest = dealRequestBody(deleteBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(deleteBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject saveProject(UFinterface ufinterface) {
        JSONObject reqBody = new JSONObject();
        reqBody.put("ufinterface",ufinterface);
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.PROJECT_SAVE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            // requestBody 需加密
            handleRequest = dealRequestBody(reqBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(reqBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject deleteProject(JSONObject deleteBody) {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.PROJECT_DELETE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            handleRequest = dealRequestBody(deleteBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(deleteBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject saveDefArchive(UFinterface ufinterface) {
        JSONObject reqBody = new JSONObject();
        reqBody.put("ufinterface",ufinterface);
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.DEFARCHIVE_SAVE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            // requestBody 需加密
            handleRequest = dealRequestBody(reqBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(reqBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject deleteDefArchive(JSONObject deleteBody) {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.DEFARCHIVE_DELETE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            handleRequest = dealRequestBody(deleteBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(deleteBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject saveCustomer(JSONObject saveBody) {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.CUSTOMER_SAVE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            // requestBody 需加密
            handleRequest = dealRequestBody(saveBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(saveBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }

    @Override
    public JSONObject deleteCustomer(JSONObject deleteBody) {
        String url = ncOpenApiConfig.getNcDomain() + NCApiUriEnum.CUSTOMER_DELETE.getUri();
        JSONObject tokenInfos = getToken();

        String access_token = tokenInfos.getJSONObject("data").getString("access_token");
        String security_key = tokenInfos.getJSONObject("data").getString("security_key");

        String handleRequest;
        try {
            handleRequest = dealRequestBody(deleteBody.toString(), security_key, ncOpenApiConfig.getLevel());
        } catch (Exception e) {
            log.error("发送业务请求时加密请求体失败",e);
            throw new BusinessException("发送业务请求时加密请求体失败"+ e.getMessage());
        }
        Map<String, String> headers = getCommonHeaders(access_token, JSONObject.toJSONString(deleteBody));

        JSONObject resp = JSONObject.parseObject(HttpUtil.doPost(url,handleRequest,null,headers)) ;
        return resp;
    }






    /**
     * 获取加密后的密钥
     *
     * @return {@link String }
     */
    public String getClientSecret(){
        try {
            return URLEncoder.encode(Encryption.pubEncrypt(ncOpenApiConfig.getPub_key(), ncOpenApiConfig.getClient_secret()), "utf-8");
        } catch (Exception e) {
            log.error("获取ClientSecret加密字符串失败了",e);
            throw new BusinessException("查询Nctoken时，获取加密的ClientSecret字符串失败");
        }
    }

    /**
     * 签名，客户端模式下获取签名
     *
     * @return {@link String }
     */
    public String getSignature(){
        return SHA256Util.getSHA256(ncOpenApiConfig.getClient_id() + ncOpenApiConfig.getClient_secret() + ncOpenApiConfig.getPub_key(), ncOpenApiConfig.getPub_key());
    }

    /**
     * 获取通用标头
     *
     * @param requestBody 原生的请求体
     * @param access_token 获取token接口返回的access_token
     * @return {@link Map }<{@link String },{@link String }>
     */
    private Map<String,String> getCommonHeaders(String access_token, String requestBody){
        Map<String,String> headers = new HashMap<>();
        headers.put("access_token",access_token);
        headers.put("signature", SHA256Util.getSHA256(ncOpenApiConfig.getClient_id() + requestBody + ncOpenApiConfig.getPub_key(), ncOpenApiConfig.getPub_key()));
        headers.put("client_id",ncOpenApiConfig.getClient_id());
        return headers;
    }


    // 根据安全级别设置，表体是否加密或压缩
    private String dealRequestBody(String source, String security_key, String level) throws Exception {
        String result = null;
        if (StringUtils.isEmpty(level) || "level0".equals(level)) {
            result = source;
        } else if ("level1".equals(level)) {
            result = Encryption.symEncrypt(security_key, source);
        } else if ("level2".equals(level)) {
            result = CompressUtil.gzipCompress(source);
        } else if ("level3".equals(level)) {
            result = Encryption.symEncrypt(security_key, CompressUtil.gzipCompress(source));
        } else if ("level4".equals(level)) {
            result = CompressUtil.gzipCompress(Encryption.symEncrypt(security_key, source));
        } else {
            throw new BusinessException("无效的安全等级");
        }
        return result;
    }

}
