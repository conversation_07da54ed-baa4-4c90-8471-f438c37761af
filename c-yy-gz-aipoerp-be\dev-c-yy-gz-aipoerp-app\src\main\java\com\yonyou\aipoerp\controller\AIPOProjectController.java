package com.yonyou.aipoerp.controller;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.IProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/project")
@Slf4j
public class AIPOProjectController {

    @Autowired
    IProjectService projectService;

    @RequestMapping("/nc/push")
    public JSONObject pushProjectToNc(@RequestBody JSONObject body){
        JSONObject resp = new JSONObject();
        body = JSONObject.parseObject(body.toJSONString()); // body 一定要再转一下
        log.error("成功进入了推送项目到nc的接口 body--->{}",body);
        try {
            if ("666666".equals(body.getString("orgid"))){
                log.error("项目的所属组织为【企业账号级】不是公司级，不同步 bipRequestData-->{},bipSaveReturn-->{}", body,body);
            }else {
                projectService.pushProjectToNC(body, body); // todo 远程规则改为后端函数，暂时这样写
            }
        }catch (Exception e){
            resp.put("succeed",false);
            resp.put("message",e.getMessage());
            return resp;
        }
        resp.put("succeed",true);
        return resp;
    }

    @RequestMapping("/nc/delete")
    public JSONObject deleteProjectFromNc(@RequestBody JSONObject body){
        JSONObject resp = new JSONObject();
        log.error("成功进入了推送客户档案到nc的接口 body--->{}",body);
        try {
            if ("666666".equals(body.getString("orgid"))){  // todo 远程规则改为后端函数，暂时这样写
                log.error("项目的所属组织为【企业账号级】不是公司级，不同步 requestData-->{}", body);
            }else {
                projectService.deleteProjectFromNC(body.getString("id"),body.getString("orgid"));
            }
        }catch (Exception e){
            resp.put("succeed",false);
            resp.put("message",e.getMessage());
            return resp;
        }
        resp.put("succeed",true);
        return resp;
    }


}
