package com.yonyou.ucf.mdf.aipo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/09   10:45
 */
@Data
public class EventSyncLogExtendVO {
    /**
     * 事件id
     */
    private String eventid;
    /**
     * 单据id
     */
    private String billid;

    private String syncdata;

    /**
     * 账套编码
     */
    private String acccode;
    /**
     * 账套名称
     */
    private String accname;
    /**
     * 制单日期
     */
    private String voucherperiod;

    /**
     * 凭证号
     */
    private String billcode;

    /**
     * 凭证类型编码
     */
    private String vouchertypecode;

    /**
     * 同步类型
     */
    private String synctype;
    /**
     * 是否同步成功
     */
    private String issuccess;

    /**
     * 错误消息
     */
    private String errorMsg;
    /**
     * 发送消息
     */
    private String sendmsg;
    /**
     * 响应消息
     */
    private String retmsg;

    /**
     * 进入时间
     */
    private String entertime;
    /**
     * 时间
     */
    private String ts;
}
