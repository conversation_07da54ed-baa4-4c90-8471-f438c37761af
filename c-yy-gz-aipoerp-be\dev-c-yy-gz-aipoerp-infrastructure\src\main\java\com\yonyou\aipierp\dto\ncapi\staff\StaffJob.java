package com.yonyou.aipierp.dto.ncapi.staff;

import com.alibaba.fastjson.JSONObject;

public class Staff<PERSON>ob extends JSONObject {

    /**
     *  "0001"（集团编码）
     */
    public String PK_GROUP = "pk_group";
    /**
     * "0000102"（bip旗舰版组织id
     */
    public String PK_ORG = "pk_org";
    /**
     * "test0001"（员工编码）,
     */
    public String PSNCODE = "psncode";
    /**
     * "0001"（人员类别编码）,
     */
    public String PK_PSNCL = "pk_psncl";
    /**
     * "dept1557"（旗舰版部门id）,
     */
    public String PK_DEPT = "pk_dept";
    /**
     *  "Y"（是否主职责）,
     */
    public String ISMAINJOB = "ismainjob";
    /**
     * "2014-08-30"（任职开始日期）,
     */
    public String INDUTYDATE = "indutydate";
    /**
     * ""(任职结束日期),
     */
    public String ENDDUTYDATE = "enddutydate";
    /**
     * "test-zw-1"(职务编码),
     */
    public String PK_JOB = "pk_job";
    /**
     * "test-gw-1"(岗位编码)
     */
    public String PK_POST = "pk_post";

    public void setPk_group(String pk_group) {
        this.put(PK_GROUP, pk_group);
    }

    public void setPk_org(String pk_org) {
        this.put(PK_ORG, pk_org);
    }

    public void setPsncode(String psncode) {
        this.put(PSNCODE, psncode);
    }

    public void setPk_psncl(String pk_psncl) {
        this.put(PK_PSNCL, pk_psncl);
    }

    public void setPk_dept(String pk_dept) {
        this.put(PK_DEPT, pk_dept);
    }

    public void setIsmainjob(String ismainjob) {
        this.put(ISMAINJOB, ismainjob);
    }

    public void setIndutydate(String indutydate) {
        this.put(INDUTYDATE, indutydate);
    }

    public void setEnddutydate(String enddutydate) {
        this.put(ENDDUTYDATE, enddutydate);
    }

    public void setPk_job(String pk_job) {
        this.put(PK_JOB, pk_job);
    }

    public void setPk_post(String pk_post) {
        this.put(PK_POST, pk_post);
    }
}
