
#    1新增修改银行账户档案

###  1.1请求类型 
POST
###  1.2请求地址
 http://IP:port/nccloud/api/uapbd/bankmanage/bankaccbas/addEx

###  1.3请求参数说明

#### 1.3.1参数说明

```
{
	"ufinterface": {
		"account": "YSGZ",
		"billtype": "bankaccbas",
		"isexchange": "Y",
		"groupcode": "A",
		"replace": "Y",
		"sender": "default",
		"bill": {
			"id":"bipbankaccid001",(旗舰版银行账户表头id)
			"billhead": {
				"accclass": "2"(0=个人,1=客户,2=公司,3=供应商 ),必输
				"pk_org": "0001A9**********0ZVZ"(组织旗舰版组织id),必输
				"pk_group": "A"（集团）,必输
				"accnum": "testnum001"（账号）,必输
				"accname": "testname001"（户名）,必输
				"code": "testnum001"（编码）,必输
				"name": "testname001"（名称）,必输
				"pk_bankdoc": "pk_bankdoc"（开户银行）,必输
				"pk_banktype": "pk_banktype"（银行类别）,
				"mnecode": "111"（助记码）,
				"accopendate": "2019-04-13"（开户日期）,必输
				"arapprop": "2(0=收入,1=支出,2=收支)",必输
				"netqueryflag": "2"网银开通状态（0=未开通,1=开通查询,2=开通查询及支付,3=开通落地支付）,必输
				"accattribute": "3"账户属性（0=基本,1=临时,2=一般,3=专用）,必输
				"genebranprop": "2"总分属性（0=总账户,1=分账户,2=独立账户）,必输
				"groupaccount": "N"（是否集团账户）,必输
				"accstate": "0"账号状态（0=正常,1=冻结,2=部分冻结,3=销户）,必输
				"accountproperty": "0"账户性质（0=公司,1=个人）,必输
				"address": "永丰路68号"（所在地）,
				"contactpsn": "王"（联系人）,
				"tel": "36800"（电话）,
				"memo": "题目要大要长题目要大要长"（备注）,
				"pk_netbankinftp": "00017"（网银接口类别）,
				"combinenum": "111"（联行号）,
				"orgnumber": "111"（机构号/分行号）,
				"bankarea": "海淀分行上地支行"（开户地区）,
				"province": "北京"（省份）,
				"city": "海淀"（地区）,
				"customernumber": "10"（客户编号）,
				"issigned": "N"（签约）,
				"financeorg": "org1"（开户单位，旗舰版组织id）,必输
				"controlorg": "org1"（核算归属组织，旗舰版组织id）,必输
				"enablestate": "2"启用状态（1=未启用,2=已启用,3=已停用）,必输
				"combineaccname": "a"（人行联行名称）,
				"qrybalanceitf": "0"（查询余额接口 0=独立户余额接口,1=归集户余额接口）,
				"bankaccsub": {
					"item": [{
							"pk_currtype": "CNY"（币种 两边系统编码需要维护一致 CNY人民币,EUR欧元,GBP英镑,HKD港币,JPY日元,USD美元,MOP	澳门币,TWD	新台币）,必输
							"code": "11111-01"（编码）,必输
							"name": "人民币户"（名称）,必输
							"acctype": "2"账号类型（0=活期,1=定期,2=通知,4=保证金户）,必输
							"isconcerted": "N"（协定）,
							"concertedmny": "********.88"（协定金额）,
							"fronzenstate": "0"（冻结状态 0=正常,1=冻结,2=部分冻结,3=销户）,必输
							"fronzenmny": "1000000.00"（冻结金额）,
							"frozendate": "2011-01-01 14:18:11"（冻结日期）,
							"defrozendate": "2011-02-02 14:18:11"（解冻日期）,
							"overdraftmny": "1500000.00"（透支额度）,
							"overdrafttype": "1"（透支控制方式 0=控制,1=提示,2=不控制）,必输
							"payarea": "0"（付款范围 0=不限制,1=全局内,2=集团内）,必输
							"istrade": "N"（交易账户 ）,
                            "isdefault":"Y"(默认)
						},
						{
							"pk_currtype": "GBP",
							"code": "11111-02",
							"name": "英磅户",
							"acctype": "0",
							"isconcerted": "N",
							"concertedmny": "100000.00",
							"fronzenstate": "1",
							"fronzenmny": "99999.99",
							"frozendate": "2011-01-02 14:18:11",
							"defrozendate": "2011-02-03 14:18:11",
							"overdraftmny": "66666.00",
							"overdrafttype": "0",
							"istrade": "Y"
						}
					]
				}
			}
		}
	}
}
```
#### 1.3.2请求示例
```
{
	"ufinterface": {
		"account": "YSGZ",
		"billtype": "bankaccbas",
		"isexchange": "Y",
		"groupcode": "A",
		"replace": "Y",
		"sender": "default",
		"bill": {
			"id":"bipbankaccid002",
			"billhead": {
				"accclass": "2",
				"pk_org": "biptestid1",
				"pk_group": "A",
				"accnum": "testnum001",
				"accname": "testname001",
				"code": "testnum001",
				"name": "testname001",
				"pk_bankdoc": "1001ZZ1000000003WUNB",
				"accopendate": "2019-04-13",
				"arapprop": "2",
				"netqueryflag": "2",
				"accattribute": "3",
				"genebranprop": "2",
				"groupaccount": "N",
				"accstate": "0",
				"accountproperty": "0",
				"financeorg": "biptestid1",
				"controlorg": "biptestid1",
				"enablestate": "2",
				"bankaccsub": {
					"item": [{
							"pk_currtype": "CNY",
							"code": "11111-01",
							"name": "人民币户1",
							"acctype": "0",
							"overdrafttype": "1",
                            "payarea":"0",
							"istrade": "N"
						}
					]
				}
			}
		}
	}
}
```
###  1.4返回示例

#### 1.4.1成功

```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "bankaccbas_1734339701589.xml",
            "billtype": "bankaccbas",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "bipbankaccid002",
                    "filename": "bankaccbas_1734339701589.xml",
                    "resultdescription": "单据  bipbankaccid002  开始处理...\r\n单据  bipbankaccid002  处理完毕!\r\n",
                    "resultcode": "1",
                    "content": "1001ZZ100000000EATYW"
                }
            ],
            "successful": "Y"
        }
    },
    "code": "**********",
    "message": null,
    "errorStack": null
}
```
#### 1.4.2失败
```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "bankaccbas_1734329448435.xml",
            "billtype": "bankaccbas",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "bipbankaccid001",
                    "filename": "bankaccbas_1734329448435.xml",
                    "resultdescription": "单据  bipbankaccid001  开始处理...\r\n单据  bipbankaccid001  处理错误:业务插件处理错误：插件类=nc.bs.bd.pfxx.plugin.BankaccbasPfxxPlugin,异常信息:下面字段引用的数据不存在：[银行类别],[开户银行]\r\n",
                    "resultcode": "32000",
                    "content": null
                }
            ],
            "successful": "N"
        }
    },
    "code": "**********",
    "message": null,
    "errorStack": null
}
```

#    2删除银行账户档案

###  2.1请求类型 
POST
###  2.2请求地址
 http://IP:port/nccloud/api/uapbd/bankmanage/bankaccbas/deleteBankaccbasByBipId

###  2.3请求参数说明
#### 2.3.1参数说明

```
{
  "bipid":"defdoc00001"（旗舰版银行账户id）,
  "version": "1"（固定值）
}
```
#### 2.3.2请求示例

```
{
  "bipid":"bipbankaccid001",
  "version": "1"
}
```


###  2.4返回示例

#### 2.4.1成功

```
{
    "success": true,
    "data": "true",
    "code": "**********",
    "message": null,
    "errorStack": null
}
```
#### 2.4.2失败

```
{
    "success": false,
    "data": null,
    "code": "**********",
    "message": "根据主键无法查询到对应的银行账户",
    "errorStack": "报错堆栈"
}
```
