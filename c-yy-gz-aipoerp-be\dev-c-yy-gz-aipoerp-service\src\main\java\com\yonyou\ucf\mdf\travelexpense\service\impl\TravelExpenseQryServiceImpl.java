package com.yonyou.ucf.mdf.travelexpense.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QueryJoin;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.travelexpense.service.ITravelExpenseQryService;
import com.yonyou.ypd.bill.basic.entity.convert.EntityConverter;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * 差旅费报销单查询接口
 *
 * <AUTHOR>
 *
 * 2025年4月14日
 */
@Service
public class TravelExpenseQryServiceImpl implements ITravelExpenseQryService {

    @Autowired
    private IBillQueryRepository billQryRepository;
    @Autowired
    private EntityConverter entityConverter;

    @SuppressWarnings("unchecked")
    @Override
    public List<Map<String, Object>> queryByDcostdateAndHandlepsn(Map<String, Set<String>> handlepsnMap) {
        if (MapUtils.isEmpty(handlepsnMap)) {
            return Collections.emptyList();
        }
        QuerySchema schema = QuerySchema.create();
        schema.distinct();
        schema.addSelect("id,code,vouchdate,status,bustype,creator");
        schema.addSelect(new QueryField("bustype", "bustype", null, "bd.bill.TransType/id"));
        // 关联子表（因为要用的子表字段做条件）
        schema.addJoin(new QueryJoin("expensebillbs", null, "left"));
        schema.addJoin(new QueryJoin("expinvoicedetails", null, "left"));
        // 关联孙表（因为要用的孙表字段做条件）
        schema.addJoin(new QueryJoin("expinvoicedetails.TravelExpInvoiceDetailVO_extend3List",
                ".TravelExpInvoiceDetailVO = expinvoicedetails.id", "left,alone"));
        // 设置查询条件
        List<QueryConditionGroup> conditions = Lists.newArrayList();
        for (String key : handlepsnMap.keySet()) {
            Set<String> values = handlepsnMap.get(key);
            QueryConditionGroup condGroup = QueryConditionGroup.and(
                    QueryCondition.name("expinvoicedetails.dcostdate").eq(key), // 子表接待日期
                    QueryConditionGroup.or(QueryCondition.name("expensebillbs.pk_handlepsn").in(values),
                            QueryCondition.name("expinvoicedetails.TravelExpInvoiceDetailVO_extend3List.extend3")
                                    .in(values))); // 子表陪同人员/报销人
            conditions.add(condGroup);
        }
        schema.addCondition(QueryConditionGroup.and(QueryCondition.name("bustype.code").eq("RBSM00601"),
                QueryConditionGroup.or(conditions.toArray(new QueryConditionGroup[0]))));

        // 指定子表（账单明细表）
        QuerySchema sonSchema1 = QuerySchema.create().name("expinvoicedetails").addSelect("*"); // 这里如果指定了具体字段，则孙表查不出来，具体不知道啥原因
        // 指定孙表（如果不指定孙表，也不会查出孙表）
        QuerySchema grandsonSchema = QuerySchema.create().name("TravelExpInvoiceDetailVO_extend3List")
                .addSelect(new QueryField("extend3", "extend3", null, "bd.staff.StaffNew/id"))
                .addSelect("extend3.code,extend3.name").addSelect("*");
        sonSchema1.addCompositionSchema(grandsonSchema);

        // 指定子表（报销明细）
        QuerySchema sonSchema2 = QuerySchema.create().name("expensebillbs").addSelect("*")
                .addSelect(new QueryField("pk_handlepsn", "pk_handlepsn", null, "bd.staff.Staff/id"))
                .addSelect("pk_handlepsn.name as psnName");
        // 设置子表
        schema.addCompositionSchema(sonSchema1);
        schema.addCompositionSchema(sonSchema2);

        // 执行查询
        List<Map<String, Object>> result = billQryRepository
                .queryMapBySchema("znbzbx.travelexpensebill.TravelExpenseBillVO", schema, "znbzbx");

        if (CollectionUtils.isEmpty(result)) {
            // 说明没有查询到
            return Collections.emptyList();
        }
        return result;
    }

    @Override
    public BizObject queryByid(String id) {
        Map<String, Object> result = billQryRepository.findMapById("znbzbx.travelexpensebill.TravelExpenseBillVO", id,
				0);
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return entityConverter.convertMapToBizObject(result);
    }

	@Override
	public List<Map<String, Object>> queryBySchema(QuerySchema schema) {
		if (schema == null) {
			return Collections.emptyList();
		}
		List<Map<String, Object>> result = billQryRepository
				.queryMapBySchema("znbzbx.travelexpensebill.TravelExpenseBillVO", schema, "znbzbx");
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyList();
		}
		return result;
	}

}
