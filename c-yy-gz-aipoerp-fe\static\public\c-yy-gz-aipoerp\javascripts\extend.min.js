/*!
 * MDF: 业务领域扩展逻辑
 *
 * author: 刘泽邦
 * time: Thu Jan 09 2025 11:46:07 GMT+0800 (中国标准时间)
 * branch: master
 * commit: 30f157401a418e5945205dfd7ddd20ae3d7a713d
 *
 * ꧁: 用友网络科技股份有限公司 Copyright © 1988-2025 :꧂
 *
 */!function(){var e,t,n,r,o,i,u={245:function(e,t,n){n(912)},912:function(e,t,n){"use strict";n.r(t)},633:function(e,t,n){"use strict";var r=new Error;e.exports=new Promise((function(e,t){if("undefined"!=typeof tns3nd)return e();n.l("/iuap-tns/ucf-wh/share/lib3nd/tns3nd.js",(function(n){if("undefined"!=typeof tns3nd)return e();var o=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;r.message="Loading script failed.\n("+o+": "+i+")",r.name="ScriptExternalLoadError",r.type=o,r.request=i,t(r)}),"tns3nd")})).then((function(){return tns3nd}))},905:function(e){"use strict";e.exports=React}},a={};function c(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={exports:{}};return u[e](n,n.exports,c),n.exports}c.m=u,c.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return c.d(t,{a:t}),t},t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},c.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var o=Object.create(null);c.r(o);var i={};e=e||[null,t({}),t([]),t(t)];for(var u=2&r&&n;"object"==typeof u&&!~e.indexOf(u);u=t(u))Object.getOwnPropertyNames(u).forEach((function(e){i[e]=function(){return n[e]}}));return i.default=function(){return n},c.d(o,i),o},c.d=function(e,t){for(var n in t)c.o(t,n)&&!c.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},c.f={},c.e=function(e){return Promise.all(Object.keys(c.f).reduce((function(t,n){return c.f[n](e,t),t}),[]))},c.u=function(e){return"./javascripts/extend."+e+"."+{398:"c6710f16",603:"b52fd227"}[e]+".min.js"},c.miniCssF=function(e){return"./stylesheets/extend."+e+".7546d5d8.min.css"},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n={},r="c-yy-gz-aipoerp:",c.l=function(e,t,o,i){if(n[e])n[e].push(t);else{var u,a;if(void 0!==o)for(var f=document.getElementsByTagName("script"),s=0;s<f.length;s++){var l=f[s];if(l.getAttribute("src")==e||l.getAttribute("data-webpack")==r+o){u=l;break}}u||(a=!0,(u=document.createElement("script")).charset="utf-8",u.timeout=120,c.nc&&u.setAttribute("nonce",c.nc),u.setAttribute("data-webpack",r+o),u.src=e),n[e]=[t];var p=function(t,r){u.onerror=u.onload=null,clearTimeout(d);var o=n[e];if(delete n[e],u.parentNode&&u.parentNode.removeChild(u),o&&o.forEach((function(e){return e(r)})),t)return t(r)},d=setTimeout(p.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=p.bind(null,u.onerror),u.onload=p.bind(null,u.onload),a&&document.head.appendChild(u)}},c.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o={603:[400]},i={400:["default","./immutable",633]},c.f.remotes=function(e,t){c.o(o,e)&&o[e].forEach((function(e){var n=c.R;n||(n=[]);var r=i[e];if(!(n.indexOf(r)>=0)){if(n.push(r),r.p)return t.push(r.p);var o=function(t){t||(t=new Error("Container missing")),"string"==typeof t.message&&(t.message+='\nwhile loading "'+r[1]+'" from '+r[2]),c.m[e]=function(){throw t},r.p=0},u=function(e,n,i,u,a,c){try{var f=e(n,i);if(!f||!f.then)return a(f,u,c);var s=f.then((function(e){return a(e,u)}),o);if(!c)return s;t.push(r.p=s)}catch(e){o(e)}},a=function(e,t,o){return u(t.get,r[1],n,0,f,o)},f=function(t){r.p=1,c.m[e]=function(e){e.exports=t()}};u(c,r[2],0,0,(function(e,t,n){return e?u(c.I,r[0],0,e,a,n):o()}),1)}}))},function(){c.S={};var e={},t={};c.I=function(n,r){r||(r=[]);var o=t[n];if(o||(o=t[n]={}),!(r.indexOf(o)>=0)){if(r.push(o),e[n])return e[n];c.o(c.S,n)||(c.S[n]={});c.S[n];var i=[];if("default"===n)!function(e){var t=function(e){"undefined"!=typeof console&&console.warn};try{var o=c(e);if(!o)return;var u=function(e){return e&&e.init&&e.init(c.S[n],r)};if(o.then)return i.push(o.then(u,t));var a=u(o);if(a&&a.then)return i.push(a.catch(t))}catch(e){t()}}(633);return i.length?e[n]=Promise.all(i).then((function(){return e[n]=1})):e[n]=1}}}(),function(){var e;c.g.importScripts&&(e=c.g.location+"");var t=c.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var r=n.length-1;r>-1&&(!e||!/^http(s?):/.test(e));)e=n[r--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),c.p=e+"../"}(),function(){if("undefined"!=typeof document){var e=function(e){return new Promise((function(t,n){var r=c.miniCssF(e),o=c.p+r;if(function(e,t){for(var n=document.getElementsByTagName("link"),r=0;r<n.length;r++){var o=(u=n[r]).getAttribute("data-href")||u.getAttribute("href");if("stylesheet"===u.rel&&(o===e||o===t))return u}var i=document.getElementsByTagName("style");for(r=0;r<i.length;r++){var u;if((o=(u=i[r]).getAttribute("data-href"))===e||o===t)return u}}(r,o))return t();!function(e,t,n,r,o){var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",c.nc&&(i.nonce=c.nc),i.onerror=i.onload=function(n){if(i.onerror=i.onload=null,"load"===n.type)r();else{var u=n&&n.type,a=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+u+": "+a+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=u,c.request=a,i.parentNode&&i.parentNode.removeChild(i),o(c)}},i.href=t,n?n.parentNode.insertBefore(i,n.nextSibling):document.head.appendChild(i)}(e,o,null,t,n)}))},t={411:0};c.f.miniCss=function(n,r){t[n]?r.push(t[n]):0!==t[n]&&{398:1}[n]&&r.push(t[n]=e(n).then((function(){t[n]=0}),(function(e){throw delete t[n],e})))}}}(),function(){var e={411:0};c.f.j=function(t,n){var r=c.o(e,t)?e[t]:void 0;if(0!==r)if(r)n.push(r[2]);else{var o=new Promise((function(n,o){r=e[t]=[n,o]}));n.push(r[2]=o);var i=c.p+c.u(t),u=new Error;c.l(i,(function(n){if(c.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var o=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;u.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",u.name="ChunkLoadError",u.type=o,u.request=i,r[1](u)}}),"chunk-"+t,t)}};var t=function(t,n){var r,o,i=n[0],u=n[1],a=n[2],f=0;if(i.some((function(t){return 0!==e[t]}))){for(r in u)c.o(u,r)&&(c.m[r]=u[r]);if(a)a(c)}for(t&&t(n);f<i.length;f++)o=i[f],c.o(e,o)&&e[o]&&e[o][0](),e[o]=0},n=self.webpackChunkc_yy_gz_aipoerp=self.webpackChunkc_yy_gz_aipoerp||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))}(),function(){"use strict";c(245);cb.extend.loadExtendResource("c-yy-gz-aipoerp",Promise.all([c.e(603),c.e(398)]).then(c.t.bind(c,398,23)))}()}(),'<TNS_BUILD_INFO>{"depMinVersion":"1.0.1"}</TNS_BUILD_INFO>'.toString();
//# sourceMappingURL=extend.min.js.map