package com.yonyou.ucf.mdf.aipo.controller.voucher;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.ext.controller.Authentication;
import com.yonyou.ucf.mdf.aipo.service.IAipoQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/rest")
@Slf4j
public class AipoQueryForeignController {

    @Autowired
    IAipoQueryService queryService;

    @RequestMapping("/query/queryBySql")
    @Authentication(false)
    public Object QueryForeignBySql(@RequestBody JSONObject jsonObject, HttpServletRequest request) throws Exception {
        log.debug("begin QueryForeignBySql");
        JSONObject params = new JSONObject();
        params.put("status", "200");
        params.put("title", "成功");
        try {
            Object rateBiz = queryService.queryForeignBySql(jsonObject);
            params.put("data", rateBiz);
        } catch (Exception e) {
            e.printStackTrace();
            params.put("status", "999");
            params.put("title", "失败");
            params.put("message", e.getMessage());
        }
        return params;
    }


}
