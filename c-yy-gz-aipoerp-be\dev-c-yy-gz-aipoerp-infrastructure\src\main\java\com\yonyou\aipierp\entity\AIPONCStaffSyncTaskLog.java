package com.yonyou.aipierp.entity;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.base.*;
import java.util.List;
import java.util.Date;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 艾珀高级版员工数据同步任务
 * @Date 2024-12-16 16:56:06
 * @since 2023/11/28
 **/
@YMSEntity(name = "AIPOERPCREATE.AIPOERPCREATE.AIPONCStaffSyncTaskLog", domain = "c-yy-gz-aipoerp")
public class AIPONCStaffSyncTaskLog extends SuperDO implements ILogicDelete {
    public static final String ENTITY_NAME = "AIPOERPCREATE.AIPOERPCREATE.AIPONCStaffSyncTaskLog";
    public static final String DATASYNCBEGINTIME = "dataSyncBeginTime";
    public static final String DATASYNCENDTIME = "dataSyncEndTime";
    public static final String TASKTYPE = "taskType";
    public static final String AIPONCSTAFFSYNCTASKLOGDETAILLIST = "AIPONCStaffSyncTaskLogDetailList";
    public static final String CREATETIME = "createTime";
    public static final String CREATOR = "creator";
    public static final String DR = "dr";
    public static final String ID = "id";
    public static final String MODIFIER = "modifier";
    public static final String MODIFYTIME = "modifyTime";
    public static final String PUBTS = "pubts";
    public static final String YTENANTID = "ytenantId";

    /* 员工数据同步开始时间 */
    private Date dataSyncBeginTime;
    /* 员工数据同步结束时间 */
    private Date dataSyncEndTime;
    /* 任务类型 */
    private String taskType;
    /* 艾珀高级版员工数据同步任务日志 */
    private List<AIPONCStaffSyncTaskLogDetail> AIPONCStaffSyncTaskLogDetailList;
    /* 创建时间 */
    private Date createTime;
    /* 创建人 */
    private String creator;
    /* 逻辑删除 */
    private Short dr;
    /* id */
    private String id;
    /* 修改人 */
    private String modifier;
    /* 修改时间 */
    private Date modifyTime;
    /* pubts */
    private Date pubts;
    /* 租户id */
    private String ytenantId;

    public void setDataSyncBeginTime(Date dataSyncBeginTime) {
        this.dataSyncBeginTime = dataSyncBeginTime;
    }

    public void setDataSyncEndTime(Date dataSyncEndTime) {
        this.dataSyncEndTime = dataSyncEndTime;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public void setAIPONCStaffSyncTaskLogDetailList(List<AIPONCStaffSyncTaskLogDetail> AIPONCStaffSyncTaskLogDetailList) {
        this.AIPONCStaffSyncTaskLogDetailList = AIPONCStaffSyncTaskLogDetailList;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setDr(Short dr) {
        this.dr = dr;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public void setPubts(Date pubts) {
        this.pubts = pubts;
    }

    public void setYtenantId(String ytenantId) {
        this.ytenantId = ytenantId;
    }

    public Date getDataSyncBeginTime() {
        return dataSyncBeginTime;
    }

    public Date getDataSyncEndTime() {
        return dataSyncEndTime;
    }

    public String getTaskType() {
        return taskType;
    }

    public List<AIPONCStaffSyncTaskLogDetail> getAIPONCStaffSyncTaskLogDetailList() {
        return AIPONCStaffSyncTaskLogDetailList;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getCreator() {
        return creator;
    }

    public Short getDr() {
        return dr;
    }

    public String getId() {
        return id;
    }

    public String getModifier() {
        return modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public Date getPubts() {
        return pubts;
    }

    public String getYtenantId() {
        return ytenantId;
    }

    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
