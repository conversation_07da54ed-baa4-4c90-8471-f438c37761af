package com.yonyou.ucf.mdf.aipo.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

@Data
public class MsgForwardDto {
    private String reqType;
    private String url;
    private JSONObject data;

    @Override
    public String toString() {
        return "MsgForwardDto{" +
                "reqType='" + reqType + '\'' +
                ", url='" + url + '\'' +
                ", data=" + data +
                '}';
    }
}
