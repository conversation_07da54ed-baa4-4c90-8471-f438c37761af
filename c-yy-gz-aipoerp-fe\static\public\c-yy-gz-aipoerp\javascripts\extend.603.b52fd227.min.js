(self.webpackChunkc_yy_gz_aipoerp=self.webpackChunkc_yy_gz_aipoerp||[]).push([[603],{246:function(e){cb.define("c-yy-gz-aipoerp",[],(function(){var t={doAction:function(e,t){this[e]&&this[e](t)},init:function(e){alert("1234, 啦啦啦啦啦啦"),e.get("button687ub").on("click",(function(){}))}};try{e.exports=t}catch(e){}return t}))},254:function(e){cb.define("c-yy-gz-aipoerp",(function(){var t={doAction:function(e,t){this[e]&&this[e](t)},init:function(e){alert("1234, 啦啦啦啦啦啦")}};try{e.exports=t}catch(e){}return t}))},356:function(e){cb.define("c-yy-gz-aipoerp",["common/common_VM.Extend.js"],(function(t){var n={doAction:function(e,t){this[e]&&this[e](t)},init:function(e){var n=!1,o={},a=e.get("details");"touch"===cb.rest.interMode&&"browse"!==e.getParams().mode&&(t.touchFormatter(a,e,"qty"),a.on("afterSetColumns",(function(){t.touchFormatter(a,e,"qty")}))),t.snformatter(a,e),t.elecBalanceWeigh(a,e,"qty"),t.initBarcodeModel(e),t.initSnCheckbox(e);var i=new cb.models.SimpleModel({needClear:!1});e.addProperty("inspection",i),i.on("filter",(function(e){l(e)}));var r=[],l=function(e){if(1==e){var t=a.getRows(),n=[];t.forEach((function(e){e.recqty!=e.qty&&n.push(e)})),a.setDataSource(n)}else a.setDataSource(r)},u=new cb.models.GridModel({columns:{barcode:{cItemName:"barcode",cShowCaption:"条码",bHidden:!1,bCanModify:!1,iColWidth:120,bShowIt:!0,cControlType:"Input"},skucode:{cItemName:"skucode",cShowCaption:"商品编码",bHidden:!1,bCanModify:!1,iColWidth:120,bShowIt:!0,cControlType:"Input"},skuname:{cItemName:"skuname",cShowCaption:"商品名称",bHidden:!1,bCanModify:!1,iColWidth:200,bShowIt:!0,cControlType:"Input"},errorcount:{cItemName:"errorcount",cShowCaption:"错误数量",bHidden:!1,bCanModify:!1,iColWidth:80,bShowIt:!0,cControlType:"Input"},reason:{cItemName:"reason",cShowCaption:"错误原因",bHidden:!1,bCanModify:!1,iColWidth:120,bShowIt:!0,cControlType:"Input"}},showCheckBox:!1,showRowNo:!1,showAggregates:!1,showColumnSetting:!1,bCanModify:!1,bIsNull:!0,pagination:!1});e.addProperty("error",u);e.get("barcode").on("enter",(function(t){var n=!1;e.get("sncheckbox")&&(n=e.get("sncheckbox").getValue());var i="productsku",l="batchno",u="producedate",s="invaliddate",c="unit",d=e.get("inwarehouse").getValue(),g=e.get("inwarehouse_iSerialManage").getValue(),b=t||e.get("barcode").getValue();if(b=b.trim(),e.getGridModels()){var m=function(e,t,n){n||(n=1);var o=a.getCellValue(e,t);return null==o||void 0===o?n:Number(o)+n};if(n){if(!function(e){return!!e.get("inwarehouse").getValue()||(cb.utils.alert("请先录入仓库!","warning"),!1)}(e))return void e.get("barcode").setValue(null);if(!g){var v=function(e,t){for(var n=e.getRows(),o=0;o<n.length;o++){var a=n[o].storeInDetailSNs;if(a)for(var i=0;i<a.length;i++)if(t==a[i].sn)return(null==n[o].qty?0:n[o].qty)+1>n[o].recqty?-2:(e.select(o),o)}return-1}(a,b);if(-1==v)f(b,pData,2);else if(-2==v)f(b,pData,3);else{var y=m(v,"qty",1);a.setCellValue(v,"qty",y,!0,!0)}return void e.get("barcode").setValue(null)}}var h=e.setProxy({settle:{url:"/bill/ref/getBarcodeResult.do",method:"POST"}}),p={isReturn:0,keyword:b,billnum:e.originalViewMeta.cBillNo,codeType:n?"snOnly":"",snWarehouse:d,iSerialManage:g};b&&a&&h.settle(p,(function(t,n){if(t)return cb.utils.alert(t.message,"error"),void e.get("barcode").setValue(null);if(void 0===n||"{}"==JSON.stringify(n))return f(b,null,1),void e.get("barcode").setValue(null);if("multirecord"==n.errtype)return f(b,null,4),void e.get("barcode").setValue(null);var d,g=n.codeType,v=n.data[0],y=v.isBatchManage,h=v.productskus[0].skuId;d=function(t,n,a,r,d){for(var f=-1,g=t.getRows(),m=g.length,v=0;v<m;v++)if("Delete"!=g[v]._status&&t.getCellValue(v,c)==r.oUnitId){if(r.isBatchManage)if(r.productskus[0]){if(r.productskus[0].batchno&&t.getCellValue(v,l)!=r.productskus[0].batchno)continue;if(r.isExpiryDateManage){if(r.productskus[0].producedate&&t.getCellValue(v,u)!=r.productskus[0].producedate)continue;if(r.productskus[0].invaliddate&&t.getCellValue(v,s)!=r.productskus[0].invaliddate)continue}}else{if(r.batchno&&t.getCellValue(v,l)!=r.batchno)continue;if(r.isExpiryDateManage){if(r.producedate&&t.getCellValue(v,u)!=r.producedate)continue;if(r.invaliddate&&t.getCellValue(v,s)!=r.invaliddate)continue}}var y=null==g[v].qty?0:g[v].qty,h=g[v].recqty,p=r.quantity;p||(p=1),y=Number(y)+Number(p);var C=cb.rest.AppContext.option.quantitydecimal;y=Number(y.toFixed(C)),h=Number(h.toFixed(C));var w=b;if("sn"==d){if(o[w])return cb.utils.alert("已扫描过该序列号["+w+"]"),-1;var V="storeInDetailSNs",S=g[v][V];if(S)for(var N=0;N<S.length;N++)if(w==S[N].sn){e.get(V).setCellValue(N,"sninspect",1,!0,!1),o[w]=!0;break}if(!o[w])continue}if(n==t.getCellValue(v,i)){if(a){if(y>h){o[w]=!1,f=-2;continue}f=v;break}if(null==t.getCellValue(v,l)){if(y>h){o[w]=!1,f=-2;continue}f=v;break}}}return f}(a,h,y,v,g),-1==d?f(b,v,2):-2==d?f(b,v,3):function(e,t,n,a,i){var l=m(t,"qty",a.quantity),u=m(t,"recqty",a.quantity),s=cb.rest.AppContext.option.quantitydecimal;if((l=Number(l.toFixed(s)))>=(u=Number(u.toFixed(s))))return f(n,a,3),void i.get("barcode").setValue(null);e.select(t),e.setCellValue(t,"qty",l,!1,!1);var c=i.get("storeInDetailSNs");if(c&&c.getRows())for(var d=c.getRows(),g=0;g<d.length;g++){var b=c.getRows()[g];o[b.sn]&&c.setCellValue(g,"sninspect",1,!0,!1)}for(var v=e.getCellValue(t,"srcBillRow"),y=0;y<r.length;y++)if(r[y].srcBillRow==v){r[y].qty=l;break}f(n,a,0),i.get("barcode").setValue(null)}(a,d,b,v,e),e.get("barcode").setValue(null)}))}}));var s=!1,c=[0,0,0],d=0,f=function(e,t,n){var o={},a=1;if(o.barcode=e,t?(o.skucode=t.cCode,o.skuname=t.cName,(a=t.quantity)||(a=1),o.errorcount=a):(o.skucode="",o.skuname="",o.errorcount=1),0==n)c[1]=c[1]+a,b(c),i.setState("productInfo",t.cName),g({type:"success",message:"匹配正确"});else{var r={};d++,1==n?(o.reason="商品不存在",r={type:"error",message:o.reason},i.setState("productInfo",o.barcode)):2==n?(o.reason="超入库范围",r={type:"warning",message:o.reason},i.setState("productInfo",t.cName)):3==n?(o.reason="超待入库数量",r={type:"warning",message:o.reason},i.setState("productInfo",t.cName)):4==n&&(o.reason="匹配到多个商品",r={type:"error",message:o.reason}),g(r),i.setState("errorCount",d),u.appendRow(o)}},g=function(e){i.setState("promptMessage",e)},b=function(e){0!==e[2]&&(e[0]=100*e[1]/e[2]),e[0]=Math.round(e[0]),e[1]=Math.round(100*e[1])/100,i.setState("progressData",e)};a.setState("orderField","rowno"),a.on("rowColChange",(function(e){if("batchno"==e.value.columnKey||"define1"==e.value.columnKey||"define2"==e.value.columnKey||"define3"==e.value.columnKey||"define4"==e.value.columnKey||"define5"==e.value.columnKey||"define6"==e.value.columnKey||"define7"==e.value.columnKey||"define8"==e.value.columnKey||"define9"==e.value.columnKey||"define10"==e.value.columnKey||"define11"==e.value.columnKey||"define12"==e.value.columnKey||"define13"==e.value.columnKey||"define14"==e.value.columnKey||"define15"==e.value.columnKey||"define16"==e.value.columnKey||"define17"==e.value.columnKey||"define18"==e.value.columnKey||"define19"==e.value.columnKey||"define20"==e.value.columnKey||"define21"==e.value.columnKey||"define22"==e.value.columnKey||"define23"==e.value.columnKey||"define24"==e.value.columnKey||"define25"==e.value.columnKey||"define26"==e.value.columnKey||"define27"==e.value.columnKey||"define28"==e.value.columnKey||"define29"==e.value.columnKey||"define30"==e.value.columnKey)return!1;var n=e.value.columnKey,o=e.value.rowIndex,i=a.getCellValue(o,"unitExchangeType");return"stockUnit_code"==n||"stockUnit_name"==n?(t.setInvExchRateState(a,o),0==i):"invExchRate"==n?(t.setInvExchRateState(a,o),0!=i):void 0})),a&&(a.on("afterSetColumns",(function(n){t.fieldVisble(e.get("srcBillType").getValue(),a),t.snformatter(a,e)})),a.on("beforeInsertRow",(function(e){if(this.getParent().get("srcBillNO")&&this.getParent().get("srcBillNO").getValue())return!1})),a.on("beforeCellValueChange",(function(e){switch(e.cellName){case"qty":case"subQty":case"contactsQuantity":case"contactsPieces":if(cb.utils.isEmpty(e.value))return;if(e.value<=0||"-"==e.value)return cb.utils.alert("["+a.getColumn(e.cellName).cShowCaption+"]只能录入大于0的值，请检查后重试！"),!1}}))),a.on("afterCellValueChange",(function(e){switch(e.cellName){case"qty":if(0==s)return;var t=e.rowIndex,n=a.getCellValue(t,"recqty"),o=e.value;if(!isNaN(o)&&Number(o)>=0&&Number(o)<=Number(n)){c[1]=c[1]+Number(o)-Number(e.oldValue),b(c);for(var i=a.getCellValue(t,"srcBillRow"),l=0;l<r.length;l++)if(r[l].srcBillRow==i){r[l].qty=e.value;break}return}a.setCellValue(t,"qty",e.oldValue);break;case"natUnitPrice":case"natMoney":"natUnitPrice"!=e.cellName&&"natMoney"!=e.cellName||null!=a.getCellValue(e.rowIndex,"autoCalcCost")&&a.setCellValue(e.rowIndex,"autoCalcCost","false")}}));a.on("afterSetDataSource",(function(){!function(){if(s){var t=e.get("details"),n=t.getCache("actions"),o=[];t.getRows().forEach((function(e){var t={};n.forEach((function(e){"btnDeleteRow"==e.cItemName&&t.btnDeleteRow&&(t.btnDeleteRow={visible:!1})})),o.push(t)})),t.setActionsState(o)}}()})),e.on("checkRow",(function(e){1!=s?m():cb.utils.alert("当前已处于验货状态!")}));var m=function(){s=!0,i&&i.setVisible(!0),e.get("btnCheckRow").setValue("验货中"),e.get("btnCheckRow").setState("className","btn-inspecting"),e.get("barcode")&&e.get("barcode").setVisible(!0),e.get("sncheckbox")&&e.get("sncheckbox").setVisible(!0),i.setState("errorGridModel",u),i.setState("productInfo","商品名称"),g({type:"success",message:""}),i.setState("errorCount",0);var t=a.getRows();r=t.slice();var n=[];t.forEach((function(e){e.recqty>0&&(e.qty=null,n.push(e),c[2]+=e.recqty)})),a.setDataSource(n),b(c)};e.on("afterEdit",(function(){t.hiddenFiSearchButton(e),t.hiddenFiSearchFields(e,a),e.get("btnAddRow")&&e.get("btnAddRow").setDisabled(!0),e.get("btnCheckRow").setDisabled(!0)})),e.on("relating",(function(){var t={billtype:"voucher",billno:"st_storeout",params:{mode:"edit",readOnly:!0,id:e.get("srcBill").getValue()}};cb.loader.runCommandLine("bill",t,e)})),e.on("afterLoadData",(function(n){t.fieldVisble(n.srcBillType,e.get("details")),t.showSnVisible(e,a),e.get("btnAddRow")&&e.get("btnAddRow").setDisabled(!0),e.get("btnCheckRow").setDisabled(!0),c=[0,0,0],s=!1,e.get("barcode")&&e.get("barcode").setVisible(!1),e.get("sncheckbox")&&e.get("sncheckbox").setVisible(!1),o={};var i=a.getRows(),r=[];i.forEach((function(e){var t=[];if(e.contactsQuantity>0){var n=e.storeInDetailSNs;if(n){for(var o=0;o<n.length;o++)0==n[o].binspect&&t.push(n[o]);e.storeInDetailSNs=t}r.push(e)}}));var l=e.getParams().mode;e.get("btnCheckRow")&&("add"===l?(e.get("btnCheckRow").setVisible(!1),e.get("btnCheckRow").setDisabled(!1)):e.get("btnCheckRow").setVisible(!1)),e.get("warehouse_isGoodsPosition").getValue()?e.get("details").setColumnState("goodsposition_cName","bCanModify",!0):e.get("details").setColumnState("goodsposition_cName","bCanModify",!1)})),e.on("modeChange",(function(o){t.bsnTabShow(o,e,"st_storein_body_page_sn","st_storein_head_page",n)})),e.on("showsn",(function(o){n=!n,t.bsnTabShow(o,e,"st_storein_body_page_sn","st_storein_head_page",n)||(n=!n)})),e.get("details").on("beforeBrowse",(function(n){if("goodsposition_cName"===n.cellName&&!e.get("inwarehouse").getValue())return cb.utils.alert("请先选择仓库！","warning"),!1;if(n.cellName&&("stockUnit_code"==n.cellName||"stockUnit_name"==n.cellName)){var o=a.getRows()[n.rowIndex];if(o.product&&o.product>0){var i={productId:o.product,tenant_id:cb.rest.AppContext.tenant.id};n.context.setCondition(i)}}n.cellName&&"batchno"==n.cellName&&t.checkBeforeaddBatchno(e,e.get("details"),n)})),e.get("inwarehouse_name").on("afterValueChange",(function(t){e.get("details").setColumnValue("goodsposition",null),e.get("details").setColumnValue("goodsposition_cName",null),e.get("warehouse_isGoodsPosition").getValue()?e.get("details").setColumnState("goodsposition_cName","bCanModify",!0):e.get("details").setColumnState("goodsposition_cName","bCanModify",!1)})),e.on("beforeSave",(function(n){var o=e.get("outorg").getValue(),i=e.get("outwarehouse").getValue(),r=e.get("inorg").getValue();if(i==e.get("inwarehouse").getValue()&&o==r)return cb.utils.alert("出库仓库和入库仓库不可以相同！"),!1;var l=a.getRows();if(0==s)for(var u=0;u<l.length;u++){var d=l[u];if(isNaN(d.qty)||null==d.qty)return cb.utils.alert("入库数量不能为空!","error"),!1}if(e.get("warehouse_isGoodsPosition").getValue())for(var f=0;f<l.length;f++){if(!l[f].goodsposition_cName)return cb.utils.alert("启用货位管理的仓库行的货位不能为空,请检查!","error"),!1}var g=e.get("inwarehouse_iSerialManage").getValue();if(cb.rest.AppContext.option.serialManage&&g){var b=t.checkSnBeforeSave(n,"details","storeInDetailSNs","qty",e);if(1==b)return!1;if(2==b)return cb.utils.alert("商品行入库数量与序列号数量不一致，请检查！","warning"),!1}var m=new cb.promise,v=n;return s&&100!=c[0]?(cb.utils.confirm("待入库数量和入库数量匹配有差异,请确认是否终止验货并保存?",(function(){var e=JSON.parse(v.data.data),t=[];l.forEach((function(e){!isNaN(e.qty)&&Number(e.qty)>0&&t.push(e)})),e.details=t,v.data.data=JSON.stringify(e),s=!1,m.resolve()}),(function(){m.reject()})),m):void 0}));var v=e.getParams(),y=v&&v.mode||env.VOUCHER_STATE_BROWSE;"browse"===y&&(e.get("btnSave")&&e.get("btnSave").setVisible(!1),e.get("btnAbandon")&&e.get("btnAbandon").setVisible(!1)),"add"===y&&(e.get("btnMoveprev")&&e.get("btnMoveprev").setVisible(!1),e.get("btnMovenext")&&e.get("btnMovenext").setVisible(!1))}};try{e.exports=n}catch(e){}return n}))},515:function(e){cb.define("c-yy-gz-aipoerp",(function(){var t={};try{e.exports=t}catch(e){}return t}))},796:function(e,t){"use strict";var n={editplus:function(e,t,n,o,a){o(n,(function(){a({})}))}};t.A=n},381:function(e,t,n){"use strict";var o=n(400),a=n.n(o)().fromJS({demoValue1:{},demoValue2:!0});t.A=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a,t=arguments.length>1?arguments[1]:void 0;return"PLATFORM_DATA_DEMO_VALUE"===t.type?e.merge(t.payload):e}},626:function(e){e.exports={enus:{"mdf-title":"mdd driver"},zhcn:{"mdf-title":"模型驱动"}}},635:function(e,t,n){var o={"./GT40884AT9/GT40884AT9_9a33f420List_VM.Extend":246,"./GT40884AT9/GT40884AT9_9a33f420List_VM.Extend.js":246,"./GT40884AT9/GT40884AT9_9a33f420_VM.Extend":254,"./GT40884AT9/GT40884AT9_9a33f420_VM.Extend.js":254,"./ST/ST_st_storein_VM.Extend":356,"./ST/ST_st_storein_VM.Extend.js":356,"./common/common_VM.Extend":515,"./common/common_VM.Extend.js":515};function a(e){var t=i(e);return n(t)}function i(e){if(!n.o(o,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return o[e]}a.keys=function(){return Object.keys(o)},a.resolve=i,e.exports=a,a.id=635}}]);
//# sourceMappingURL=extend.603.b52fd227.min.js.map