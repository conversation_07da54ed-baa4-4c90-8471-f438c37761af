{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.83\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.83\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "E9E15655C095BBF5D81B23FEDD64FFD4AF188E874DBE73C15A4C2A5BE4E09B90"}, "default_search_provider_data": {"template_url_data": "6BDFF176F21311C1CB6521D4E6203D40AFE1F3530BB10958B5F2CEABD4947996"}, "edge": {"services": {"account_id": "EEC4CD56BE05F22110891E705FA3A74A2888123C0099F942A6C151088F992196", "last_username": "1CB8165B95A58F3B894056A424E8E6000C5FC99FD4FF3E62A993DD6B67564D5A"}}, "enterprise_signin": {"policy_recovery_token": "0153BB782A42DB7AEE8A598ABD9F5141A044211B99D7F989A70D6252D9E20F7D"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "29C585DE974CA1B108C2C74B879FF63CC1B995B2893615B55F4B52EF365D5D96", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "C93F72F190424524943DD0B274DB22CE35CABC70C7AE1640B8EFC4370C6F0B1C"}, "ui": {"developer_mode": "48505B7CA9B0332654FE208234AA526987F653F1E9597ED0ABC3729CE63FB261"}}, "google": {"services": {"last_signed_in_username": "DFC68EC8617689D2F61215BC43B81BD176952F1684024D43796E188D02C018C8"}}, "homepage": "C863DB5503C7E8B69E8BE1E6AF116DB99FE9DBE4B5E527D1C6AC57832DE9A3E2", "homepage_is_newtabpage": "808C6A092A50436C2841264F4D9B8E14D0B3F72BB9D33FA61E8F3501CAB37B81", "media": {"cdm": {"origin_data": "6EE8A891E9F7E91FE438502DAF2407C3BEDE6AC1AD1E84F3B995851C519F5E46"}, "storage_id_salt": "68A6D228CDE4751F0DA80C5086AB20C3269E71605DB176D74B0AE78C59F748D9"}, "pinned_tabs": "356E705D7A5180FB19705CBAC56090904538ED1F7D5F813BEC6977DFCCCD539B", "prefs": {"preference_reset_time": "F41F26718039C4A6E001D0BDC6EF368A628328745E6F26E100E4591BCC6A24B5"}, "safebrowsing": {"incidents_sent": "ADE17EA20C5049838F7B6AF36711E182220E6A065F7A5E13D72B17E0ECDD487F"}, "search_provider_overrides": "5F967245289EAF0E9833F3C2BE7A93B116DC558C57EA8BEAA54E9A28B756EBF4", "session": {"restore_on_startup": "B739990A2DFBFA168573BF5016C65E93DE6925F6BD266453E939C481B80F6792", "startup_urls": "DBD4FE588A671D795BE5E4FC519693E01325051A19FA5AAAC58A419CA9F83A03"}}, "super_mac": "4012E8A8ADE11593471810F579E7AAA6AACDF07693755C83772BD85074BF1109"}}