package com.yonyou.ucf.mdf.product.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/5 15:28
 * @DESCRIPTION 物料服务接口
 */
public interface ProductService {
    /**
     * 根据物料ID查询
     * 实物物料属性 realProductAttributeType 为 设备(4)
     * 或 价值管理模式 detail.valueManageType 为 固定资产(1) 的数据
     *
     * @param id id
     * @return list
     */
    List<Map<String, Object>> getProductListById(Object id);

    /**
     * 获取 实物物料属性 realProductAttributeType 为 设备(4)
     * 或者 价值管理模式 detail.valueManageType 为 固定资产(1)
     * 且 detail.stopstatus=false
     * @return list
     */
    List<Map<String, Object>> getEnableProductList();
}
