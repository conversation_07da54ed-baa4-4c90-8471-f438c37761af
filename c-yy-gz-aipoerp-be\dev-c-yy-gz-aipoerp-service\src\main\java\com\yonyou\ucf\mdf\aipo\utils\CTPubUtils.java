package com.yonyou.ucf.mdf.aipo.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.yonyou.ucf.mdf.aipo.utils.base.UFDouble;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 工具类
 * */
public class CTPubUtils {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("[0-9.-]*");
    /**
     * 数学计算操作符
     */
    public final static String S_MATH_OPER_ADD = "+";
    public final static String S_MATH_OPER_SUB = "-";
    public final static String S_MATH_OPER_MULTIPLY = "*";
    public final static String S_MATH_OPER_DIV = "/";
    public final static Pattern DATE_PATTERN = Pattern.compile("([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])");


    /**
     * 检查传入的参数是否为空。
     *
     * @return boolean 如果被检查值为null，返回true。
     * 如果value的类型为String，并且value.length()为0，返回true。
     * 如果value的类型为Object[]，并且value.length为0，返回true。
     * 如果value的类型为Collection，并且value.size()为0，返回true。
     * 如果value的类型为Dictionary，并且value.size()为0，返回true。
     * 如果value的类型为Map，并且value.size()为0，返回true。
     * 否则返回false。
     * <AUTHOR> 2009-9-3
     * @param value
     *            被检查值。
     */
    public static boolean isEmpty(Object value) {
        if (value == null){
            return true;
        }
        if (value instanceof String || value instanceof StringBuilder){
            return org.apache.commons.lang3.StringUtils.isBlank(String.valueOf(value));
        }
        if ((value instanceof Object[]) && (((Object[]) value).length <= 0)) {
            return true;
        }
        if ((value instanceof ArrayNode) && (((ArrayNode) value).size() <= 0)) {
            return true;
        }
        if (value instanceof Object[]) {
            Object[] t = (Object[]) value;
            for (Object o : t) {
                if (o != null) {
                    return false;
                }
            }
            return true;
        }
        if ((value instanceof Collection) && ((Collection) value).size() <= 0){
            return true;
        }
        if ((value instanceof Dictionary) && ((Dictionary) value).size() <= 0){
            return true;
        }
        if(value instanceof JsonNode && ((JsonNode) value).isNull()){
            return true;
        }
        return (value instanceof Map) && ((Map) value).size() <= 0;
    }

    public static boolean isNotEmpty(Object value) {
        return !isEmpty(value);
    }

    /**
     * 判定两个字符串，是否相等
     */
    public static boolean isEqual(String str1,String str2){
        if(str1==null && str2==null) {
            return true;
        }
        if(str1!=null) {
            return str1.equals(str2);
        } else {
            return false;
        }
    }
    public static boolean isNotEqual(String str1,String str2){
        return !isEqual(str1, str2);
    }

    public static <T> T convert(Object obj, Class<T> type) {
        if (obj != null && !CTPubUtils.isEmpty(obj.toString())) {
            if (type.equals(Integer.class) || type.equals(int.class)) {
                return (T) new Integer(obj.toString());
            } else if (type.equals(Long.class) || type.equals(long.class)) {
                return (T) new Long(obj.toString());
            } else if (type.equals(Boolean.class) || type.equals(boolean.class)) {
                return (T) new Boolean(obj.toString());
            } else if (type.equals(Short.class) || type.equals(short.class)) {
                return (T) new Short(obj.toString());
            } else if (type.equals(Float.class) || type.equals(float.class)) {
                return (T) new Float(obj.toString());
            } else if (type.equals(Double.class) || type.equals(double.class)) {
                return (T) new Double(obj.toString());
            } else if (type.equals(Byte.class) || type.equals(byte.class)) {
                return (T) new Byte(obj.toString());
            } else if (type.equals(Character.class) || type.equals(char.class)) {
                return (T) new Character(obj.toString().charAt(0));
            } else if (type.equals(String.class)) {
                if (obj instanceof Long) {
                    return (T) obj.toString();
                }
                return (T) obj;
            } else if (type.equals(BigDecimal.class)) {
                return (T) new BigDecimal(obj.toString());
            } else if (type.equals(LocalDateTime.class)) {
                // DateTimeFormatter formatter =
                // DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return (T) LocalDateTime.parse(obj.toString());
            } else if (type.equals(Date.class)) {
                try {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                    return (T) formatter.parse(obj.toString());
                } catch (ParseException e) {
                    throw new RuntimeException(e.getMessage());
                }
            } else {
                return null;
            }
        } else {
            if (type.equals(int.class)) {
                return (T) new Integer(0);
            } else if (type.equals(long.class)) {
                return (T) new Long(0L);
            } else if (type.equals(boolean.class)) {
                return (T) new Boolean(false);
            } else if (type.equals(short.class)) {
                return (T) new Short("0");
            } else if (type.equals(float.class)) {
                return (T) new Float(0.0);
            } else if (type.equals(double.class)) {
                return (T) new Double(0.0);
            } else if (type.equals(byte.class)) {
                return (T) new Byte("0");
            } else if (type.equals(char.class)) {
                return (T) new Character('\u0000');
            } else {
                return null;
            }
        }
    }

    public static String getCurrTimeString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 设置时区为 GMT+8
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        // 获取当前时间
        String currTime = sdf.format(new Date());
        return currTime;
    }



    /**
     * 安全加法计算
     */
    public static <T extends Number> T safeAdd(T d1, T d2) {
        return compute(d1, S_MATH_OPER_ADD, d2);
    }
    /**
     * 通用安全计算
     *
     * @param d1   数值1
     * @param oper 数学计算操作符
     * @param d2   数值2
     * @return
     */
    public static <T extends Number> T compute(T d1, String oper, T d2) {
        if (d1 == null && d2 == null) {
            return (T) BigDecimal.ZERO;
        }

        UFDouble ufd1 = parseUFDouble(d1);
        UFDouble ufd2 = parseUFDouble(d2);

        UFDouble result = null;
        // 计算
        if (S_MATH_OPER_ADD.equals(oper)) {
            result = ufd1.add(ufd2);
        } else if (S_MATH_OPER_SUB.equals(oper)) {
            result = ufd1.sub(ufd2);
        } else if (S_MATH_OPER_MULTIPLY.equals(oper)) {
            result = ufd1.multiply(ufd2);
        } else if (S_MATH_OPER_DIV.equals(oper)) {
            result = ufd1.div(ufd2);
        }
        // 转换返回值
        T typeTmp = d1 == null ? d2 : d1;
        if (typeTmp == null) {
            return (T) BigDecimal.ZERO;
        } else if (typeTmp instanceof BigDecimal) {
            return (T) result.toBigDecimal();
        } else if (typeTmp instanceof Double) {
            return (T) result.toDouble();
        } else if (typeTmp instanceof Long) {
            return (T) (Long) result.longValue();
        } else if (typeTmp instanceof Integer) {
            return (T) (Integer) result.intValue();
        }
        return (T) result.toDouble();
    }
    /**
     * 将对象转为 UFDouble
     */
    public static UFDouble parseUFDouble(Object obj) {
        if (obj == null) {
            return UFDouble.ZERO_DBL;
        }
        if (obj instanceof BigDecimal) {
            return new UFDouble(obj);
        }
        if (obj instanceof Double) {
            return new UFDouble((Double) obj);
        }
        if (obj instanceof Long || obj instanceof Integer) {
            return new UFDouble(obj);
        }
        if (obj instanceof String) {
            return new UFDouble(obj.toString());
        }
        return (UFDouble) obj;
    }

    public static String getGeneralDataString(Date date){
        if (CTPubUtils.isEmpty(date)) {
            return "";
        }
        return CTPubUtils.convert(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date),String.class);
    }

    public static String getCurrDataString(){
        Date currentDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(currentDate);
    }



}
