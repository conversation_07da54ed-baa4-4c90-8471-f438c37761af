package com.yonyou.ucf.mdf.rbsm.model;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 人力支付财务配置
 * @Date 2025-04-23 11:29:04
 * @since 2023/11/28
 **/
@YMSEntity(name = "PAY_ORG_CFG.PAY_ORG_CFG.PayOrgConfig", domain = "c-yy-gz-aipoerp")
public class PayOrgConfig extends SuperDO {
	public static final String ENTITY_NAME = "PAY_ORG_CFG.PAY_ORG_CFG.PayOrgConfig";
	public static final String PAYTYPE = "payType";
	public static final String PAYORG = "payorg";
	public static final String CFINACEORG = "cfinaceorg";
	public static final String CFINACEDEPT = "cfinacedept";
	public static final String ENTERPRISEBANKACCT = "enterprisebankacct";
	public static final String SUPPLIER = "supplier";
	public static final String EXPLAIN = "explain";
	public static final String PKBALATYPE = "pkBalatype";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 支付类型 */
	private String payType;
	/* 缴交机构 */
	private String payorg;
	/* 财务组织 */
	private String cfinaceorg;
	/* 财务部门 */
	private String cfinacedept;
	/* 企业银行账户 */
	private String enterprisebankacct;
	/* 收款客户 */
	private Long supplier;
	/* 摘要 */
	private String explain;
	/* 结算方式 */
	private Long pkBalatype;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public void setPayorg(String payorg) {
		this.payorg = payorg;
	}

	public void setCfinaceorg(String cfinaceorg) {
		this.cfinaceorg = cfinaceorg;
	}

	public void setCfinacedept(String cfinacedept) {
		this.cfinacedept = cfinacedept;
	}

	public void setEnterprisebankacct(String enterprisebankacct) {
		this.enterprisebankacct = enterprisebankacct;
	}

	public void setSupplier(Long supplier) {
		this.supplier = supplier;
	}

	public void setExplain(String explain) {
		this.explain = explain;
	}

	public void setPkBalatype(Long pkBalatype) {
		this.pkBalatype = pkBalatype;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getPayType() {
		return payType;
	}

	public String getPayorg() {
		return payorg;
	}

	public String getCfinaceorg() {
		return cfinaceorg;
	}

	public String getCfinacedept() {
		return cfinacedept;
	}

	public String getEnterprisebankacct() {
		return enterprisebankacct;
	}

	public Long getSupplier() {
		return supplier;
	}

	public String getExplain() {
		return explain;
	}

	public Long getPkBalatype() {
		return pkBalatype;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
