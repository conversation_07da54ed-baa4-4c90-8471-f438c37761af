sysid=diwork
ignorePattern=.*\\.js,.*\\.css,.*\\.jsx,/rest/.*,/style/.*,/index\\.html,/controller/register/.*,/CloudRemoteCall/*
yht.user.base.url=@yht.user.base.url@
apps.tenant.base.url=@apps.tenant.base.url@
app.base.url=@app.base.url@
cas.url=@cas.url@
yht.client.credential.path=authfile.properties
client.credential.path=authfile.properties
httpclient.MaxTotal=100
httpclient.MaxPerRoute=100
issimpleregister=0;
rest.algorithm=HmacSHA1
UAP.DigitalSignatureAlgorithm=SHA1withRSA
UAP.DigitalSignatureKeyLength=1024
UAP.RandomAlgorithm=SHA1PRNG
UAP.KDF.PRF=HmacSHA1
UAP.AUTH.ALG=HMAC
OSP.SDK.VERSION=0.0.6
yht.enterprise.base.url=@yht.enterprise.base.url@
client_id=@yht.sdk.client_id@
client_secret=@yht.sdk.client_secret@
scope=7
