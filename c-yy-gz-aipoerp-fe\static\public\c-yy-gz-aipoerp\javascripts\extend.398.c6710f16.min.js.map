{"version": 3, "file": "./javascripts/extend.398.c6710f16.min.js", "mappings": "spDAEqB,IAEAA,EAAI,SAAAC,GAAA,SAAAD,IAAA,O,4FAAAE,CAAA,KAAAF,GAAAG,EAAA,KAAAH,EAAAI,UAAA,Q,qRAAAC,CAAAL,EAAAC,G,EAAAD,G,EAAA,EAAAM,IAAA,SAAAC,MACvB,WACE,OACEC,IAAAA,cAAA,WAAK,gBAET,M,yFAAC,CALsB,CAASC,EAAAA,U,yJCQ5BC,G,OAAe,CACjBC,MAAAA,EACAC,UAAAA,EACAC,KAAAA,EACAC,KAAAA,EACAC,MAAAA,EACAC,QAAAA,EACAC,OAAAA,IACAC,QAAAA,IAEJ,K,0GCrBA,IAAMC,EAAkBC,EAAAA,KACxBC,GAAGC,OAAOC,gBAAgBC,kBAA2BL,GAKnD,IAAMM,EAAmBL,EAAAA,KAAAA,EACzBC,GAAGC,OAAOI,mBAAmBF,kBAA2BC,GAI1D,IAAME,EAAiBP,EAAAA,KAAAA,EACvBC,GAAGC,OAAOM,iBAAiBJ,kBAA2BG,GAGtD,IAAME,EAAeT,EAAAA,KAAAA,EACrBC,GAAGC,OAAOQ,eAAeN,kBAA2BK,GAGpD,IAAME,EAAkBX,EAAAA,KAAAA,EACxBC,GAAGC,OAAOU,kBAAkBR,kBAA2BO,GAGvDV,GAAGC,OAAOW,YAAYT,kBAA2B,CAC/CU,WAAY,UAIdb,GAAGc,KAAKC,aAAaZ,kBAA2BJ,EAAQ,KAAe,G,gDC1BvE,KAAiBpB,KAAAA,EAAAA,E,gDCCjB,KACE,CAAEqC,OAAO,EAAMC,KAAM,QAASC,UAAWvC,EAAAA,M,+BCH3C,IAAIwC,EAAoB,SAA2BjC,GAClD,OAID,SAAyBA,GACxB,QAASA,GAA0B,iBAAVA,CAC1B,CANQkC,CAAgBlC,KAQxB,SAAmBA,GAClB,IAAImC,EAAcC,OAAOC,UAAUC,SAASC,KAAKvC,GAEjD,MAAuB,oBAAhBmC,GACa,kBAAhBA,GAQL,SAAwBnC,GACvB,OAAOA,EAAMwC,WAAaC,CAC3B,CATKC,CAAe1C,EACpB,CAbM2C,CAAU3C,EAChB,EAeA,IACIyC,EADiC,mBAAXG,QAAyBA,OAAOC,IAClBD,OAAOC,IAAI,iBAAmB,MAUtE,SAASC,EAA8B9C,EAAO+C,GAC7C,OAA0B,IAAlBA,EAAQC,OAAmBD,EAAQd,kBAAkBjC,GAC1DiD,GANiBC,EAMKlD,EALlBmD,MAAMC,QAAQF,GAAO,GAAK,CAAC,GAKDlD,EAAO+C,GACrC/C,EAPJ,IAAqBkD,CAQrB,CAEA,SAASG,EAAkBC,EAAQC,EAAQR,GAC1C,OAAOO,EAAOE,OAAOD,GAAQE,KAAI,SAASC,GACzC,OAAOZ,EAA8BY,EAASX,EAC/C,GACD,CAkBA,SAASY,EAAQL,GAChB,OAAOlB,OAAOwB,KAAKN,GAAQE,OAT5B,SAAyCF,GACxC,OAAOlB,OAAOyB,sBACXzB,OAAOyB,sBAAsBP,GAAQQ,QAAO,SAASC,GACtD,OAAO3B,OAAO4B,qBAAqBzB,KAAKe,EAAQS,EACjD,IACE,EACJ,CAGmCE,CAAgCX,GACnE,CAEA,SAASY,EAAmBC,EAAQC,GACnC,IACC,OAAOA,KAAYD,CACpB,CAAE,MAAME,GACP,OAAO,CACR,CACD,CASA,SAASC,EAAYhB,EAAQC,EAAQR,GACpC,IAAIwB,EAAc,CAAC,EAiBnB,OAhBIxB,EAAQd,kBAAkBqB,IAC7BK,EAAQL,GAAQkB,SAAQ,SAASzE,GAChCwE,EAAYxE,GAAO+C,EAA8BQ,EAAOvD,GAAMgD,EAC/D,IAEDY,EAAQJ,GAAQiB,SAAQ,SAASzE,IAblC,SAA0BuD,EAAQvD,GACjC,OAAOmE,EAAmBZ,EAAQvD,MAC5BqC,OAAOqC,eAAelC,KAAKe,EAAQvD,IACpCqC,OAAO4B,qBAAqBzB,KAAKe,EAAQvD,GAC/C,EAUM2E,CAAiBpB,EAAQvD,KAIzBmE,EAAmBZ,EAAQvD,IAAQgD,EAAQd,kBAAkBsB,EAAOxD,IACvEwE,EAAYxE,GAhDf,SAA0BA,EAAKgD,GAC9B,IAAKA,EAAQ4B,YACZ,OAAO1B,EAER,IAAI0B,EAAc5B,EAAQ4B,YAAY5E,GACtC,MAA8B,mBAAhB4E,EAA6BA,EAAc1B,CAC1D,CA0CsB2B,CAAiB7E,EAAKgD,EAAtB6B,CAA+BtB,EAAOvD,GAAMwD,EAAOxD,GAAMgD,GAE5EwB,EAAYxE,GAAO+C,EAA8BS,EAAOxD,GAAMgD,GAEhE,IACOwB,CACR,CAEA,SAAStB,EAAUK,EAAQC,EAAQR,IAClCA,EAAUA,GAAW,CAAC,GACd8B,WAAa9B,EAAQ8B,YAAcxB,EAC3CN,EAAQd,kBAAoBc,EAAQd,mBAAqBA,EAGzDc,EAAQD,8BAAgCA,EAExC,IAAIgC,EAAgB3B,MAAMC,QAAQG,GAIlC,OAFgCuB,IADZ3B,MAAMC,QAAQE,GAKvBwB,EACH/B,EAAQ8B,WAAWvB,EAAQC,EAAQR,GAEnCuB,EAAYhB,EAAQC,EAAQR,GAJ5BD,EAA8BS,EAAQR,EAM/C,CAEAE,EAAU8B,IAAM,SAAsBC,EAAOjC,GAC5C,IAAKI,MAAMC,QAAQ4B,GAClB,MAAM,IAAIC,MAAM,qCAGjB,OAAOD,EAAME,QAAO,SAASC,EAAMC,GAClC,OAAOnC,EAAUkC,EAAMC,EAAMrC,EAC9B,GAAG,CAAC,EACL,EAEA,IAAIsC,EAAcpC,EAElBqC,EAAOC,QAAUF,C", "sources": ["webpack://c-yy-gz-aipoerp/./src/client/web/components/basic/demo/index.jsx", "webpack://c-yy-gz-aipoerp/./src/client/web/components/index.jsx", "webpack://c-yy-gz-aipoerp/./src/client/web/index.jsx", "webpack://c-yy-gz-aipoerp/./src/client/web/redux/reducers.jsx", "webpack://c-yy-gz-aipoerp/./src/client/web/routes/index.jsx", "webpack://c-yy-gz-aipoerp/./node_modules/deepmerge/dist/cjs.js"], "sourcesContent": ["import React, { Component } from 'react';\n\nimport './index.less'\n\nexport default class Demo extends Component {\n  render() {\n    return (\n      <div>这是一个PC组件Demo!</div>\n    )\n  }\n}", "import * as basic from './basic';              // 用于扩展基础组件（一般是表单组件）\nimport * as formatter from './formatter';      // 用于扩展表格格式化列组件\nimport * as home from './home';                // 用于扩展首页组件\nimport * as meta from './meta';                // 用于扩展业务组件（一般是容器组件）\nimport * as modal from './modal';              // 用于扩展弹窗组件\nimport * as popover from './popover';          // 用于扩展弹出菜单组件\nimport portal from './portal';                // 用于扩展门户组件\nimport * as toolbar from './toolbar';\n\nimport deepmerge from 'deepmerge';\n\n\nconst originExtend = {\n    basic,\n    formatter,\n    home,\n    meta,\n    modal,\n    popover,\n    portal,\n    toolbar\n}\nexport default originExtend;\n", "// 注册扩展脚本\nconst businessContext = require.context(\"business\");\ncb.extend.registerScripts(process.env.__DOMAINKEY__, businessContext);\n\n// 本地调试和普通打包时加载扩展组件，组件单独打包时扩展组件不在此处注册\nif (!process.env.__EXTENDCOMP__) {\n  // 注册扩展组件\n  const extendComponents = require(\"./components\").default;\n  cb.extend.registerComponents(process.env.__DOMAINKEY__, extendComponents);\n}\n\n// 注册reducer\nconst extendReducers = require(\"./redux/reducers\").default;\ncb.extend.registerReducers(process.env.__DOMAINKEY__, extendReducers);\n\n// 注册router\nconst extendRoutes = require(\"./routes\").default;\ncb.extend.registerRoutes(process.env.__DOMAINKEY__, extendRoutes);\n\n// 注册扩展action\nconst extendBizAction = require(\"../common/biz/actions\").default;\ncb.extend.registerBizAction(process.env.__DOMAINKEY__, extendBizAction);\n\n// 注册变量（框架使用的变量前后添加__）此方式和在Consul中配置等效且优先级大于Consul\ncb.extend.registerEnv(process.env.__DOMAINKEY__, { // registerEnv的第2个参数可以是个方法，接收一个当前环境env参数\n  currentEnv: \"daily\" // 领域自定义变量示例（小驼峰规范），变量使用：viewmodel.getEnv('currentEnv')\n});\n\n// 注册多语\ncb.lang.registerLang(process.env.__DOMAINKEY__, require(\"../../pack\"), \"\");\n\n\n", "// >>>>>>>>>>>>>>>>>>>> 示例开始 >>>>>>>>>>>>>>>>>>>> //\nimport Demo from \"../../common/redux/modules/demo\";\n\nexport default { Demo };\n// <<<<<<<<<<<<<<<<<<<< 示例结束 <<<<<<<<<<<<<<<<<<<< //", "// >>>>>>>>>>>>>>>>>>>> 示例开始 >>>>>>>>>>>>>>>>>>>> //\nimport { Demo } from \"../components/basic\";\n\n// 目前支持参数 exact | path | component \nexport default [\n  { exact: true, path: \"/demo\", component: Demo }\n];\n// <<<<<<<<<<<<<<<<<<<< 示例结束 <<<<<<<<<<<<<<<<<<<< //", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n"], "names": ["Demo", "_Component", "_classCallCheck", "_callSuper", "arguments", "_inherits", "key", "value", "React", "Component", "originExtend", "basic", "formatter", "home", "meta", "modal", "popover", "portal", "toolbar", "businessContext", "require", "cb", "extend", "registerScripts", "process", "extendComponents", "registerComponents", "extendReducers", "registerReducers", "extendRoutes", "registerRoutes", "extendBizAction", "registerBizAction", "registerEnv", "currentEnv", "lang", "registerLang", "exact", "path", "component", "isMergeableObject", "isNonNullObject", "stringValue", "Object", "prototype", "toString", "call", "$$typeof", "REACT_ELEMENT_TYPE", "isReactElement", "isSpecial", "Symbol", "for", "cloneUnlessOtherwiseSpecified", "options", "clone", "deepmerge", "val", "Array", "isArray", "defaultArrayMerge", "target", "source", "concat", "map", "element", "get<PERSON><PERSON><PERSON>", "keys", "getOwnPropertySymbols", "filter", "symbol", "propertyIsEnumerable", "getEnumerableOwnPropertySymbols", "propertyIsOnObject", "object", "property", "_", "mergeObject", "destination", "for<PERSON>ach", "hasOwnProperty", "propertyIsUnsafe", "customMerge", "getMergeFunction", "arrayMerge", "sourceIsArray", "all", "array", "Error", "reduce", "prev", "next", "deepmerge_1", "module", "exports"], "sourceRoot": ""}