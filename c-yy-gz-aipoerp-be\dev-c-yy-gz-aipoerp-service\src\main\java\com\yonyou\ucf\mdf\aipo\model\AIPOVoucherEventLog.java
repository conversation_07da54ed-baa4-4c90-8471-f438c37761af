package com.yonyou.ucf.mdf.aipo.model;

import java.util.Date;
import java.util.List;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 凭证事件日志
 * @Date 2025-05-21 09:47:44
 * @since 2023/11/28
 **/
@YMSEntity(name = "AIPOERPCREATE.AIPOERPCREATE.AIPOVoucherEventLog", domain = "c-yy-gz-aipoerp")
public class AIPOVoucherEventLog extends SuperDO {
	public static final String ENTITY_NAME = "AIPOERPCREATE.AIPOERPCREATE.AIPOVoucherEventLog";
	public static final String EVENTID = "eventid";
	public static final String EVENTTYPE = "eventType";
	public static final String EVENTCONTENT = "eventContent";
	public static final String EVENTVALID = "eventValid";
	public static final String BILLID = "billid";
	public static final String BILLCODE = "billcode";
	public static final String VOUCHERPERIOD = "voucherperiod";
	public static final String ACCBOOKID = "accbookId";
	public static final String PROCESSSTATUS = "processStatus";
	public static final String PROCESSRESULT = "processResult";
	public static final String COSTTIME = "costTime";
	public static final String ERRORMSG = "errorMsg";
	public static final String CREATETIME = "createTime";
	public static final String UPDATETIME = "updateTime";
	public static final String PROCESSCOUNT = "processCount";
	public static final String VOUCHEREVENTLOGDETAILLIST = "VoucherEventLogDetailList";
	public static final String BUSINESSDATE = "businessDate";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 事件id */
	private String eventid;
	/* 事件类型 */
	private String eventType;
	/* 事件内容 */
	private String eventContent;
	/* 事件有效性 */
	private String eventValid;
	/* 凭证id */
	private String billid;
	/* 凭证号 */
	private String billcode;
	/* 凭证期间 */
	private String voucherperiod;
	/* 账簿 */
	private String accbookId;
	/* 处理状态 */
	private String processStatus;
	/* 处理结果 */
	private String processResult;
	/* 处理耗时 */
	private Integer costTime;
	/* 失败原因 */
	private String errorMsg;
	/* 创建时间 */
	private Date createTime;
	/* 更新时间 */
	private Date updateTime;
	/* 处理次数 */
	private Integer processCount;
	/* 凭证事件日志明细 */
	private List<VoucherEventLogDetail> VoucherEventLogDetailList;
	/* 单据日期 */
	private String businessDate;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setEventid(String eventid) {
		this.eventid = eventid;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public void setEventContent(String eventContent) {
		this.eventContent = eventContent;
	}

	public void setEventValid(String eventValid) {
		this.eventValid = eventValid;
	}

	public void setBillid(String billid) {
		this.billid = billid;
	}

	public void setBillcode(String billcode) {
		this.billcode = billcode;
	}

	public void setVoucherperiod(String voucherperiod) {
		this.voucherperiod = voucherperiod;
	}

	public void setAccbookId(String accbookId) {
		this.accbookId = accbookId;
	}

	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}

	public void setProcessResult(String processResult) {
		this.processResult = processResult;
	}

	public void setCostTime(Integer costTime) {
		this.costTime = costTime;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public void setProcessCount(Integer processCount) {
		this.processCount = processCount;
	}

	public void setVoucherEventLogDetailList(List<VoucherEventLogDetail> VoucherEventLogDetailList) {
		this.VoucherEventLogDetailList = VoucherEventLogDetailList;
	}

	public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getEventid() {
		return eventid;
	}

	public String getEventType() {
		return eventType;
	}

	public String getEventContent() {
		return eventContent;
	}

	public String getEventValid() {
		return eventValid;
	}

	public String getBillid() {
		return billid;
	}

	public String getBillcode() {
		return billcode;
	}

	public String getVoucherperiod() {
		return voucherperiod;
	}

	public String getAccbookId() {
		return accbookId;
	}

	public String getProcessStatus() {
		return processStatus;
	}

	public String getProcessResult() {
		return processResult;
	}

	public Integer getCostTime() {
		return costTime;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public Integer getProcessCount() {
		return processCount;
	}

	public List<VoucherEventLogDetail> getVoucherEventLogDetailList() {
		return VoucherEventLogDetailList;
	}

	public String getBusinessDate() {
		return businessDate;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}

