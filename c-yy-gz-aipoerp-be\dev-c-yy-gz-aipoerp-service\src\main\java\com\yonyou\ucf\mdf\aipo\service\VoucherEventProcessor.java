package com.yonyou.ucf.mdf.aipo.service;

import com.yonyou.ucf.mdf.aipo.model.EventContent;
import com.yonyou.ucf.mdf.aipo.model.EventType;

/**
 * 凭证事件处理器基类
 */
public abstract class VoucherEventProcessor implements IVoucherEventProcessService {
	private final EventType supportedEventType;

	protected VoucherEventProcessor(EventType supportedEventType) {
		this.supportedEventType = supportedEventType;
	}

	public EventType getSupportedEventType() {
		return supportedEventType;
	}

	@Override
	public void processEvent(EventContent eventContent) {
		if (eventContent.getType() != supportedEventType) {
			throw new IllegalArgumentException("不支持的事件类型: " + eventContent.getType());
		}
		doProcessEvent(eventContent);
	}

	/**
	 * 具体的事件处理逻辑
	 */
	protected abstract void doProcessEvent(EventContent eventContent);

}
