package com.yonyou.ucf.mdf.equip.model;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.yonyou.iuap.yms.annotation.YMSEntity;
import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import com.yonyou.ypd.bill.basic.entity.SuperDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 资产卡片数据
 * @Date 2025-02-28 15:14:01
 * @since 2023/11/28
 **/
@Data
@YMSEntity(name = "ZCEDI.ZCEDI.EquipCardDataVo", domain = "c-yy-gz-aipoerp")
public class EquipCardDataVo extends SuperDO {
    public static final String ENTITY_NAME = "ZCEDI.ZCEDI.EquipCardDataVo";
    public static final String COMPARISON_RESULT = "comparison_result";
    public static final String EQUIP_NAME = "equip_name";
    public static final String EQUIP_CODE = "equip_code";
    public static final String PK_ORG = "pk_org";
    public static final String PK_LOCATION = "pk_location";
    public static final String PK_CATEGORY = "pk_category";
    public static final String PK_USED_STATUS = "pk_used_status";
    public static final String PK_USEDORG = "pk_usedorg";
    public static final String PK_PRIORITY = "pk_priority";
    public static final String BEGIN_DATE = "begin_date";
    public static final String PK_USEDEPT = "pk_usedept";
    public static final String PRECODING = "precoding";
    public static final String ACCU_DEP = "accu_dep";
    public static final String PK_USER = "pk_user";
    public static final String SPEC = "spec";
    public static final String PK_MANDEPT = "pk_mandept";
    public static final String ASSOCIATE_FLAG = "associate_flag";
    public static final String PK_OWNERORG = "pk_ownerorg";
    public static final String PK_MATERIAL = "pk_material";
    public static final String IS_EXIT_DIFF = "is_exit_diff";
    public static final String IS_PUSH = "is_push";
    public static final String START_USED_DATE = "start_used_date";
    public static final String PK_ICORG = "pk_icorg";
    public static final String PK_CARD_EFA = "pk_card_efa";
    public static final String SERVICE_MONTH = "service_month";
    public static final String MODEL = "model";
    public static final String USED_MONTH = "used_month";
    public static final String FA_FLAG = "fa_flag";
    public static final String NEW_VALUE = "new_value";
    public static final String PK_OWNERUNIT = "pk_ownerunit";
    public static final String PURC_PRICE_TAX = "purc_price_tax";
    public static final String FA_WRITEBACK_TIME = "fa_writeback_time";
    public static final String ASSET_CODE = "asset_code";
    public static final String PK_MANAGER = "pk_manager";
    public static final String USERDEFINES = "userDefines";
    public static final String EQUIPCARDDATACONTRASTDETAILVOLIST = "EquipCardDataContrastDetailVoList";
    public static final String CREATETIME = "createTime";
    public static final String CREATOR = "creator";
    public static final String ID = "id";
    public static final String MODIFIER = "modifier";
    public static final String MODIFYTIME = "modifyTime";
    public static final String PUBTS = "pubts";
    public static final String YTENANTID = "ytenantId";

    /* 对比结果 */
    private String comparison_result="MATCH";
    /* 资产名称 */
    private String equip_name;
    /* 资产编码 */
    private String equip_code;
    /* 资产组织 */
    private String pk_org;
    /* 位置 */
    private String pk_location;
    /* 资产类别 */
    private String pk_category;
    /* 资产状态 */
    private String pk_used_status;
    /* 使用单位 */
    private String pk_usedorg;
    /* 关键程度 */
    private String pk_priority;
    /* 开始使用日期 */
    private String begin_date;
    /* 使用部门 */
    private String pk_usedept;
    /* 出厂编码 */
    private String precoding;
    /* 累计折旧 */
    private BigDecimal accu_dep;
    /* 责任人 */
    private String pk_user;
    /* 规格 */
    private String spec;
    /* 管理部门 */
    private String pk_mandept;
    /* 联动固定资产 */
    private Boolean associate_flag;
    /* 资产管理组织 */
    private String pk_ownerorg;
    /* 物料 */
    private Long pk_material;
    /* 与改变前是否存在差异 */
    private String is_exit_diff="N";
    /* 是否已推送 */
    private String is_push="N";
    /* 投用日期 */
    private String start_used_date;
    /* 库存组织 */
    private String pk_icorg;
    /* 固定资产 */
    private String pk_card_efa;
    /* 使用月限 */
    private Integer service_month;
    /* 型号 */
    private String model;
    /* 已计提期数 */
    private BigDecimal used_month;
    /* 固定资产核算 */
    private Boolean fa_flag;
    /* 净值 */
    private BigDecimal new_value;
    /* 所有权 */
    private String pk_ownerunit;
    /* 价税合计 */
    private BigDecimal purc_price_tax;
    /* 价值回传时间 */
    private Date fa_writeback_time;
    /* 固定资产编码 */
    private String asset_code;
    /* 管理人 */
    private String pk_manager;
    /* 自定义项 */
    private UserDefines userDefines;
    /* 数据对比明细 */
    private List<EquipCardDataContrastDetailVo> EquipCardDataContrastDetailVoList;
    /* 创建时间 */
    private Date createTime;
    /* 创建人 */
    private String creator;
    /* id */
    private String id;
    /* 修改人 */
    private String modifier;
    /* 修改时间 */
    private Date modifyTime;
    /* pubts */
    private Date pubts;
    /* 租户id */
    private String ytenantId;

    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
