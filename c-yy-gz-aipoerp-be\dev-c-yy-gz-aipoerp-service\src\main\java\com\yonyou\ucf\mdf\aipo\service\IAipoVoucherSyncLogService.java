package com.yonyou.ucf.mdf.aipo.service;

import java.util.List;

import com.yonyou.ucf.mdf.aipo.vo.AIPOVoucherSyncLog;

/**
 * 凭证同步日志服务接口
 */
public interface IAipoVoucherSyncLogService {

	/**
	 * 查询推送失败的凭证同步日志
	 * 
	 * @return 失败的日志记录列表
	 */
	List<AIPOVoucherSyncLog> queryFailedSyncLogs();

	/**
	 * 保存推送凭证同步日志
	 * 
	 * @param syncLog
	 * @return
	 */
	AIPOVoucherSyncLog save(AIPOVoucherSyncLog syncLog);
}