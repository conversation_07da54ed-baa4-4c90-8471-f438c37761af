package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IDeptService;
import com.yonyou.ucf.mdf.api.IEnterBankAccService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Deprecated
@Slf4j
@Component("aipoEnterBankAccountAfterDeleteRule")
public class AIPOEnterBankAccountAfterDeleteRule implements IYpdCommonRul {
    @Autowired
    IEnterBankAccService enterBankAccService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        JSONObject deleteParams = JSON.parseObject(JSON.toJSONString(params));
        if (MapUtils.isEmpty(deleteParams.getJSONObject("requestData"))){
            log.error("无法获取到企业银行账户删除的详细信息以删除高级版部门，请重试 deleteParams-->{}",deleteParams);
            throw new BusinessException("无法获取到企业银行账户删除的详细信息以删除高级版部门，请重试");
        }else {
            String id = deleteParams.getJSONObject("requestData").getString("id");
            enterBankAccService.deleteEnterBankFromNC(id);
        }

        return result;
    }
}
