package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.entity.AIPONCStaffSyncTaskLogDetail;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IStaffService;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 员工保存后自动同步规则测试类
 */
@ExtendWith(MockitoExtension.class)
class AIPOStaffAfterSaveRuleTest {

    @Mock
    private IStaffService staffService;

    @Mock
    private RulCtxVO rulCtxVO;

    @InjectMocks
    private AIPOStaffAfterSaveRule staffAfterSaveRule;

    private Map<String, Object> params;
    private JSONObject staffInfo;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        staffInfo = new JSONObject();
        staffInfo.put("id", "test-staff-001");
        staffInfo.put("name", "张三");
        staffInfo.put("code", "ZS001");
        staffInfo.put("unitId", "org001");
        staffInfo.put("deptId", "dept001");
        staffInfo.put("mobile", "13800138000");

        params = new HashMap<>();
        Map<String, Object> returnData = new HashMap<>();
        returnData.put("return", staffInfo);
        returnData.put("requestData", staffInfo);
        params.putAll(returnData);
    }

    @Test
    void testExecute_Success() {
        // 模拟成功的同步响应
        JSONObject mockResponse = new JSONObject();
        mockResponse.put("success", true);
        when(staffService.pushStaffInfoToNC(any(JSONObject.class), any(AIPONCStaffSyncTaskLogDetail.class)))
                .thenReturn(mockResponse);

        // 执行规则
        Object result = staffAfterSaveRule.execute(rulCtxVO, params);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof RuleExecuteResult);

        // 验证服务调用
        verify(staffService, times(1)).pushStaffInfoToNC(any(JSONObject.class), any(AIPONCStaffSyncTaskLogDetail.class));
    }

    @Test
    void testExecute_EmptyReturn_ThrowsException() {
        // 准备空的返回数据
        params.put("return", new JSONObject());

        // 执行并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            staffAfterSaveRule.execute(rulCtxVO, params);
        });

        assertEquals("无法获取到员工保存结果同步高级版系统，请重试", exception.getMessage());
        verify(staffService, never()).pushStaffInfoToNC(any(), any());
    }

    @Test
    void testExecute_NullReturn_ThrowsException() {
        // 准备null返回数据
        params.put("return", null);

        // 执行并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            staffAfterSaveRule.execute(rulCtxVO, params);
        });

        assertEquals("无法获取到员工保存结果同步高级版系统，请重试", exception.getMessage());
        verify(staffService, never()).pushStaffInfoToNC(any(), any());
    }

    @Test
    void testExecute_ServiceThrowsException_PropagatesException() {
        // 模拟服务抛出异常
        when(staffService.pushStaffInfoToNC(any(JSONObject.class), any(AIPONCStaffSyncTaskLogDetail.class)))
                .thenThrow(new RuntimeException("NC系统连接失败"));

        // 执行并验证异常传播
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            staffAfterSaveRule.execute(rulCtxVO, params);
        });

        assertEquals("NC系统连接失败", exception.getMessage());
        verify(staffService, times(1)).pushStaffInfoToNC(any(JSONObject.class), any(AIPONCStaffSyncTaskLogDetail.class));
    }
}
