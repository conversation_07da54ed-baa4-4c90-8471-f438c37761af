package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.bill.rule.base.AbstractCommonRule;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdd.ext.model.BillContext;
import com.yonyou.ucf.mdf.api.IMerchantService;
import com.yonyou.ucf.mdf.api.IProjectService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("aipoMerchantAfterSaveRule")
@Slf4j
public class AIPOMerchantAfterSaveRule implements IYpdCommonRul {

    @Autowired
    IMerchantService merchantService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        JSONObject jsonParams = (JSONObject) JSON.toJSON(params);
        JSONObject reqReturn = jsonParams.getJSONObject("return");
        // 采购模块会有允许供应商自行注册然后艾珀在系统审核，审核完成后就会将该供应商推入供应商档案里面，这时requestData是list，反正也用不上requestData，直接注释掉
//        JSONObject requestData = jsonParams.getJSONObject("requestData");

        if (MapUtils.isEmpty(reqReturn)){
            log.error("无法获取到客户档案保存结果同步高级版系统，请重试 params-->{}",params);
            throw new BusinessException("无法获取到客户档案保存结果同步高级版系统，请重试");
        }else {
            JSONObject jsonObject =  merchantService.pushMerchantToNC(reqReturn, reqReturn);
            log.error("这个是处理完毕的，包含应用范围完整子表信息的客户档案 jsonObject-->{}", jsonObject);
        }
        return result;
    }

}
