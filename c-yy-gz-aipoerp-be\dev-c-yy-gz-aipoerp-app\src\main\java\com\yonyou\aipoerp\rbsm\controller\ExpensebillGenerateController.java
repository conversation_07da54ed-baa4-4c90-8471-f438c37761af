package com.yonyou.aipoerp.rbsm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.rbsm.model.GenerateSocialParam;
import com.yonyou.ucf.mdf.rbsm.model.IncomeTaxGenerateParam;
import com.yonyou.ucf.mdf.rbsm.model.LaborGenerateParam;
import com.yonyou.ucf.mdf.rbsm.model.PeriodParam;
import com.yonyou.ucf.mdf.rbsm.model.PeriodVO;
import com.yonyou.ucf.mdf.rbsm.service.itf.IExpensebillGenerateService;
import com.yonyou.ucf.mdf.rbsm.service.itf.ILaborPayBillGenerateService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPeriodService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IncomeTaxGenerateService;

/**
 * 报销单自动生成接口
 *
 * <AUTHOR>
 *
 *         2025年2月21日
 */
@RestController
@RequestMapping("/expensebill")
public class ExpensebillGenerateController {

	@Autowired
	private IExpensebillGenerateService generateService;
	@Autowired
	private ILaborPayBillGenerateService laborPayBillGenerateService;
	@Autowired
	private IncomeTaxGenerateService incomeTaxGenerateService;
	@Autowired
	private IPeriodService periodService;

	@RequestMapping("/generatesocial")
	public JSONObject generateSocial(@RequestBody GenerateSocialParam param) {
		generateService.generateSocial(param);
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("message", "生成成功");
		return result;
	}

	/**
	 * 自动生成工会会费支付单
	 */
	@RequestMapping("/generatelabor")
	public JSONObject generateLabor(@RequestBody LaborGenerateParam param) {
		laborPayBillGenerateService.generateLabor(param);
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("message", "生成成功");
		return result;
	}

	/**
	 * 自动生成个税支付单
	 */
	@RequestMapping("/generateIncomeTax")
	public JSONObject generateIncomeTax(@RequestBody IncomeTaxGenerateParam param) {
		incomeTaxGenerateService.generateIncomeTax(param);
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("message", "生成成功");
		return result;
	}

	@RequestMapping("/getPeriod")
	public JSONObject getPeriod(@RequestBody PeriodParam param) {
		PeriodVO period = periodService.getPeriod(param);
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("message", "success");
		result.put("data", period);
		return result;
	}

}
