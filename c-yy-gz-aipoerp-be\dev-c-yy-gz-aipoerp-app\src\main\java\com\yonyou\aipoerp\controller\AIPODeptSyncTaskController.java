package com.yonyou.aipoerp.controller;

import java.util.Date;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.dept.model.TaskResult;
import com.yonyou.ucf.mdf.dept.service.DeptSyncService;

import lombok.extern.slf4j.Slf4j;

/**
 * 部门同步定时任务控制器
 */
@RestController
@RequestMapping("/dispatchtask")
@Slf4j
public class AIPODeptSyncTaskController {

    @Autowired
    private DeptSyncService deptSyncService;

    /**
     * 部门数据同步到NCC高级版定时任务接口
     * 
     * @param request HTTP请求对象
     * @param paramMap 请求参数，包含beginDate和endDate
     * @return 任务执行结果
     */
    @RequestMapping("/deptsync")
    public TaskResult syncDeptToNC(HttpServletRequest request, @RequestBody(required = false) JSONObject paramMap) {
        log.info("开始执行部门同步定时任务");
        
        String logId = Optional.ofNullable(request.getHeader("logId")).orElse("");
        Date beginDate = null;
        Date endDate = null;

        // 解析请求参数
        boolean forceSync = false; // 默认不强制同步
        if (paramMap != null) {
            beginDate = paramMap.getDate("beginDate");
            endDate = paramMap.getDate("endDate");
            forceSync = paramMap.getBooleanValue("forceSync"); // 获取强制同步参数
        }

        // 如果没有传入时间参数，使用默认时间范围（最近24小时）
        if (beginDate == null || endDate == null) {
            endDate = new Date();
            beginDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000L); // 24小时前
        }

        log.info("部门同步任务参数 - logId: {}, beginDate: {}, endDate: {}, forceSync: {}",
                logId, beginDate, endDate, forceSync);

        try {
            // 执行部门同步
            JSONObject syncResult = deptSyncService.syncDeptToNC(beginDate, endDate, forceSync);
            
            // 构建返回结果
            int totalCount = syncResult.getIntValue("totalCount");
            int filteredCount = syncResult.getIntValue("filteredCount");
            int syncCount = syncResult.getIntValue("syncCount");
            int successCount = syncResult.getIntValue("successCount");
            int failCount = syncResult.getIntValue("failCount");
            boolean success = syncResult.getBooleanValue("success");

            String title = "部门数据同步到NCC高级版";
            String content = String.format("同步完成 - 查询总数: %d, 过滤数: %d, 同步数: %d, 成功: %d, 失败: %d",
                    totalCount, filteredCount, syncCount, successCount, failCount);
            
            if (success) {
                log.info("部门同步任务执行成功: {}", content);
                return TaskResult.success(title, content);
            } else {
                String errorMsg = syncResult.getString("errorMsg");
                log.error("部门同步任务执行失败: {}, 错误信息: {}", content, errorMsg);
                return TaskResult.failure(title, content, errorMsg);
            }
            
        } catch (Exception e) {
            log.error("部门同步任务执行异常", e);
            String title = "部门数据同步到NCC高级版";
            String content = "同步任务执行异常";
            String errorMsg = e.getMessage();
            return TaskResult.failure(title, content, errorMsg);
        }
    }

    /**
     * 异步部门数据同步到NCC高级版定时任务接口
     * 
     * @param request HTTP请求对象
     * @param paramMap 请求参数，包含beginDate和endDate
     * @return 任务执行结果
     */
    @RequestMapping("/deptsync/async")
    public TaskResult asyncSyncDeptToNC(HttpServletRequest request, @RequestBody(required = false) JSONObject paramMap) {
        log.info("开始执行异步部门同步定时任务");
        
        String logId = Optional.ofNullable(request.getHeader("logId")).orElse("");
        Date beginDate = null;
        Date endDate = null;

        // 解析请求参数
        boolean forceSync = false; // 默认不强制同步
        if (paramMap != null) {
            beginDate = paramMap.getDate("beginDate");
            endDate = paramMap.getDate("endDate");
            forceSync = paramMap.getBooleanValue("forceSync"); // 获取强制同步参数
        }

        // 如果没有传入时间参数，使用默认时间范围（最近24小时）
        if (beginDate == null || endDate == null) {
            endDate = new Date();
            beginDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000L); // 24小时前
        }

        log.info("异步部门同步任务参数 - logId: {}, beginDate: {}, endDate: {}, forceSync: {}",
                logId, beginDate, endDate, forceSync);

        // 异步执行，立即返回
        final Date finalBeginDate = beginDate;
        final Date finalEndDate = endDate;
        final String finalLogId = logId;
        final boolean finalForceSync = forceSync;

        // 在新线程中执行同步任务
        new Thread(() -> {
            try {
                log.info("异步执行部门同步任务开始 - logId: {}", finalLogId);
                JSONObject syncResult = deptSyncService.syncDeptToNC(finalBeginDate, finalEndDate, finalForceSync);
                
                int totalCount = syncResult.getIntValue("totalCount");
                int filteredCount = syncResult.getIntValue("filteredCount");
                int syncCount = syncResult.getIntValue("syncCount");
                int successCount = syncResult.getIntValue("successCount");
                int failCount = syncResult.getIntValue("failCount");
                boolean success = syncResult.getBooleanValue("success");

                if (success) {
                    log.info("异步部门同步任务执行成功 - logId: {}, 查询总数: {}, 过滤数: {}, 同步数: {}, 成功: {}, 失败: {}",
                            finalLogId, totalCount, filteredCount, syncCount, successCount, failCount);
                } else {
                    String errorMsg = syncResult.getString("errorMsg");
                    log.error("异步部门同步任务执行失败 - logId: {}, 查询总数: {}, 过滤数: {}, 同步数: {}, 成功: {}, 失败: {}, 错误: {}",
                            finalLogId, totalCount, filteredCount, syncCount, successCount, failCount, errorMsg);
                }
                
            } catch (Exception e) {
                log.error("异步部门同步任务执行异常 - logId: {}", finalLogId, e);
            }
        }).start();

        String title = "异步部门数据同步到NCC高级版";
        String content = "异步同步任务已启动";
        return TaskResult.async(title, content);
    }
}
