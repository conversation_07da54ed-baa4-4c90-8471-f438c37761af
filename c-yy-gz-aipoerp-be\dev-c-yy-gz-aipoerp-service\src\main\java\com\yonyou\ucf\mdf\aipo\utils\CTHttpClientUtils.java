package com.yonyou.ucf.mdf.aipo.utils;


import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ** description 描述 **
 *
 * <AUTHOR> duanlmz
 * @date :2022/7/20 17:51
 */
@Slf4j
public class CTHttpClientUtils {

    public static final String POST = "post";

    public static final String GET = "get";

    private static final long CONNECT_TIMEOUT = 180;
    private static final long READ_TIMEOUT = 180;
    private static final long WRITE_TIMEOUT = 180;

    public static String doGet(String url, String path, Map<String, Object> headerParams, Map<String, Object> mapParams) {
        String resultString = "";
        Response response = null;
        try {
            //******** 一、 创建 httpClient 对象***********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            String params = setUrlParams(mapParams);
            String endpoint = url + path + params;

            // ******** 二、创建request 对象*************
            Request.Builder builder = new Request.Builder()
                    .url(endpoint)
                    .get();
            if (null != headerParams && !headerParams.isEmpty()) {
                headerParams.forEach((key, val) -> builder.addHeader(key, val.toString()));
            }
            Request request = builder.build();
            //********* 三、发送请求 ********************

            response = httpClient.newCall(request).execute();

            //********* 四、对响应进行处理 ***************
            //1、如果 http 状态码不在 【200 ，300】区间内，抛出异常
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            // 因为服务器返回的数据可能非常大，所以必须通过数据流的方式来进行访问
            // 提供了诸如 string() 和 bytes() 这样的方法将流内的数据一次性读取完毕
            if (response.body() != null) {
                resultString = response.body().string();
            }

        } catch (SocketTimeoutException e) {
            // 超时
            throw new RuntimeException();
        } catch (IOException e) {
            // 网络IO异常
            throw new RuntimeException();
        } catch (Exception e) {
            // 其他异常
            throw new RuntimeException();
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                if (response.body() != null) {
                    response.body().close();
                }
            }
        }
        return resultString;
    }

    public static String doPost(String url, String path, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {

        Response response = null;
        String resultString = "";
        try {
            //******* 一、创建 httpClient 对象 **********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            StringBuffer stringB = new StringBuffer();
            stringB.append(url);
            stringB.append(path);
            if (null != queryParams && !queryParams.isEmpty()) {
                String params = setUrlParams(queryParams);
                stringB.append(params);
            }

            MediaType mediaType = MediaType.parse("application/json");
            String payload = JSON.writeValueAsString(obj);
            RequestBody body = RequestBody.create(payload, mediaType);

            //******** 二、创建 request 对象 ************
            log.error("调用异构系统接口地址:{}, queryString:{}, payload: {}",url, stringB, payload);
            Request.Builder builder = new Request.Builder()
                    .url(stringB.toString())
                    .post(body);
            if (null != headerParams && !headerParams.isEmpty()) {
                headerParams.forEach((key, val) -> {
                    builder.addHeader(key, val.toString());
                });
            }
            Request request = builder.build();
            //******** 三、执行请求 ********************
            response = httpClient.newCall(request).execute();

            //******** 四、处理响应 ********************
            int code = response.code();
            String message = response.message();
/*            if (!response.isSuccessful()) {
                throw new IOException(" Unexpect code " + response);
            }*/

            if (response.body() != null) {
                resultString = response.body().string();
            }
            log.error("调用异构系统接口地址:{}, queryString:{}, resultString: {}",url, stringB, resultString);
        } catch (SocketTimeoutException e) {
            // 超时
            log.error("接口调用连接超时,一般是到三方域名或IP不通，调用接口地址: {}", url);
            throw new RuntimeException();
        } catch (IOException e) {
            // 网络IO异常
            log.error("调用三方接口IO异常: {}", url);
            throw new RuntimeException();
        } catch (Exception e) {
            // 其他异常
            log.error("调用三方接口内部代码异常: {}", url);
            throw new RuntimeException();
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                assert response.body() != null;
                response.body().close();
            }
        }
        return resultString;
    }

    public static String doDelete(String url, String path, Map<String, Object> headerParams, Map<String, Object> queryParams) {

        Response response = null;
        String resultString = "";
        try {
            //******* 一、创建 httpClient 对象 **********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            StringBuffer stringB = new StringBuffer();
            stringB.append(url);
            stringB.append(path);
            if (null != queryParams && !queryParams.isEmpty()) {
                String params = setUrlParams(queryParams);
                stringB.append(params);
            }
            MediaType mediaType = MediaType.parse("application/json");

            //******** 二、创建 request 对象 ************

            Request.Builder builder = new Request.Builder()
                    .url(stringB.toString())
                    .delete();
            if (null != headerParams && !headerParams.isEmpty()) {
                headerParams.forEach((key, val) -> {
                    builder.addHeader(key, val.toString());
                });
            }
            Request request = builder.build();
            //******** 三、执行请求 ********************
            response = httpClient.newCall(request).execute();

            //******** 四、处理响应 ********************
            if (!response.isSuccessful()) {
                throw new IOException(" Unexpect code " + response);
            }
            if (response.body() != null) {
                resultString = response.body().string();
            }

        } catch (SocketTimeoutException e) {
            // 超时
            throw new RuntimeException();
        } catch (IOException e) {
            // 网络IO异常
            throw new RuntimeException();
        } catch (Exception e) {
            // 其他异常
            throw new RuntimeException();
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                if (response.body() != null) {
                    response.body().close();
                }
            }
        }
        return resultString;
    }

    public static String doPut(String url, String path, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {

        Response response = null;
        String resultString = "";
        try {
            //******* 一、创建 httpClient 对象 **********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            StringBuffer stringB = new StringBuffer();
            stringB.append(url);
            stringB.append(path);
            if (null != queryParams && !queryParams.isEmpty()) {
                String params = setUrlParams(queryParams);
                stringB.append(params);
            }
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(JSON.writeValueAsString(obj), mediaType);

            //******** 二、创建 request 对象 ************

            Request.Builder builder = new Request.Builder()
                    .url(stringB.toString())
                    .put(body);
            if (null != headerParams && !headerParams.isEmpty()) {
                headerParams.forEach((key, val) -> {
                    builder.addHeader(key, val.toString());
                });
            }
            Request request = builder.build();
            //******** 三、执行请求 ********************
            response = httpClient.newCall(request).execute();

            //******** 四、处理响应 ********************
            if (!response.isSuccessful()) {
                throw new IOException(" Unexpect code " + response);
            }

            if (response.body() != null) {
                resultString = response.body().string();
            }

        } catch (SocketTimeoutException e) {
            // 超时
            throw new RuntimeException();
        } catch (IOException e) {
            // 网络IO异常
            throw new RuntimeException();
        } catch (Exception e) {
            // 其他异常
            throw new RuntimeException();
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                if (response.body() != null) {
                    response.body().close();
                }
            }
        }
        return resultString;
    }

    public static String setUrlParams(Map<String, Object> mapParams) {

        // 是否已经设置了问号
        boolean hasSetQuestionMark = false;
        StringBuilder strParams = new StringBuilder();
        if (mapParams != null) {
            Iterator<String> iterator = mapParams.keySet().iterator();
            String key = "";
            while (iterator.hasNext()) {
                try {
                    key = iterator.next();
                    if (!hasSetQuestionMark) {
                        strParams.append("?").append(key).append("=")
                                .append(mapParams.get(key).toString());
                        // .append(URLEncoder.encode(mapParams.get(key).toString(), "utf-8"));
                        hasSetQuestionMark = true;
                        continue;
                    }
                    strParams.append("&").append(key).append("=")
                            .append(mapParams.get(key).toString());
                    //.append(URLEncoder.encode(mapParams.get(key).toString(), "utf-8"));
                } catch (Exception e) {
                    log.error("参数设置异常：{}", e.getMessage());
                }
            }
        }
        return strParams.toString();
    }

    public static String doGet(String url, Map<String, Object> headerParams, Map<String, Object> mapParams) {
        return doGet(url, "", headerParams, mapParams);
    }

    public static String doPost(String url, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {
        return doPost(url, "", headerParams, queryParams, obj);
    }

    public static String doDelete(String url, Map<String, Object> headerParams, Map<String, Object> queryParams) {
        return doDelete(url, "", headerParams, queryParams);
    }

    public static String doPut(String url, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {
        return doPut(url, "", headerParams, queryParams, obj);
    }
}
