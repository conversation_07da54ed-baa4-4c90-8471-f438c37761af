package com.yonyou.ucf.mdf.rbsm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 薪资发放单对象
 * 
 * <AUTHOR>
 *
 *         2025年3月10日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WaPayfile {
	private String id;//
	private String busiOrg;// 组织id
	private String code;// 发放单编码
	private String name;// 发放单名称
	private String payPeriod;// 期间id
	private String taxYear;// 纳税年份
	private String taxMonth;// 纳税月份
	private String payDate;// 发薪日期
	private String pkWaScheme;// 发薪方案id
	private String payNums;// 发薪人数
	private String totals;// 应发合计
	private String actualTotals;// 实发合计
	private String schemeName;// 发薪方案名称
	private String payPeriodName;// 发薪期间名称
	private String segmentAccount;// 是否分段(0：不分段 1：是分段）
	private String busiOrgName;// 组织名称
	private String mullangName;// 发放单的多语名称
}
