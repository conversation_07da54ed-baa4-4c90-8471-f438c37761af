package com.yonyou.ucf.mdf.utils.openApi;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * Key工厂类
 *
 * <AUTHOR>
 */
public class KeysFactory {

    public static Key getPublicKey(String pubKey) throws Exception {
        Key key = null;

        try {
            byte[] keyBytes = Base64Util.decryptBASE64(pubKey);
            KeyFactory keyFactory = KeyFactory.getInstance(CipherConstant.RSA);

            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
            key = keyFactory.generatePublic(x509KeySpec);

        } catch (Exception e) {
            throw new Exception("无效的密钥  " + e.getMessage());
        }

        return key;
    }

    public static Key getSymKey(String symKey) throws Exception {
        Key key = null;

        try {
            byte[] keyBytes = Base64Util.decryptBASE64(symKey);
            // Key转换
            key = new SecretKeySpec(keyBytes, CipherConstant.AES);
        } catch (Exception e) {
            throw new Exception("无效密钥 " + e.getMessage());
        }

        return key;
    }
}
