package com.yonyou.ucf.mdf.rbsm.model;

import com.yonyou.ucf.mdf.rbsm.utils.OriNumEncryptUtils;

/**
 * 社保缴交明细
 */
public class InsurancePayDetail {
	private String insuranceId; // 社保缴交id
	private String staffId; // 员工id
	private String staffName; // 员工姓名
	private String insuranceYearMonth; // 缴交年月
	private String piId; // 缴交机构id
	private String piCode; // 缴交机构编码
	private String piName; // 缴交机构名称
	private String payInsuranceTypeId; // 险种id
	private String insuranceTypeName; // 险种名称
	private String radix; // 缴纳基数
	private String payPersonalBase; // 个人缴交基数
	private String payPersonalPayAmount; // 个人缴交额
	private String payPersonalFixedAmount; // 个人固定缴交额
	private String payPersonalAdjustmentAmount; // 个人缴交调整额
	private String payCompanyBase; // 单位交接基数
	private String payCompanyPayAmount; // 单位缴交额
	private String payCompanyFixedAmount; // 单位固定缴交额
	private String payCompanyAdjustmentAmount; // 单位缴交调整额

	private String taxOrgId; // 人员财务组织id
	private String taxDeptId; // 人员财务部门id

	public String getInsuranceId() {
		return insuranceId;
	}

	public void setInsuranceId(String insuranceId) {
		this.insuranceId = insuranceId;
	}

	public String getStaffId() {
		return staffId;
	}

	public void setStaffId(String staffId) {
		this.staffId = staffId;
	}

	public String getStaffName() {
		return staffName;
	}

	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}

	public String getInsuranceYearMonth() {
		return insuranceYearMonth;
	}

	public void setInsuranceYearMonth(String insuranceYearMonth) {
		this.insuranceYearMonth = insuranceYearMonth;
	}

	public String getPiId() {
		return piId;
	}

	public void setPiId(String piId) {
		this.piId = piId;
	}

	public String getPiCode() {
		return piCode;
	}

	public void setPiCode(String piCode) {
		this.piCode = piCode;
	}

	public String getPiName() {
		return piName;
	}

	public void setPiName(String piName) {
		this.piName = piName;
	}

	public String getPayInsuranceTypeId() {
		return payInsuranceTypeId;
	}

	public void setPayInsuranceTypeId(String payInsuranceTypeId) {
		this.payInsuranceTypeId = payInsuranceTypeId;
	}

	public String getInsuranceTypeName() {
		return insuranceTypeName;
	}

	public void setInsuranceTypeName(String insuranceTypeName) {
		this.insuranceTypeName = insuranceTypeName;
	}

	public String getRadix() {
		return OriNumEncryptUtils.decode(radix);
	}

	public void setRadix(String radix) {
		this.radix = radix;
	}

	public String getPayPersonalBase() {
		return OriNumEncryptUtils.decode(payPersonalBase);
	}

	public void setPayPersonalBase(String payPersonalBase) {
		this.payPersonalBase = payPersonalBase;
	}

	public String getPayPersonalPayAmount() {
		return OriNumEncryptUtils.decode(payPersonalPayAmount);
	}

	public void setPayPersonalPayAmount(String payPersonalPayAmount) {
		this.payPersonalPayAmount = payPersonalPayAmount;
	}

	public String getPayPersonalFixedAmount() {
		return OriNumEncryptUtils.decode(payPersonalFixedAmount);
	}

	public void setPayPersonalFixedAmount(String payPersonalFixedAmount) {
		this.payPersonalFixedAmount = payPersonalFixedAmount;
	}

	public String getPayPersonalAdjustmentAmount() {
		return OriNumEncryptUtils.decode(payPersonalAdjustmentAmount);
	}

	public void setPayPersonalAdjustmentAmount(String payPersonalAdjustmentAmount) {
		this.payPersonalAdjustmentAmount = payPersonalAdjustmentAmount;
	}

	public String getPayCompanyBase() {
		return OriNumEncryptUtils.decode(payCompanyBase);
	}

	public void setPayCompanyBase(String payCompanyBase) {
		this.payCompanyBase = payCompanyBase;
	}

	public String getPayCompanyPayAmount() {
		return OriNumEncryptUtils.decode(payCompanyPayAmount);
	}

	public void setPayCompanyPayAmount(String payCompanyPayAmount) {
		this.payCompanyPayAmount = payCompanyPayAmount;
	}

	public String getPayCompanyFixedAmount() {
		return OriNumEncryptUtils.decode(payCompanyFixedAmount);
	}

	public void setPayCompanyFixedAmount(String payCompanyFixedAmount) {
		this.payCompanyFixedAmount = payCompanyFixedAmount;
	}

	public String getPayCompanyAdjustmentAmount() {
		return OriNumEncryptUtils.decode(payCompanyAdjustmentAmount);
	}

	public void setPayCompanyAdjustmentAmount(String payCompanyAdjustmentAmount) {
		this.payCompanyAdjustmentAmount = payCompanyAdjustmentAmount;
	}

	public String getTaxOrgId() {
		return taxOrgId;
	}

	public void setTaxOrgId(String taxOrgId) {
		this.taxOrgId = taxOrgId;
	}

	public String getTaxDeptId() {
		return taxDeptId;
	}

	public void setTaxDeptId(String taxDeptId) {
		this.taxDeptId = taxDeptId;
	}

}
