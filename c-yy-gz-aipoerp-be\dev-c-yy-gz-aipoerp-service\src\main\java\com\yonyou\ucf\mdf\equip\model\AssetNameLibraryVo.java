package com.yonyou.ucf.mdf.equip.model;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.base.*;
import java.util.Date;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 资产名称库
 * @Date 2025-03-07 12:06:08
 * @since 2023/11/28
 **/
@YMSEntity(name = "ZCMCK.ZCMCK.AssetNameLibraryVo", domain = "c-yy-gz-aipoerp")
public class AssetNameLibraryVo extends SuperDO implements ICode,IEnable,IOrgItf {
    public static final String ENTITY_NAME = "ZCMCK.ZCMCK.AssetNameLibraryVo";
    public static final String NAME = "name";
    public static final String MATERIALCLASSIFY = "materialClassify";
    public static final String ASSETCLASSIFY = "assetClassify";
    public static final String SOURCE = "source";
    public static final String PRODUCTID = "productId";
    public static final String CODE = "code";
    public static final String CREATETIME = "createTime";
    public static final String CREATOR = "creator";
    public static final String DISABLETS = "disablets";
    public static final String ENABLE = "enable";
    public static final String ENABLETS = "enablets";
    public static final String ID = "id";
    public static final String MODIFIER = "modifier";
    public static final String MODIFYTIME = "modifyTime";
    public static final String ORGID = "orgId";
    public static final String PUBTS = "pubts";
    public static final String YTENANTID = "ytenantId";

    /* 资产名称 */
    private String name;
    /* 物料分类 */
    private Long materialClassify;
    /* 资产类别 */
    private String assetClassify;
    /* 来源 */
    private String source="MANUAL";
    /* 物料ID */
    private String productId;
    /* 编码 */
    private String code;
    /* 创建时间 */
    private Date createTime;
    /* 创建人 */
    private String creator;
    /* 停用时间 */
    private Date disablets;
    /* 档案状态 */
    private String enable;
    /* 启用时间 */
    private Date enablets;
    /* id */
    private String id;
    /* 修改人 */
    private String modifier;
    /* 修改时间 */
    private Date modifyTime;
    /* 主组织 */
    private String orgId;
    /* pubts */
    private Date pubts;
    /* 租户id */
    private String ytenantId;

    public void setName(String name) {
        this.name = name;
    }

    public void setMaterialClassify(Long materialClassify) {
        this.materialClassify = materialClassify;
    }

    public void setAssetClassify(String assetClassify) {
        this.assetClassify = assetClassify;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setDisablets(Date disablets) {
        this.disablets = disablets;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public void setEnablets(Date enablets) {
        this.enablets = enablets;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public void setPubts(Date pubts) {
        this.pubts = pubts;
    }

    public void setYtenantId(String ytenantId) {
        this.ytenantId = ytenantId;
    }

    public String getName() {
        return name;
    }

    public Long getMaterialClassify() {
        return materialClassify;
    }

    public String getAssetClassify() {
        return assetClassify;
    }

    public String getSource() {
        return source;
    }

    public String getProductId() {
        return productId;
    }

    public String getCode() {
        return code;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getCreator() {
        return creator;
    }

    public Date getDisablets() {
        return disablets;
    }

    public String getEnable() {
        return enable;
    }

    public Date getEnablets() {
        return enablets;
    }

    public String getId() {
        return id;
    }

    public String getModifier() {
        return modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public String getOrgId() {
        return orgId;
    }

    public Date getPubts() {
        return pubts;
    }

    public String getYtenantId() {
        return ytenantId;
    }

    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
