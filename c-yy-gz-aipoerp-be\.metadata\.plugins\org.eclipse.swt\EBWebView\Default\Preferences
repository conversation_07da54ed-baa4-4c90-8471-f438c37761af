{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"available_dark_theme_options": "All", "has_seen_welcome_page": false, "recent_theme_color_list": [**********.0, **********.0, **********.0, **********.0, **********.0], "user_level_features_context": {}}, "browser_content_container_height": 83, "browser_content_container_width": 600, "browser_content_container_x": 0, "browser_content_container_y": 0, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "credentials_enable_service": false, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "edge": {"bookmarks": {"last_dup_info_record_time": "13397194151754214"}, "msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_sso_info": {"is_msa_first_profile": true, "msa_sso_algo_state": 1}, "services": {"signin_scoped_device_id": "631478e5-c45d-46a0-918a-aa544463e938"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "refresh_status_muted_until": "13397798941719262"}, "edge_ux_config": {"assignmentcontext": "", "dataversion": "0", "experimentvariables": {}, "flights": {}}, "edge_wallet": {"passwords": {"password_lost_report_date": "13397194171809187"}}, "enterprise_profile_guid": "9d36242c-d571-41ac-8ace-6ba5ce541558", "extension": {"installed_extension_count": 2}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.3351.83", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "fsd": {"retention_policy_last_version": 138}, "intl": {"selected_languages": "zh-CN,en,en-GB,en-US"}, "media": {"engagement": {"schema_version": 5}}, "muid": {"last_sync": "13397194141743102", "values_seen": []}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": true, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"file:///*,*": {"last_modified": "13397214135031161", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.0.3296.83", "creation_time": "13394509140554784", "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "943d4c45-fd0b-4aab-b5d7-9ef87ea3122f", "exit_type": "Normal", "has_seen_signin_fre": false, "is_relative_to_aad": false, "last_time_obsolete_http_credentials_removed": 1750035790.081466, "last_time_password_store_metrics_reported": 1752720571.808553, "managed_user_id": "", "name": "用户配置 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_137.0.3296.83": 2.0, "feedback_rating_in_product_help_observed_session_time_key_138.0.3351.83": 0.0}, "password_hash_data_list": [], "signin_fre_seen_time": "13394509140565347", "were_old_google_logins_removed": true}, "reset_prepopulated_engines": false, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "sessions": {"event_log": [{"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13396083789654860", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396084753331996", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13396098160079899", "type": 2, "window_count": 0}, {"crashed": false, "time": "13396413307065257", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13396413307303198", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396523824710887", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13396523824949469", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396582181499798", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13396582181635205", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396671260304731", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13396671260448228", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396934407701569", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13396934407866577", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397014604742509", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13397014604885732", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397101053235295", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 1}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["zh-CN"]}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}}