package com.yonyou.ucf.mdf.iris.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/16 10:03
 * @DESCRIPTION 类描述
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.bank")
public class BankConfig {
    private String pfxPath;
    private String cerPath;
    private String password;
    private String url;
    private Integer port;
    private String custNo;
    private String custChnl;
}
