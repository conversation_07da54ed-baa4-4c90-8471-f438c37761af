package com.yonyou.ucf.mdf.aipo.service;

import com.yonyou.ucf.mdf.aipo.model.EventType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 凭证事件处理器工厂
 */
@Component
public class VoucherEventProcessorFactory {
    private final Map<EventType, IVoucherEventProcessService> processors = new ConcurrentHashMap<>();

    @Autowired
    public void setProcessors(IVoucherEventProcessService... processors) {
        for (IVoucherEventProcessService processor : processors) {
            if (processor instanceof VoucherEventProcessor) {
                VoucherEventProcessor voucherProcessor = (VoucherEventProcessor) processor;
                this.processors.put(voucherProcessor.getSupportedEventType(), processor);
            }
        }
    }

    public IVoucherEventProcessService getProcessor(EventType eventType) {
        return processors.get(eventType);
    }
}
