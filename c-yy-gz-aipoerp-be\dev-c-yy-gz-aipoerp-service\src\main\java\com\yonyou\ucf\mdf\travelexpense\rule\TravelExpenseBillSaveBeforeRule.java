package com.yonyou.ucf.mdf.travelexpense.rule;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ucf.mdf.travelexpense.service.IBustypeQryService;
import com.yonyou.ucf.mdf.travelexpense.service.IConsumeKindQryService;
import com.yonyou.ucf.mdf.travelexpense.service.IReceptionApplyQryService;
import com.yonyou.ucf.mdf.travelexpense.service.ITravelExpenseQryService;
import com.yonyou.ypd.bill.basic.entity.convert.EntityConverter;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;

import cn.hutool.core.date.DateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年3月25日
 */
@Component("travelExpenseBillSaveBeforeRule")
@Slf4j
public class TravelExpenseBillSaveBeforeRule implements IYpdCommonRul {

	@Autowired
	private IConsumeKindQryService consumeKindQryService;
	@Autowired
	private IReceptionApplyQryService receptionApplyQryService;
	@Autowired
	private IBustypeQryService bustypeQryService;
	@Autowired
	private ITravelExpenseQryService travelExpenseQryService;
	@Autowired
	private EntityConverter entityConverter;

	@SneakyThrows
	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {

		log.error("-------------进入差旅报销单保存前规则-------------start");

		String action = rulCtxVO.getAction();
		if (!"save".equals(action)) {
			log.error("action命令不是save命令：{}", action);
			return null;
		}

		List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
		if (CollectionUtils.isEmpty(bills)) {
			log.error("获取bills对象为空");
			return null;
		}

		BizObject bizObject = bills.get(0);

		String bustype = bizObject.getString("bustype");
		String busTypeCode = bustypeQryService.queryBusTypeCodeById(bustype);

		if (!"RBSM00601".equals(busTypeCode)) {
			return null;
		}

		Object id = bizObject.get("id");
		BizObject oldBizObject = null;
		if (id != null && id.toString().length() > 0) {
			// 如果id不为空的情况下，需要重新查询出单据，因为修改保存的时候，这个单据的数据不全
			oldBizObject = getBizObjectById(id.toString());
		}

//        log.error("获取业务对象：{}", JSONUtil.toJson(bizObject));

		checkTravelExpenseBill(bizObject, oldBizObject);

		log.error("-------------进入差旅报销单保存前规则-------------end");
		return null;
	}

	/**
	 * 根据id查询差旅费报销单
	 *
	 * @param string
	 * @return
	 */
	private BizObject getBizObjectById(String id) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("*");
		QuerySchema sonSchema1 = QuerySchema.create().name("expinvoicedetails").addSelect("*");
		QuerySchema grandsonSchema = QuerySchema.create().name("TravelExpInvoiceDetailVO_extend3List").addSelect("*")
				.addSelect(new QueryField("extend3", "extend3", null, "bd.staff.StaffNew/id"))
				.addSelect("extend3.code as psnCode, extend3.name as psnName, extend3.id as psnId");
		sonSchema1.addCompositionSchema(grandsonSchema);
		QuerySchema sonSchema2 = QuerySchema.create().name("expensebillbs").addSelect("*");
		schema.addCompositionSchema(sonSchema1);
		schema.addCompositionSchema(sonSchema2);
		schema.addCondition(QueryConditionGroup.and(QueryCondition.name("id").eq(id)));
		List<Map<String, Object>> result = travelExpenseQryService.queryBySchema(schema);
		if (result.isEmpty()) {
			return null;
		}
		return entityConverter.convertMapToBizObject(result.get(0));
	}

	/**
	 * <pre>
	 * 校验差旅报销单，检查报销人是否已经报销了招待费
	 * 逻辑:
	 * 1、差旅人员取报销明细任意一行报销人员和账单明细每一行同餐人（多选）
	 * 2、校验日期取每一行账单发生日期
	 * 3、费用分别需要校验账单明细对应每一行的中、晚餐是否已经在发生日期报销了招待费报销单
	 * 对应费用项目：
	 * 070201 餐饮费-中餐（含酒）
	 * 070202 餐饮费-晚餐（含酒）
	 * 070203 餐饮费-中餐（不含酒）
	 * 070204 餐饮费-晚餐（不含酒）
	 * </pre>
	 *
	 * @param bizObject
	 * @param oldBizObject TODO
	 */
	private void checkTravelExpenseBill(BizObject bizObject, BizObject oldBizObject) {
		/**
		 * <pre>
		 * 代码逻辑：
		 * 1、首先取出报销人，因为在校验每一行账单明细的时候要带上报销人
		 * 2、过滤出账单消费类型为【餐饮】且中餐或晚餐填了费用的账单明细行
		 * 3、循环取每一行账单明细发生日期和对应的同餐人。
		 * 以发生日期为key，同餐人为value，构建Map，目的是拿到相关参数，一次性查询所有招待费报销单。
		 * 避免账单明细很多时循环查询效率低下
		 * 4、再次循环账单明细，根据查询出的招待费报销单去校验账单明细中、晚餐费用是否已经报销过，记录校验结果
		 * 5、如果校验结果存在校验失败的情况，则抛出异常
		 * </pre>
		 */
		/** ----- 1、首先取出报销明细报销人（表头的报销人可能是代报销人，所以要去明细里的报销人）----start **/
		// 报销人主键
		String pk_handlepsn = getHandlepsn(bizObject, oldBizObject);
		if (StringUtils.isBlank(pk_handlepsn)) {
			log.error("获取报销明细报销热人为空，跳过");
			return;
		}
		/** ----- 1、首先取出报销明细报销人（表头的报销人可能是代报销人，所以要去明细里的报销人）----end **/

		/** ----- 2、过滤出账单消费类型为【餐饮】且中餐或晚餐填了费用的账单明细行---------start **/
		List<BizObject> expinvoicedetails = bizObject.get("expinvoicedetails");
		if (CollectionUtils.isEmpty(expinvoicedetails)) {
			log.error("账单明细行数据为空，跳过");
			return;
		}
		Map<String, BizObject> oldExpinvoicedetailMap = getOldExpinvoicedetailMap(oldBizObject);
		// 餐饮、食品消费类型编码（系统预置的类型，改动可能性较小）
		List<String> consumeKindCodes = Arrays.asList("YZ001", "YZ009");
		List<String> consumeKindIds = consumeKindQryService.queryIdByCodes(consumeKindCodes); // 查询出消费类型【餐饮、食品】id
		expinvoicedetails = expinvoicedetails.stream().filter(biz -> {
			BizObject oldExpinvoicedetail = oldExpinvoicedetailMap.get(biz.getString("id"));
			String pk_consumekind = getConsumeKindId(biz, oldExpinvoicedetail);
			return consumeKindIds.contains(pk_consumekind);
		}).collect(Collectors.toList());
		if (expinvoicedetails.isEmpty()) {
			log.error("不存在消费类型为【餐饮、食品】的账单明细，跳过，bizObject：{}，oldBizObject：{}", JSONUtil.toJson(bizObject),
					JSONUtil.toJson(oldBizObject));
			return;
		}
		/** ----- 2、过滤出账单消费类型为【餐饮】且中餐或晚餐填了费用的账单明细行---------end **/

		/** ----- 3、循环取每一行账单明细发生日期和对应的同餐人---------start **/
		Map<String, Set<String>> handlepsnMap = Maps.newHashMap();
		Map<String/* 日期 */, Map<String/* 餐次 */, Set<String/* 人员 */>>> feeMap = Maps.newHashMap(); // 餐次
		for (BizObject expinvoicedetail : expinvoicedetails) {
			BizObject oldExpinvoicedetail = oldExpinvoicedetailMap.get(expinvoicedetail.getString("id"));
			Date date = getDcostdate(expinvoicedetail, oldExpinvoicedetail);
			if (date == null) {
				// 如果发生日期是空的，直接跳过
				continue;
			}
			String dcostdate = DateUtil.format(date, "yyyy-MM-dd");

			String status = expinvoicedetail.getString("_status");
			if (status != null && status.equals("Delete")) {
				continue;
			}

			BizObject fee = expinvoicedetail.getBizObject("expinvoicedetailDcs", BizObject.class);
			if (fee == null) {
				continue;
			}

			List<BizObject> extend3List = expinvoicedetail.getBizObjects("TravelExpInvoiceDetailVO_extend3List",
					BizObject.class);
			Set<String> psns = Sets.newHashSet();
			psns.add(pk_handlepsn);
			handlepsnMap.computeIfAbsent(dcostdate, k -> Sets.newHashSet()).add(pk_handlepsn);
			if (CollectionUtils.isNotEmpty(extend3List)) { // 如果有选择陪同人员
				List<String> extend3Ids = extend3List.stream().filter(biz -> {
					String _status = biz.getString("_status");
					return !"Delete".equals(_status);
				}).map(biz -> biz.getString("extend3")).collect(Collectors.toList());
				psns.addAll(extend3Ids);
				handlepsnMap.computeIfAbsent(dcostdate, k -> Sets.newHashSet()).addAll(extend3Ids);
			}

			BigDecimal breakfastFee = fee.getBigDecimal("BX14"); // 早餐费
			BigDecimal lunchFee = fee.getBigDecimal("BX15"); // 中餐费
			BigDecimal dinnerFee = fee.getBigDecimal("BX16"); // 晚餐费
			if (breakfastFee != null && breakfastFee.compareTo(BigDecimal.ZERO) > 0) {
				Map<String/* 餐次 */, Set<String/* 人员 */>> feePsn = feeMap.get(dcostdate);
				if (feePsn == null) {
					feePsn = Maps.newHashMap();
					feeMap.put(dcostdate, feePsn);
				}
				feePsn.computeIfAbsent("1", k -> Sets.newHashSet()).addAll(psns);
			}
			if (lunchFee != null && lunchFee.compareTo(BigDecimal.ZERO) > 0) {
				Map<String/* 餐次 */, Set<String/* 人员 */>> feePsn = feeMap.get(dcostdate);
				if (feePsn == null) {
					feePsn = Maps.newHashMap();
					feeMap.put(dcostdate, feePsn);
				}
				feePsn.computeIfAbsent("2", k -> Sets.newHashSet()).addAll(psns);
			}
			if (dinnerFee != null && dinnerFee.compareTo(BigDecimal.ZERO) > 0) {
				Map<String/* 餐次 */, Set<String/* 人员 */>> feePsn = feeMap.get(dcostdate);
				if (feePsn == null) {
					feePsn = Maps.newHashMap();
					feeMap.put(dcostdate, feePsn);
				}
				feePsn.computeIfAbsent("3", k -> Sets.newHashSet()).addAll(psns);
			}
		}
		/** ----- 3、循环取每一行账单明细发生日期和对应的同餐人---------end **/

		/** ----- 4、根据账单发生日期和陪同人员去查询招待费申请单，并根据是否存在招待费申请单进行校验---------start **/
		List<Map<String, Object>> receptionApplyList = receptionApplyQryService
				.queryByDcostdateAndHandlepsn(handlepsnMap);
		if (!receptionApplyList.isEmpty()) {
			log.error("--------receptionApplyList:{}", JSONUtil.toJson(receptionApplyList));
			StringBuilder errMsg = new StringBuilder();
			errMsg.append("以下人员已经存在招待费申请单:\n");
			boolean hasError = false;
			for (String key : handlepsnMap.keySet()) {
				Map<String/* 餐次 */, Set<String/* 人员 */>> fee = feeMap.get(key); // 餐次
				if (MapUtils.isEmpty(fee)) {
					continue;
				}
				Set<String> emsg = Sets.newHashSet();
				for (String f : fee.keySet()) {
					Set<String/* 人员 */> psn = fee.get(f);
					String msg = checkError(key, psn, f, receptionApplyList);
					if (StringUtils.isNotBlank(msg) && !emsg.contains(msg)) {
						emsg.add(msg);
						errMsg.append(msg);
						hasError = true;
					}
				}
			}
			if (hasError) {
				throw new RuntimeException(errMsg.toString());
			}
		}

		/**
		 * -----
		 * 5、根据账单发生日期和陪同人员去查询差旅费报销单，并根据是否存在差旅费报销单进行校验（如果是更新的情况，要排除当前单据）---------start
		 **/

		List<Map<String, Object>> travelExpenseList = travelExpenseQryService
				.queryByDcostdateAndHandlepsn(handlepsnMap);
		if (!travelExpenseList.isEmpty()) {
			Object id = bizObject.get("id");
			if (id != null) {
				travelExpenseList = travelExpenseList.stream().filter(m -> {
					return !id.toString().equals(m.getOrDefault("id", "").toString());
				}).collect(Collectors.toList()); // 过滤掉当前单据
			}
			if (travelExpenseList.isEmpty()) {
				return;
			}
			log.error("--------travelExpenseList:{}", JSONUtil.toJson(travelExpenseList));
			StringBuilder errMsg = new StringBuilder();
			errMsg.append("以下人员已经存在差旅费报销单:\n");
			boolean hasError = false;
			for (String key : handlepsnMap.keySet()) {
				Map<String/* 餐次 */, Set<String/* 人员 */>> fee = feeMap.get(key); // 餐次
				if (MapUtils.isEmpty(fee)) {
					continue;
				}
				Set<String> emsg = Sets.newHashSet();
				String error = checkError2(key, fee, travelExpenseList);
				if (StringUtils.isNotBlank(error) && !emsg.contains(error)) {
					emsg.add(error);
					errMsg.append(error);
					hasError = true;
				}
			}
			if (hasError) {
				throw new RuntimeException(errMsg.toString());
			}
		}
	}

	/**
	 * 获取账单发生日期
	 * 
	 * @param expinvoicedetail
	 * @param oldExpinvoicedetail
	 * @return
	 */
	private Date getDcostdate(BizObject expinvoicedetail, BizObject oldExpinvoicedetail) {
		if (expinvoicedetail != null && expinvoicedetail.getDate("dcostdate") != null) {
			return expinvoicedetail.getDate("dcostdate");
		}
		if (oldExpinvoicedetail != null && oldExpinvoicedetail.getDate("dcostdate") != null) {
			return oldExpinvoicedetail.getDate("dcostdate");
		}
		return null;
	}

	/**
	 * 获取账单类型id
	 * 
	 * @param expinvoicedetail
	 * @param oldExpinvoicedetail
	 * @return
	 */
	private String getConsumeKindId(BizObject expinvoicedetail, BizObject oldExpinvoicedetail) {
		if (expinvoicedetail != null && expinvoicedetail.getString("pk_consumekind") != null) {
			return expinvoicedetail.getString("pk_consumekind");
		}
		if (oldExpinvoicedetail != null && oldExpinvoicedetail.getString("pk_consumekind") != null) {
			return oldExpinvoicedetail.getString("pk_consumekind");
		}
		return null;
	}

	/**
	 * 获取旧账单明细数据
	 * 
	 * @param oldBizObject
	 * @return
	 */
	private Map<String, BizObject> getOldExpinvoicedetailMap(BizObject oldBizObject) {
		if (oldBizObject == null) {
			return Collections.emptyMap();
		}
		List<BizObject> oldexpinvoicedetails = oldBizObject.get("expinvoicedetails");
		if (CollectionUtils.isNotEmpty(oldexpinvoicedetails)) {
			return oldexpinvoicedetails.stream().collect(Collectors.toMap(b -> {
				return b.get("id").toString();
			}, b -> b, (b1, b2) -> b2));
		}
		return Collections.emptyMap();
	}

	/**
	 * 获取报销明细报销人主键
	 * 
	 * @param bizObject
	 * @param oldBizObject
	 * @return
	 */
	private String getHandlepsn(BizObject bizObject, BizObject oldBizObject) {
		List<BizObject> expensebillbs = bizObject.get("expensebillbs");
		if (CollectionUtils.isEmpty(expensebillbs) || expensebillbs.get(0).getString("pk_handlepsn") == null) {
			expensebillbs = oldBizObject.get("expensebillbs");
		}
		if (CollectionUtils.isEmpty(expensebillbs)) {
			return null;
		}
		return expensebillbs.get(0).getString("pk_handlepsn");
	}

	private String checkError2(String key, Map<String, Set<String>> fee, List<Map<String, Object>> travelExpenseList) {
		StringBuilder errMsg = new StringBuilder();
		Set<String> errs = Sets.newHashSet();
		for (Map<String, Object> map : travelExpenseList) {
			JSONObject bill = JSONObject.parseObject(JSONUtil.toJson(map));
			JSONArray bvos = bill.getJSONArray("expensebillbs");
			JSONObject bvo = bvos.getJSONObject(0); // 取一条报销明细子表
			String pk_handlepsn = bvo.getString("pk_handlepsn"); // 报销人
			String psnName = bvo.getString("psnName"); // 报销人名称
			JSONArray expinvoicedetails = bill.getJSONArray("expinvoicedetails"); // 账单明细子表
			String code = bill.getString("code");
			String creator = bill.getString("creator");
			if (expinvoicedetails == null || expinvoicedetails.size() == 0) {
				continue;
			}
			Iterator<Object> it = expinvoicedetails.iterator();
			while (it.hasNext()) {
				Object b = it.next();
				JSONObject bjson = JSONObject.parseObject(JSONUtil.toJson(b));

				Date jdr = bjson.getDate("dcostdate"); // 发生日期
				String jdrStr = DateUtil.format(jdr, "yyyy-MM-dd");
				if (StringUtils.isEmpty(jdrStr) || !key.equals(jdrStr)) {
					continue;
				}

				JSONObject expinvoicedetailDcs = bjson.getJSONObject("expinvoicedetailDcs");
				if (expinvoicedetailDcs == null) {
					continue;
				}

				// 把餐次提出来
				Set<String> feeHas = Sets.newHashSet();
				BigDecimal breakfastFee = expinvoicedetailDcs.getBigDecimal("BX14"); // 早餐费
				BigDecimal lunchFee = expinvoicedetailDcs.getBigDecimal("BX15"); // 中餐费
				BigDecimal dinnerFee = expinvoicedetailDcs.getBigDecimal("BX16"); // 晚餐费
				if (breakfastFee != null && breakfastFee.compareTo(BigDecimal.ZERO) > 0) {
					feeHas.add("1");
				}
				if (lunchFee != null && lunchFee.compareTo(BigDecimal.ZERO) > 0) {
					feeHas.add("2");
				}
				if (dinnerFee != null && dinnerFee.compareTo(BigDecimal.ZERO) > 0) {
					feeHas.add("3");
				}
				if (feeHas.isEmpty()) {
					continue;
				}

				String err = checkError(pk_handlepsn, feeHas, fee);

				if (StringUtils.isNotBlank(err) && !errs.contains(err)) {
					errs.add(err);
					errMsg.append("【人员：");
					errMsg.append(psnName);
					errMsg.append("，餐次：");
					errMsg.append(err);
					errMsg.append("，账单发生日期：");
					errMsg.append(key);
					errMsg.append("，差旅费报销单：");
					errMsg.append(code);
					errMsg.append("，创建人：");
					errMsg.append(creator);
					errMsg.append("】\n");
				}
				JSONArray extend3List = bjson.getJSONArray("TravelExpInvoiceDetailVO_extend3List"); // 陪同人
				if (extend3List != null) {
					for (int i = 0; i < extend3List.size(); i++) {
						JSONObject extend3 = extend3List.getJSONObject(i);
						String errF = checkError(extend3.getString("extend3"), feeHas, fee);
						if (StringUtils.isNotBlank(errF) && !errs.contains(errF)) {
							errs.add(errF);
							String name = extend3.getString("extend3_name");
							errMsg.append("【人员：");
							errMsg.append(name);
							errMsg.append("，餐次：");
							errMsg.append(errF);
							errMsg.append("，账单发生日期：");
							errMsg.append(key);
							errMsg.append("，差旅费报销单：");
							errMsg.append(code);
							errMsg.append("，创建人：");
							errMsg.append(creator);
							errMsg.append("】\n");
						}
					}
				}
			}
		}
		return errMsg.toString();
	}

	/**
	 * 检查是否已经存在差旅费报销餐次报销
	 * 
	 * @param pk_handlepsn 人员
	 * @param feeHas       餐次
	 * @param fee          餐次人员
	 * @return
	 */
	private String checkError(String pk_handlepsn, Set<String> feeHas, Map<String, Set<String>> fee) {
		Set<String> ff = Sets.newHashSet();
		for (String f : feeHas) {
			Set<String> psn = fee.get(f);
			if (CollectionUtils.isNotEmpty(psn)) {
				if (psn.contains(pk_handlepsn)) {
					ff.add(f);
				}
			}
		}
		if (!ff.isEmpty()) {
			return ff.stream().map(this::getFeeName).collect(Collectors.joining("/"));
		}
		return null;
	}

	/**
	 * 检查是否已经存在招待费申请单
	 *
	 * @param key                招待日期
	 * @param psn                人员
	 * @param f                  餐次
	 * @param receptionApplyList 招待费申请单
	 * @return
	 */
	private String checkError(String key, Set<String/* 人员 */> psn, String f,
			List<Map<String, Object>> receptionApplyList) {
		for (Map<String, Object> map : receptionApplyList) {
			JSONObject bill = JSONObject.parseObject(JSONUtil.toJson(map));
			JSONArray bvos = bill.getJSONArray("MemoapplyBVO");
			String code = bill.getString("code");
			String creator = bill.getString("creator");
			if (bvos == null || bvos.size() == 0) {
				continue;
			}
			Iterator<Object> it = bvos.iterator();
			while (it.hasNext()) {
				Object b = it.next();
				JSONObject bjson = JSONObject.parseObject(JSONUtil.toJson(b));
				JSONArray extend3List = bjson.getJSONArray("CommonMemoApplyBVO_extend3List");
				JSONObject memoapplyBDcs = bjson.getJSONObject("memoapplyBDcs");
				String cf = bjson.getString("extend4"); // 餐次
				if (memoapplyBDcs == null || StringUtils.isEmpty(cf) || !cf.equals(f)) {
					continue;
				}
				Date jdr = memoapplyBDcs.getDate("BX18"); // 接待日
				String jdrStr = DateUtil.format(jdr, "yyyy-MM-dd");
				if (StringUtils.isEmpty(jdrStr) || !key.equals(jdrStr)) {
					continue;
				}
				for (int i = 0; i < extend3List.size(); i++) {
					JSONObject extend3 = extend3List.getJSONObject(i);
					if (psn.contains(extend3.getString("extend3"))) {
						String name = extend3.getString("extend3_name");
						StringBuilder errMsg = new StringBuilder();
						errMsg.append("【人员：");
						errMsg.append(name);
						errMsg.append("，餐次：");
						errMsg.append(getFeeName(f));
						errMsg.append("，接待日期：");
						errMsg.append(key);
						errMsg.append("，招待费申请单：");
						errMsg.append(code);
						errMsg.append("，创建人：");
						errMsg.append(creator);
						errMsg.append("】\n");
						return errMsg.toString();
					}
				}
			}
		}
		return null;
	}

	/**
	 * 根据餐次枚举获取餐次名称
	 *
	 * @param f
	 * @return
	 */
	private String getFeeName(String f) {
		switch (f) {
		case "1":
			return "早餐";
		case "2":
			return "中餐";
		case "3":
			return "晚餐";
		default:
			break;
		}
		return null;
	}

}
