<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
	<localRepository>D:\MavenRepository</localRepository>
    <pluginGroups>
    </pluginGroups>
    <proxies>
    </proxies>
    <servers>
	  <server>
            <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>nccloud-snapshots</id>
        </server>
        <server>
             <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>nccloud-stagings</id>
        </server>
        <server>
             <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>nccloud-releases</id>
        </server>
        <server>
            <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>YonyouCloud-Release</id>
        </server>
        <server>
            <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>YonyouCloud-Snapshot</id>
        </server>
        <server>
            <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>iUAP</id>
        </server>
		<server>
            <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>iUAP-Snapshots</id>
        </server>
        <server>
            <username>lanlin</username>
            <password>wDDHS@1381795790</password>
            <id>iUAP-Stagings</id>
        </server>
    </servers>
      <profiles>
        <profile>
            <repositories>
                 <repository>
                    <id>jfrog</id>
                    <name>jfrog</name>
                    <url>https://repo.yyrd.com/artifactory/yonyou-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
           </repositories>
            <pluginRepositories>
                <pluginRepository>
                   <releases>
                     <enabled>true</enabled>
                   </releases>
                   <snapshots>
                      <enabled>true</enabled>
                    </snapshots>
                    <id>jfrog</id>
                    <name>jfrog</name>
                    <url>https://repo.yyrd.com/artifactory/yonyou-public/</url>
                </pluginRepository>
            </pluginRepositories>
            <id>jfrog</id>
        </profile>
     </profiles>
        <activeProfiles>  
		<activeProfile>jfrog</activeProfile>		
	</activeProfiles>
</settings>