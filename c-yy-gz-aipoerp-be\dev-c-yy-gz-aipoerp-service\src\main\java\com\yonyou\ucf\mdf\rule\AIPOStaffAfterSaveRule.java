package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.entity.AIPONCStaffSyncTaskLogDetail;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IStaffService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 员工保存后自动同步规则
 * 当员工信息保存后，自动将员工信息同步到NC高级版系统
 */
@Slf4j
@Component("aipoStaffAfterSaveRule")
public class AIPOStaffAfterSaveRule implements IYpdCommonRul {

    @Autowired
    IStaffService staffService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        JSONObject jsonParams = (JSONObject) JSON.toJSON(params);
        JSONObject reqReturn = jsonParams.getJSONObject("return");
        JSONObject requestData = jsonParams.getJSONObject("requestData");

        if (MapUtils.isEmpty(reqReturn)) {
            log.error("无法获取到员工保存结果同步高级版系统，请重试 params-->{}", params);
            throw new BusinessException("无法获取到员工保存结果同步高级版系统，请重试");
        } else {
            // 将保存结果转换为JSONObject
            JSONObject bipStaffInfo = JSON.parseObject(JSON.toJSONString(reqReturn));

            // 创建任务日志明细对象，用于记录同步结果
            AIPONCStaffSyncTaskLogDetail taskLogDetail = new AIPONCStaffSyncTaskLogDetail();
            taskLogDetail.setDataType("2"); // 2表示单条数据的任务日志

            // 调用服务推送员工信息到NC（taskLogDetail会在服务内部被填充）
            staffService.pushStaffInfoToNC(bipStaffInfo, taskLogDetail);

            log.info("员工保存后自动同步到高级版系统完成，员工ID: {}, 员工姓名: {}, 同步结果: {}",
                    bipStaffInfo.getString("id"),
                    bipStaffInfo.getString("name"),
                    "1".equals(taskLogDetail.getSyncResult()) ? "成功" : "失败");
        }

        return result;
    }
}
