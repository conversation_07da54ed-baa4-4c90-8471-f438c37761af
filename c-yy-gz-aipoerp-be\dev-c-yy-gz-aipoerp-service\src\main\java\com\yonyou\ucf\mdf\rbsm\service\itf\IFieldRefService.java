package com.yonyou.ucf.mdf.rbsm.service.itf;

import java.util.List;
import java.util.Map;

import com.yonyou.ucf.mdf.rbsm.model.FieldRef;

/**
 * 查询字段映射接口
 * 
 * <AUTHOR>
 *
 *         2025年3月10日
 */
public interface IFieldRefService {

	/**
	 * 查询所有字段映射
	 * 
	 * @return
	 */
	Map<String, FieldRef> getFieldRef();

	/**
	 * 根据客开配置字段获取
	 * 
	 * @param kkField
	 * @return
	 */
	FieldRef getFieldRefByKkField(String kkField);

	/**
	 * 根据客开字段批量获取
	 * 
	 * @param kkFields
	 * @return
	 */
	List<FieldRef> getFieldRefByKkFields(List<String> kkFields);

	/**
	 * 获取字段映射，客开字段作为key，映射字段作为value返回
	 * 
	 * @return
	 */
	Map<String, String> getFieldRefKkKey();

}
