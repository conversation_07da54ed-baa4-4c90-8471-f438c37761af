package com.yonyou.ucf.mdf.rbsm.model;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 通用报销单保存接口调用对象
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CommonExpenseBillVO {
	private String id; // 单据id(_status为Update更新时必填)
	private String dcostdate; // 是 发生日期(格式：yyyy-MM-dd) 示例：2021-09-26
	private String vouchdate; // 是 单据日期(格式：yyyy-MM-dd) 示例：2021-09-26
	private String vfinacedeptid; // 是 费用承担部门(支持id和code) 示例：****************
	private String cfinaceorg; // 是 费用承担组织(支持id和code) 示例：****************
	private String caccountorg; // 是 会计主体(支持id和code) 示例：****************
	private String bustype; // 是 交易类型(支持id和code) 示例：****************
	private String vcurrency = "CNY"; // 是 原币(支持id和code) 示例：****************
	private String pk_handlepsn; // 是 报销人(支持id和code) 示例：****************
	private String vhandledeptid; // 是 报销人部门(支持id和code) 示例：****************
	private String chandleorg; // 是 报销人组织(支持id和code) 示例：****************
	private String vnatcurrency = "CNY"; // 是 组织本币(支持id和code) 示例：****************
	private String vnatexchratetype = "01"; // 是 组织本币汇率类型(支持id和code) 示例：py7y8nze
	private String dnatexchratedate; // 是 组织本币汇率日期(格式：yyyy-MM-dd) 示例：2021-09-26
	private String nnatbaseexchrate = "1"; // 是 组织本币企业汇率 示例：1
	private String nnatexchrate = "1"; // 是 组织本币汇率 示例：1
	private String nexpensemny; // 是 不含税总额 示例：133.74
	private String nnatexpensemny; // 是 不含税总额-本币 示例：129.87
	private String nsummny; // 是 报销价税总额 示例：133.74
	private String nnatsummny; // 是 报销价税总额-本币 示例：133.74
	private String nshouldpaymny; // 是 应付总额(应付总额=报销价税总额-核销总额) 示例：1
	private String nnatshouldpaymny; // 是 应付总额-本币(应付总额-本币=报销价税总额-本币 - 核销总额-本币) 示例：1
	private String pk_cusdoc; // 否 供应商id 示例：2185034480177408
	private String pk_cusdoc_code; // 否 供应商编码
	private String vreason; // 是 报销说明 示例：测试
	private String creatorId; // 否 创建人id(不传值默认YonSuite默认用户) 示例：2185004199465216 接口测试下来创建人id和编码至少有一个不能为空
	private String creator_code; // 否 创建人编码(不传值默认YonSuite默认用户)
	private String _status = "Insert"; // 是 操作标识, Insert:新增、Update:更新 示例:Insert 示例：Insert
	private List<Expensebillb> expensebillbs; // 报销明细
	private List<Expapportion> expapportions; // 分摊明细
	private List<Expsettleinfo> expsettleinfos; // 结算明细

	private JSONObject expensebillDcs; // 特征组自定义字段

	private String extend2; // 期间-扩展字段
	private String extend3; // 纳税期间-扩展字段
}