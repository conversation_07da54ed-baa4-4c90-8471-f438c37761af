package com.yonyou.ucf.mdf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.enterBankAcc.EnterBankBill;
import com.yonyou.aipierp.dto.ncapi.enterBankAcc.EnterBankBillHead;
import com.yonyou.aipierp.dto.ncapi.enterBankAcc.EnterBankCurrencyAcc;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IEnterBankAccService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import com.yonyou.ypd.bill.basic.entity.WeakTypingDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class EnterBankServiceImpl implements IEnterBankAccService {

    NCOpenApiConfig ncOpenApiConfig;
    NCOpenApiService ncOpenApiService;
    AIPORepository aipoRepository;
    SimpleDateFormat bankDateFormat;
    SimpleDateFormat bankDateTimeFormat;

    public EnterBankServiceImpl(NCOpenApiConfig ncOpenApiConfig,
                                NCOpenApiService ncOpenApiService,
                                AIPORepository aipoRepository) {
        this.ncOpenApiConfig = ncOpenApiConfig;
        this.ncOpenApiService = ncOpenApiService;
        this.aipoRepository = aipoRepository;
        this.bankDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        this.bankDateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    @Override
    public JSONObject pushEnterBankToNC(JSONObject oriSaveReq, JSONObject saveRuleReturn) {
        String id = oriSaveReq.getString("id");
        JSONObject finalSaveEnterBank = getFinalSavedEnterBankAcc(id,oriSaveReq,saveRuleReturn);
        UFinterface uFinterface = convertToEnterBankSaveUFinterface(finalSaveEnterBank);
        JSONObject resp = ncOpenApiService.saveEnterBank(uFinterface);
        if (!resp.getBooleanValue("success") ||
                !"1".equals(resp.getJSONObject("data").getJSONObject("ufinterface").getJSONArray("sendresult").getJSONObject(0).getString("resultcode"))) {
            log.error("同步企业银行账户信息至高级版系统失败 uFinterface-->{},resp-->{}", uFinterface, resp);
            throw new BusinessException("同步企业银行账户信息至高级版系统失败，错误原因：" + resp.toJSONString());
        }
        return resp;
    }

    @Override
    public JSONObject deleteEnterBankFromNC(String enterBankId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bipid", enterBankId);
        jsonObject.put("version", "1");
        JSONObject resp = ncOpenApiService.deleteEnterBankBipId(jsonObject);
        if (!resp.getBooleanValue("success")) {
            log.error("从高级版删除企业银行账户信息失败 req-->{},resp-->{}", jsonObject, resp);
            throw new BusinessException("高级版删除企业银行账户信息失败，错误原因：" + resp);
        }
        return resp;
    }

    public JSONObject getEnterBankAccById(String enterBankId){
        WeakTypingDO enterBank = aipoRepository.queryEnterBankAccById(enterBankId);
        if (enterBank == null) return null;
        else {
            return (JSONObject) JSON.toJSON(enterBank.toMap());
        }
    }

    private UFinterface convertToEnterBankSaveUFinterface(JSONObject bipEnterBankAcc){
        // 获取翻译字段的值
        JSONObject transInfo = getTranslateField(bipEnterBankAcc);

        UFinterface uFinterface = UFinterface.init(ncOpenApiConfig.getBiz_center(),"bankaccbas",ncOpenApiConfig.getGroupcode());

        EnterBankBillHead enterBankBillHead = new EnterBankBillHead();
        enterBankBillHead.setAccclass("2"); // todo 企业银行账户没这个字段，先默认写成公司
        enterBankBillHead.setPk_org(bipEnterBankAcc.getString("orgid"));
        enterBankBillHead.setPk_group(ncOpenApiConfig.getGroupcode());
        enterBankBillHead.setAccnum(bipEnterBankAcc.getString("account"));
        enterBankBillHead.setAccname(bipEnterBankAcc.getString("acctName"));
        enterBankBillHead.setCode(bipEnterBankAcc.getString("code"));
        enterBankBillHead.setName(bipEnterBankAcc.getString("name"));
        enterBankBillHead.setPk_bankdoc(bipEnterBankAcc.getString("bankNumber"));// 开户行传主键
//        enterBankBillHead.setPk_banktype(); // 银行类别不传
        if (bipEnterBankAcc.getString("accountOpenDate")!=null){
            enterBankBillHead.setAccopendate(bankDateFormat.format(bipEnterBankAcc.getDate("accountOpenDate")));
        }
        enterBankBillHead.setArapprop(transInfo.getString("arapprop"));
        enterBankBillHead.setNetqueryflag("0"); // 网银开通状态默认0，没这个字段
        enterBankBillHead.setAccattribute(transInfo.getString("accattribute"));    // 银行账户属性
        enterBankBillHead.setGenebranprop("2");    // 总分属性默认2，没这个字段
        enterBankBillHead.setGroupaccount("Y"); // 是否集团账户，默认Y，没这个字段
        enterBankBillHead.setAccstate(transInfo.getString("accstate") );
        enterBankBillHead.setAccountproperty("0"); // 账户性质默认公司，没这个字段
        // enterBankBillHead.setAddress("");   // 地址没有这个字段
        // enterBankBillHead.setContactpsn("");    // 联系人 ，没有这个字段
        // enterBankBillHead.setTel("");    // 电话 没有这个字段
        enterBankBillHead.setMemo(bipEnterBankAcc.getString("description"));  // 备注
        // enterBankBillHead.setPk_netbankinftp();  // 网银接口类别没这个字段
        if (transInfo.getJSONObject("bankDot") != null) {
            JSONObject bankDot = transInfo.getJSONObject("bankDot");    // 企业银行账户的开户行信息
            enterBankBillHead.setCombinenum(bankDot.getString("linenumber"));
            enterBankBillHead.setBankarea(bankDot.getString("name"));
        }
        enterBankBillHead.setBankarea(transInfo.getString("province")); // 开户省
        enterBankBillHead.setCity(transInfo.getString("city")); // 开户市
        enterBankBillHead.setIssigned("N"); //签约默认为否
        enterBankBillHead.setFinanceorg(bipEnterBankAcc.getString("orgid"));
        enterBankBillHead.setControlorg(bipEnterBankAcc.getString("orgid")); // 核算归属组织，没有这个字段
        // 1-启用 2-停用
        if ("1".equals(bipEnterBankAcc.getString("enable"))){
            enterBankBillHead.setEnablestate("2");
        }else {
            enterBankBillHead.setEnablestate("3");
        }

        // 构建企业银行账户币种子表
        List<EnterBankCurrencyAcc> enterBankCurrencyAccs = new ArrayList<>();
        List<JSONObject> bipEnterBankCurrencyList = JSON.parseArray(bipEnterBankAcc.getString("currencyList"), JSONObject.class);
        bipEnterBankCurrencyList.stream().forEach(bipEnterBankCurrency->{
            EnterBankCurrencyAcc enterBankCurrencyAcc = new EnterBankCurrencyAcc();
            // 查询下币种编码
            WeakTypingDO currencyInfo = aipoRepository.queryCurrencyById(bipEnterBankCurrency.getString("currency"));
            if (currencyInfo != null) {
                enterBankCurrencyAcc.setPk_currtype(currencyInfo.getCode());
                enterBankCurrencyAcc.setCode(bipEnterBankAcc.getString("name") + "_" + currencyInfo.getCode());
            }
            enterBankCurrencyAcc.setName(bipEnterBankAcc.getString("name"));
            enterBankCurrencyAcc.setAcctype("0"); //子表账户类型，没有这个字段，默认活期
            enterBankCurrencyAcc.setIsconcerted("N");
            enterBankCurrencyAcc.setConcertedmny("0");
            enterBankCurrencyAcc.setFronzenstate("0");
//            enterBankCurrencyAcc.setFronzenmny("0");
            enterBankCurrencyAcc.setOverdrafttype("2");
            enterBankCurrencyAcc.setPayarea("0");
            enterBankCurrencyAcc.setIstrade("Y");   // 交易账户，没这个字段，默认Y
//            enterBankCurrencyAcc.setIsdefault("N"); // 默认，没有这个字段

            enterBankCurrencyAccs.add(enterBankCurrencyAcc);
        });
        JSONObject item = new JSONObject();
        item.put("item",enterBankCurrencyAccs);
        enterBankBillHead.setBankaccsub(item);

        EnterBankBill enterBankBill = new EnterBankBill();
        enterBankBill.setId(bipEnterBankAcc.getString("id"));
        enterBankBill.setBillhead(enterBankBillHead);

        uFinterface.setBill(enterBankBill);
        return uFinterface;
    }

    /**
     * 获取最终保存的企业银行帐户结果<br/>
     *  执行扩展规则时，保存事务还未结束、提交，此时查库无法查到更新后的数据，所以如果涉及子表更新，则必须拼接下 【数据库查询结果+请求requestbody+saveRuleReturn】
     * @param enterBankId 输入银行 ID
     * @param oriSaveReq  原来保存请求
     * @return {@link JSONObject }
     */
    private JSONObject getFinalSavedEnterBankAcc(String enterBankId, JSONObject oriSaveReq, JSONObject saveRuleReturn) {
        if (oriSaveReq.getString("_status") != null && !"insert".equals(oriSaveReq.getString("_status").toLowerCase())) {  // 非新增时

            // 此时说明更新了子表信息，需要把所有币种子表都补充上
            JSONObject dataBaseEnterBank = getEnterBankAccById(enterBankId);
            if (dataBaseEnterBank != null) {
                List<JSONObject> dataBaseEnterBankCurrencyList = JSON.parseArray(dataBaseEnterBank.getString("currencyList"), JSONObject.class);

                if (oriSaveReq.get("currencyList") != null) {
                    List<JSONObject> oriReqCurrencyList = JSON.parseArray(oriSaveReq.getString("currencyList"), JSONObject.class);
                    if (CollectionUtils.isNotEmpty(oriReqCurrencyList)) {
                        List<JSONObject> saveRuleReturnEnterBankCurrencyList = JSON.parseArray(saveRuleReturn.getString("currencyList"), JSONObject.class);
                        // 1,dataBaseEnterBankCurrencyList删去request中删除的currency
                        oriReqCurrencyList.stream().forEach(reqCurrency -> {
                            if (reqCurrency.getString("_status").toLowerCase().equals("delete")) {
                                String deleteId = reqCurrency.getString("id");
                                dataBaseEnterBankCurrencyList.removeIf(dataBaseCurrency -> dataBaseCurrency.getString("id").equals(deleteId));
                                saveRuleReturnEnterBankCurrencyList.removeIf(saveReturnCurrency -> saveReturnCurrency.getString("id").equals(deleteId));
                            }
                        });
                        // 2,saveReturn中的所有币种，相同id覆盖到dataBaseEnterBankCurrencyList 中，不能覆盖就新增
                        saveRuleReturnEnterBankCurrencyList.stream().forEach(saveReturnCurrency -> {
                            boolean saveReturnOverWrite = false;
                            for (int i = 0; i < dataBaseEnterBankCurrencyList.size(); i++) {
                                JSONObject dataBaseCurrency = dataBaseEnterBankCurrencyList.get(i);
                                if (dataBaseCurrency.getString("id").equals(saveReturnCurrency.getString("id"))) {
                                    dataBaseEnterBankCurrencyList.set(i, saveReturnCurrency);
                                    saveReturnOverWrite = true;
                                    break;
                                }
                            }
                            if (!saveReturnOverWrite) {
                                dataBaseEnterBankCurrencyList.add(saveReturnCurrency);
                            }
                        });
                    }
                }
                // 3，把databaseCurrency全部替换到saveReturnCurrency中
                saveRuleReturn.put("currencyList", dataBaseEnterBankCurrencyList);
            }
        }
        return saveRuleReturn;
    }

    private JSONObject getCurrencyInfoById(String currencyId){
        WeakTypingDO currency = aipoRepository.queryCurrencyById(currencyId);
        return (JSONObject) JSON.toJSON(currency.toMap());
    }

    private JSONObject getTranslateField(JSONObject bipEnterBankAcc){
        JSONObject jsonObject = new JSONObject();
        // 先翻译一波数据
        // 翻译账户用途
        WeakTypingDO accPurpose = aipoRepository.queryAccountPurposeById("accountPurpose");
        String arapprop = null;
        if (accPurpose!=null){
            // 01-收入 02-支出 03-收支
            if ("01".equals(accPurpose.get("code").toString())){
                arapprop = "0";
            }else if ("02".equals(accPurpose.get("code").toString())){
                arapprop = "1";
            }else {
                arapprop = "2";
            }
        }
        jsonObject.put("arapprop",arapprop);
        // 翻译账户属性
        String accattribute = "0";
        if ("1".equals(bipEnterBankAcc.getString("acctType"))){
            accattribute = "2";
        }else if ("2".equals(bipEnterBankAcc.getString("acctType"))){
            accattribute = "1";
        }else if ("3".equals(bipEnterBankAcc.getString("acctType"))){
            accattribute = "3";
        }
        jsonObject.put("accattribute",accattribute);
        // 翻译账号状态 "cEnumString": "{\"0\":\"正常\",\"1\":\"已销户\",\"2\":\"冻结\"}",
        String accstate = "0";
        if ("1".equals(bipEnterBankAcc.getString("acctstatus"))){
            accstate = "3";
        }else if ("2".equals(bipEnterBankAcc.getString("acctstatus"))){
            accstate = "1";
        }
        jsonObject.put("accstate",accstate);
        // 翻译联行号
        JSONObject bankDot = aipoRepository.queryBankDotById(bipEnterBankAcc.getString("bankNumber"));
        if (bankDot!=null) {
            jsonObject.put("bankDot",bankDot);
        }
        // 翻译开户省，开户市
        JSONObject bankProvinceInfo = aipoRepository.queryRegionById(bipEnterBankAcc.getString("bankProvince"));
        JSONObject bankCityInfo = aipoRepository.queryRegionById(bipEnterBankAcc.getString("bankCity"));
        if(bankProvinceInfo!=null){
            jsonObject.put("province",bankProvinceInfo.getString("name"));
        }
        if (bankCityInfo!=null){
            jsonObject.put("city",bankCityInfo.getString("name"));
        }

        return jsonObject;
    }
}
