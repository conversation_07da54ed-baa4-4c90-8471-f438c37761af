{"pools": [{"id": "f5ae2147-6c8a-4ade-9025-cb9f25aab1a1", "name": "redis-default", "type": "single", "host": "************", "port": 6852, "password": "B3BaS#c5Xm5J", "max-active": 100, "max-wait": 10000, "max-idle": 20, "min-idle": 20, "test-on-borrow": false, "isDefault": false, "tenantRoute": {"routeType": "tenantList", "routeRule": ""}}], "clients": [{"id": "1c64fecd-a6ba-46b0-a42b-4881e7da6454", "name": "ymsredislockclient", "database": 200, "pool": "redis-default", "mode": 1, "defaultTTL": 0, "tenantSensitive": false, "distributeMode": "appoint", "forbiddenCommands": ""}, {"id": "1e7f5426-455f-45d9-ade5-eeaeda04f061", "name": "metadata-bootstrap_redisConfig", "database": 0, "pool": "redis-default", "mode": 1, "defaultTTL": 0, "tenantSensitive": false, "distributeMode": "appoint", "forbiddenCommands": ""}, {"id": "bf5bba73-7c1c-47f5-8989-c82886fd3ca6", "name": "c-yy-gz-aipoerp_redis_pub", "database": 200, "pool": "redis-default", "mode": 1, "defaultTTL": 0, "tenantSensitive": false, "distributeMode": "appoint", "forbiddenCommands": ""}, {"id": "bf656527-086c-4042-a4e7-c75678382cbf", "name": "iuap-metadata-base-pubRedisConfig", "database": 0, "pool": "redis-default", "mode": 1, "defaultTTL": 0, "tenantSensitive": false, "distributeMode": "appoint", "forbiddenCommands": ""}, {"id": "e23e5b05-fa46-4692-ac57-bddbc3ed9e7a", "name": "c-yy-gz-aipoerp_redis", "database": 216, "pool": "redis-default", "mode": 1, "defaultTTL": 0, "tenantSensitive": false, "distributeMode": "appoint", "forbiddenCommands": ""}, {"id": "ee5b0148-70fb-440b-8331-12dc35f9b425", "name": "ymsSessionRedisClient", "database": 215, "pool": "redis-default", "mode": 1, "defaultTTL": 0, "tenantSensitive": false, "distributeMode": "appoint", "forbiddenCommands": ""}, {"id": "facd550c-c8cb-466d-a360-ff9184cd7414", "name": "iuap-ap-refcheck-redis", "database": 0, "pool": "redis-default", "mode": 1, "defaultTTL": 0, "tenantSensitive": false, "distributeMode": "appoint", "forbiddenCommands": ""}], "lastUpdateTime": 1733585897384}