package com.yonyou.ucf.mdf.aipo.model;

import lombok.Data;

import java.io.Serializable;


@Data
public class MessageModel implements Serializable {

    /**
     * 业务系统待办主键
     */
    private String taskId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 消息内容
     */
    private String messageContent;

    /**
     * 业务系统发送者登录名
     */
    private String noneBindingSender;

    private String receiver;

    //待办创建时间
    private String createDateTime;

    /**
     * PC穿透地址
     */
    private String pcUrl;

    private String appUrl;
    //原生应用穿透命令，穿透命令需要按这个顺序：iphone|ipad|android|wp
    private String appParam;
    //备注
    private String remarks;




}
