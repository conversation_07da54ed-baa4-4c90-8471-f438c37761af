package com.yonyou.ucf.mdf.travelexpense.rule;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ucf.mdf.travelexpense.service.IBustypeQryService;
import com.yonyou.ucf.mdf.travelexpense.service.IConsumeKindQryService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 保存前重新计算报销明细的金额
 */
@Slf4j
@Component("travelExpenseBillSaveBeforeCalcRule")
public class TravelExpenseBillSaveBeforeCalcRule implements IYpdCommonRul {

	@Autowired
	private IBustypeQryService bustypeQryService;
	@Autowired
	private IConsumeKindQryService consumeKindQryService;

	@SneakyThrows
	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
		log.error("-------------进入差旅报销单保存前规则-------------start");

		String action = rulCtxVO.getAction();
		if (!"save".equals(action)) {
			log.error("action命令不是save命令：{}", action);
			return null;
		}

		List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
		if (CollectionUtils.isEmpty(bills)) {
			log.error("获取bills对象为空");
			return null;
		}

		BizObject bizObject = bills.get(0);

		String bustype = bizObject.getString("bustype");
		String busTypeCode = bustypeQryService.queryBusTypeCodeById(bustype);

		if (!"RBSM00601".equals(busTypeCode)) {
			return null;
		}

		log.error("获取业务对象：{}", JSONUtil.toJson(bizObject));

		bizObject = recalculatingBillbs(bizObject);

		return new RuleExecuteResult(bizObject);
	}

	/**
	 * 重新计算报销明细中的金额
	 * 
	 * @param bizObject
	 * @return
	 */
	private BizObject recalculatingBillbs(BizObject bizObject) {
		// 账单明细
		List<BizObject> expinvoicedetails = bizObject.getBizObjects("expinvoicedetails", BizObject.class);
		// 报销明细
		List<BizObject> expensebillbs = bizObject.getBizObjects("expensebillbs", BizObject.class);
		if (CollectionUtils.isEmpty(expinvoicedetails) || CollectionUtils.isEmpty(expensebillbs)) {
			return bizObject;
		}

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

		Map<String, String> consumeIdCode = consumeKindQryService.queryIdCodeMap();

		// 分组
		Map<String, List<BizObject>> groupExpinvoicedetails = expinvoicedetails.stream()
				.collect(Collectors.groupingBy(v -> {
					Date dcostdate = v.getDate("dcostdate"); // 发生日期
					String dcostdateF = format.format(dcostdate);
					String pk_costaddr = v.getString("pk_costaddr"); // 发生地
					String pk_busimemo = v.getString("pk_busimemo"); // 消费类型
					String code = consumeIdCode.get(pk_busimemo);
					if ("YZ003".equals(code)) { // 交通
						return "YZ003" + "@" + dcostdateF + "@" + pk_costaddr;
					} else if ("YZ002".equals(code)) { // 住宿
						return "YZ002" + "@" + dcostdateF + "@" + pk_costaddr;
					} else if ("YZ001".equals(code) || "YZ009".equals(code)) { // 餐饮、食品
						return "YZ001YZ009" + "@" + dcostdateF + "@" + pk_costaddr;
					}
					return "00000" + "@" + dcostdateF + "@" + pk_costaddr; // 其他
				}));

		for (Entry<String, List<BizObject>> entry : groupExpinvoicedetails.entrySet()) {
			String key = entry.getKey();
			String[] keys = key.split("@");
			List<BizObject> values = entry.getValue();
			BigDecimal nexpmnyTotal = BigDecimal.ZERO; // 可报销金额
			BigDecimal ndeducttaxmnyTotal = BigDecimal.ZERO; // 可抵扣税额
			BigDecimal nmnyTotal = BigDecimal.ZERO; // 价税合计
			BigDecimal nuntaxmnyTotal = BigDecimal.ZERO; // 不含税金额
			for (BizObject v : values) {
				BigDecimal nexpmny = v.getBigDecimal("nexpmny");
				BigDecimal ndeducttaxmny = v.getBigDecimal("ndeducttaxmny");
				BigDecimal nmny = v.getBigDecimal("nmny");
				BigDecimal nuntaxmny = v.getBigDecimal("nuntaxmny");
				nexpmnyTotal = add(nexpmnyTotal, nexpmny);
				ndeducttaxmnyTotal = add(ndeducttaxmnyTotal, ndeducttaxmny);
				nmnyTotal = add(nmnyTotal, nmny);
				nuntaxmnyTotal = add(nuntaxmnyTotal, nuntaxmny);
			}

			for (BizObject expensebillb : expensebillbs) {
				Date dbegindate = expensebillb.getDate("dbegindate");
				String dbegindateF = format.format(dbegindate);
				Date denddate = expensebillb.getDate("denddate");
				String denddateF = format.format(denddate);
				String pk_begaddr = expensebillb.getString("pk_begaddr");
				String pk_endaddr = expensebillb.getString("pk_endaddr");
				boolean isBetween = isBetween(keys[1], dbegindateF, denddateF);
				if (isBetween && (keys[2].equals(pk_begaddr) || keys[2].equals(pk_endaddr))) {
					if ("YZ003".equals(keys[0])) {
						expensebillb.set("ntransmny", nexpmnyTotal);
					} else if ("YZ002".equals(keys[0])) {
						expensebillb.set("nstdhotelmny", nexpmnyTotal);
					} else if ("YZ001".equals(keys[0]) || "YZ009".equals(keys[0])) {
						expensebillb.set("nmealmny", nexpmnyTotal);
					} else {
						expensebillb.set("nmiscellansexp", nexpmnyTotal);
					}
					expensebillb.set("nsummny", nexpmnyTotal);
					expensebillb.set("ntaxmny", ndeducttaxmnyTotal);
					expensebillb.set("nexpensemny", nuntaxmnyTotal);
					continue;
				}
			}

		}

		return bizObject;
	}

	/**
	 * 两个数相加
	 * 
	 * @param num1
	 * @param num2
	 * @return
	 */
	private BigDecimal add(BigDecimal num1, BigDecimal num2) {
		if (num1 == null) {
			return num2;
		}
		if (num2 == null) {
			return num1;
		}
		return num1.add(num2);
	}

	/**
	 * 判断日期是否在两个日期之间，字符串形式
	 * 
	 * @param date
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	private boolean isBetween(String date, String beginDate, String endDate) {
		if (StringUtils.isAnyBlank(date, beginDate, endDate)) {
			return false;
		}
		if (date.compareTo(beginDate) >= 0 && date.compareTo(endDate) <= 0) {
			return true;
		}
		return false;
	}

}
