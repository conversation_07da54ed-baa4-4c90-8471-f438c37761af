package com.yonyou.ucf.mdf.aipo.model;

/**
 * 事件处理状态
 * 
 * <AUTHOR>
 *
 *         2025年5月21日
 */
public enum EventProcessStatus {
	UNPROCESS("1", "未处理"), PROCESSING("2", "处理中"), PROCESSED("3", "处理完成");

	EventProcessStatus(String code, String name) {
		this.code = code;
		this.name = name;
	}

	private String code;
	private String name;

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

}
