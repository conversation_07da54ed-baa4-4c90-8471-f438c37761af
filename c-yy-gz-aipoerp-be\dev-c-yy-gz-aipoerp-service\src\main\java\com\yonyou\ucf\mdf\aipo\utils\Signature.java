package com.yonyou.ucf.mdf.aipo.utils;

import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class Signature {


    private static final char[] HEX = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    public static String sign(String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA1");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            return toHexString(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 签名算法
     * 1. 计算步骤
     * 用于计算签名的参数在不同接口之间会有差异，但算法过程固定如下4个步骤。
     * 将<key, value>请求参数对按key进行字典升序排序，得到有序的参数对列表N
     * 将列表N中的参数对按URL键值对的格式拼接成字符串，得到字符串T（如：key1=value1&key2=value2），
     * URL键值拼接过程value部分需要URL编码，URL编码算法用大写字母，例如%E8，而不是小写%e8
     * 将应用密钥以_sk为键名，组成URL键值拼接到字符串T末尾，得到字符串S（如：key1=value1&key2=value2&app_key=密钥)
     * 对字符串S进行MD5运算，得到接口请求签名
     * 2. 注意事项
     * 不同接口要求的参数对不一样，计算签名使用的参数对也不一样
     * 参数名区分大小写，参数值为空不参与签名
     * URL键值拼接过程value部分需要URL编码
     *
     * @return 签名字符串
     */
    public static String sign(Map<String, Object> map, String secretKey) throws Exception {
        List<Map.Entry<String, Object>> infoIds = new ArrayList<>(map.entrySet());
        infoIds.sort(Map.Entry.comparingByKey());
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> m : infoIds) {
            if (null != m.getValue() && StringUtils.isNotBlank(m.getValue().toString())) {
                sb.append(m.getKey()).append("=").append(URLEncoder.encode(m.getValue().toString(), "UTF-8").toUpperCase()).append("&");
            }
        }
        sb.append("_sk=").append(secretKey);

        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        messageDigest.update(sb.toString().getBytes(Charset.defaultCharset()));
        return toHexString(messageDigest.digest());
    }

    /**
     * 签名验证方法
     */
    public static boolean signValidate(Map<String, Object> data, String secretKey, String sign) {
        try {
            String mySign = sign(data, secretKey);
            return StringUtils.equals(sign, mySign);
        } catch (Exception e) {
            return false;
        }
    }

    private static String toHexString(byte[] bytes) {
        int len = bytes.length;
        StringBuilder buf = new StringBuilder(len * 2);
        // 把密文转换成十六进制的字符串形式
        for (byte aByte : bytes) {
            buf.append(HEX[(aByte >> 4) & 0x0f]);
            buf.append(HEX[aByte & 0x0f]);
        }
        return buf.toString();
    }
}
