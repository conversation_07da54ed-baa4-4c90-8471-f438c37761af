package com.yonyou.ucf.mdf.aipo.controller.voucher;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.aipo.model.AIPOVoucherEventLog;
import com.yonyou.ucf.mdf.aipo.model.EventContent;
import com.yonyou.ucf.mdf.aipo.service.AIPOVoucherEventLogService;
import com.yonyou.ucf.mdf.aipo.service.IVoucherEventProcessService;
import com.yonyou.ucf.mdf.aipo.service.VoucherEventProcessorFactory;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/rest")
@Slf4j
public class AipoVoucherRetryController {

	@Autowired
	private AIPOVoucherEventLogService eventLogService;

	@Autowired
	private VoucherEventProcessorFactory voucherEventProcessorFactory;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@RequestMapping("/aipo/voucherRetry")
	public Object retryVoucherEvent(@RequestBody JSONObject jsonObject, HttpServletRequest request) throws Exception {
		log.debug("开始重试凭证事件推送");
		JSONObject params = new JSONObject();
		params.put("code", "200");
		params.put("status", "1");
		// 发送信息内容
		params.put("content", "重试凭证事件推送成功");
		// 发送信息标题
		params.put("title", "重试凭证事件推送");
		try {

			RobotExecutors.runAs(tenantId, () -> {
				// 1. 获取推送失败的日志记录
				List<AIPOVoucherEventLog> failedLogs = eventLogService.findAllFail();
				if (CollectionUtils.isEmpty(failedLogs)) {
					return;
				}
				for (AIPOVoucherEventLog aipoVoucherEventLog : failedLogs) {
					try {
						EventContent eventContent = JSONUtil.toBean(aipoVoucherEventLog.getEventContent(),
								EventContent.class);
						// 获取对应的事件处理器
						IVoucherEventProcessService processor = voucherEventProcessorFactory
								.getProcessor(eventContent.getType());
						if (processor != null) {
							try {
								log.error("事件类型: {}, 说明: 租户 {} 处理事件，信息: {}", eventContent.getType(),
										eventContent.getTenantId(), eventContent.getContent());
								processor.processEvent(eventContent);
							} catch (IllegalArgumentException e) {
								log.error("事件类型不匹配: {}", e.getMessage());
							}
						} else {
							log.error("未找到处理 {} 事件的处理器", eventContent.getType());
						}
					} catch (Exception e) {
						log.error("事件处理异常：" + e.getMessage(), e);
					}
				}
			});
			params.put("message", "重试推送成功");
		} catch (Exception e) {
			params.put("code", "500");
			params.put("message", e.getMessage());
			params.put("status", "0");
			// 发送信息内容
			params.put("content", "重试凭证事件推送失败：" + e.getMessage());
			log.error("重试凭证事件推送失败", e);
		}

		return params;
	}

}