package com.yonyou.aipierp.dto.ncapi.staff;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public class StaffBillHead extends JSONObject {

    /**
     * "0001"（集团编码）,
     */
    public String PK_GROUP = "pk_group";
    /**
     * "0000102"（旗舰版组织id）,
     */
    public String PK_ORG = "pk_org";
    /**
     * "test0001"（人员code）,
     */
    public String CODE = "code";
    /**
     * "测试人员1"（人员name）,
     */
    public String NAME = "name";
    /**
     * "测试1-update"（曾用名）,
     */
    public String USEDNAME = "usedname";
    /**
     * "2000-01-01"（生日）,
     */
    public String BIRTHDATE = "birthdate";
    /**
     *  "1"（1=男,2=女）,
     */
    public String SEX = "sex";
    /**
     * "CN01"（证件类型CN01=身份证，CN02=护照，CN03=回乡证，CN04=外国人永久居留证，HK01=香港居民身份证，MO01=澳门居民身份证，TW01=台湾身份证，TW02=台胞证）,
     */
    public String IDTYPE = "idtype";
    /**
     * "sfz123"（证件号）,
     */
    public String ID = "id";
    /**
     * "test0001"（助记码）,
     */
    public String MNECODE = "mnecode";
    /**
     * "2014-08-28"（参加工作日期）,
     */
    public String JOINWORKDATE = "joinworkdate";
    /**
     * "35814"（办公电话）,
     */
    public String OFFICEPHONE = "officephone";
    /**
     * "35824"（家庭低昂）,
     */
    public String HOMEPHONE = "homephone";
    /**
     * "35834"（手机）,
     */
    public String MOBILE = "mobile";
    /**
     * "<EMAIL>"（邮箱）,
     */
    public String EMAIL = "email";
    /**
     * （工作信息）:
     */
    public String PSNJOBS = "psnjobs";

    public void setPk_group(String pk_group) {
        this.put(PK_GROUP, pk_group);
    }

    public void setPk_org(String pk_org) {
        this.put(PK_ORG, pk_org);
    }

    public void setCode(String code) {
        this.put(CODE, code);
    }

    public void setName(String name) {
        this.put(NAME, name);
    }

    public void setUsedname(String usedname) {
        this.put(USEDNAME, usedname);
    }

    public void setBirthdate(String birthdate) {
        this.put(BIRTHDATE, birthdate);
    }

    public void setSex(String sex) {
        this.put(SEX, sex);
    }

    public void setIdtype(String idtype) {
        this.put(IDTYPE, idtype);
    }

    public void setId(String id) {
        this.put(ID, id);
    }

    public void setMnecode(String mnecode) {
        this.put(MNECODE, mnecode);
    }

    public void setJoinworkdate(String joinworkdate) {
        this.put(JOINWORKDATE, joinworkdate);
    }

    public void setOfficephone(String officephone) {
        this.put(OFFICEPHONE, officephone);
    }

    public void setHomephone(String homephone) {
        this.put(HOMEPHONE, homephone);
    }

    public void setMobile(String mobile) {
        this.put(MOBILE, mobile);
    }

    public void setEmail(String email) {
        this.put(EMAIL, email);
    }

    public void setPsnjobs(JSONObject psnjobs) {
        this.put(PSNJOBS, psnjobs);
    }

}
