{"java.configuration.updateBuildConfiguration": "interactive", "java.project.sourcePaths": ["src/main/java"], "java.project.outputPath": "target/classes", "java.project.referencedLibraries": ["lib/**/*.jar"], "java.configuration.runtimes": [{"name": "JavaSE-1.8", "path": "D:\\Program Files\\Java\\jdk1.8.0_291", "default": true}], "java.jdt.ls.java.home": "D:\\Program Files\\Java\\jdk1.8.0_291", "java.maven.updateSnapshots": true, "java.maven.downloadSources": true, "java.server.launchMode": "Standard", "java.completion.importOrder": ["java", "javax", "org", "com"], "java.format.settings.url": ".vscode/java-formatter.xml", "java.format.settings.profile": "GoogleStyle", "maven.terminal.useJavaHome": false, "maven.excludedFolders": ["**/.*", "**/node_modules", "**/target", "**/bin", "**/archetype-resources"], "maven.dependency.enableConflictDiagnostics": false, "maven.executable.path": "D:\\Program Files\\apache-maven-3.8.1\\bin\\mvn.cmd", "maven.executable.options": "D:\\Program Files\\apache-maven-3.8.1\\conf\\settings.xml", "commentTranslate.targetLanguage": "zh-CN", "commentTranslate.source": "Google"}