package com.yonyou.ucf.mdf.aipo.service;

import com.yonyou.ucf.mdf.aipo.model.EventContent;
import com.yonyou.ucf.mdf.aipo.model.EventType;

/**
 * @program: aipo
 * @description: 凭证数据同步接口
 * @author: yangh
 * @create: 2024-12-09 15:23
 **/
public interface IAipoVoucherService {

    /**
     * 凭证保存
     *
     * @param contentMsg  事件入参
     * @param eventId     事件ID
     * @param o           冗余参数
     * @param contentType
     * @throws Exception
     */
    void addData(String contentMsg, String eventId, Object o, EventType contentType) throws Exception;

    /**
     * 凭证删除
     *
     * @param contentMsg  事件入参
     * @param eventId     事件ID
     * @param contentType
     * @param content
     * @throws Exception
     */
    void delData(String contentMsg, String eventId, EventType contentType, EventContent content) throws Exception;

    /**
     *
     * @param contentMsg  事件入参
     * @param type 事件类型
     * @param eventId 事件ID
     * @param o
     * @throws Exception
     */
    void actionVoucherOpertionBatch(String contentMsg, EventType type, String eventId, Object o)throws Exception;
}
