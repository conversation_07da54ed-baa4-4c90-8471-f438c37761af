package com.yonyou.ucf.mdf.dept.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.dept.DeptBill;
import com.yonyou.aipierp.dto.ncapi.dept.DeptBillHead;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.service.impl.NCOpenApiConfig;
import com.yonyou.ucf.mdf.dept.entity.DeptSyncLog;
import com.yonyou.ucf.mdf.dept.service.DeptSyncLogService;
import com.yonyou.ucf.mdf.dept.service.DeptSyncService;
import com.yonyou.ucf.mdf.utils.AIPODaoHelper;
import com.yonyou.ypd.bill.basic.entity.WeakTypingDO;

import lombok.extern.slf4j.Slf4j;

/**
 * 部门同步服务实现类
 */
@Slf4j
@Service
public class DeptSyncServiceImpl implements DeptSyncService {

    // 部门元数据实体uri
    private static final String ADMINORGVO_URI = "bd.adminOrg.AdminOrgVO";
    
    @Autowired
    private AIPODaoHelper aipoDaoHelper;
    
    @Autowired
    private NCOpenApiService ncOpenApiService;
    
    @Autowired
    private NCOpenApiConfig ncOpenApiConfig;
    
    @Autowired
    private DeptSyncLogService deptSyncLogService;

    @Override
    public JSONObject syncDeptToNC(Date beginTime, Date endTime) {
        log.info("开始同步部门数据到NCC高级版，时间范围：{} - {}", beginTime, endTime);
        
        JSONObject result = new JSONObject();
        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;
        
        try {
            // 查询时间范围内的部门数据
            List<JSONObject> deptList = queryDeptsByPubtsRange(beginTime, endTime);
            totalCount = deptList.size();
            
            log.info("查询到{}条部门数据需要同步", totalCount);
            
            // 批量推送部门数据
            List<DeptSyncLog> syncLogs = batchPushDeptToNC(deptList);
            
            // 统计结果
            for (DeptSyncLog syncLog : syncLogs) {
                if ("Y".equals(syncLog.getSuccess())) {
                    successCount++;
                } else {
                    failCount++;
                }
            }
            
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("success", failCount == 0);
            
            log.info("部门同步完成，总数：{}，成功：{}，失败：{}", totalCount, successCount, failCount);
            
        } catch (Exception e) {
            log.error("部门同步过程中发生异常", e);
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failCount", totalCount - successCount);
            result.put("success", false);
            result.put("errorMsg", e.getMessage());
        }
        
        return result;
    }

    @Override
    public List<JSONObject> queryDeptsByPubtsRange(Date beginTime, Date endTime) {
        QuerySchema querySchema = QuerySchema.create().fullname(ADMINORGVO_URI).addSelect("*");
        
        QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
                QueryCondition.name("pubts").between(beginTime, endTime),
                QueryCondition.name("dr").eq(0),  // 未删除
                QueryCondition.name("orgtype").eq(2)  // 部门类型
        );
        querySchema.appendQueryCondition(queryConditionGroup);
        querySchema.addOrderBy("pubts");
        
        try {
            List<WeakTypingDO> deptList = (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
            List<JSONObject> result = new ArrayList<>();
            
            if (CollectionUtils.isNotEmpty(deptList)) {
                for (WeakTypingDO dept : deptList) {
                    result.add((JSONObject) JSON.toJSON(dept.toMap()));
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据时间范围查询部门数据失败", e);
            throw new BusinessException("根据时间范围查询部门数据失败，请稍后再试");
        }
    }

    @Override
    public DeptSyncLog pushSingleDeptToNC(JSONObject deptInfo) {
        DeptSyncLog syncLog = new DeptSyncLog();
        Date now = new Date();
        
        try {
            // 设置基本信息
            syncLog.setDeptId(deptInfo.getString("id"));
            syncLog.setDeptCode(deptInfo.getString("code"));
            syncLog.setDeptName(deptInfo.getString("name"));
            syncLog.setOrgId(deptInfo.getString("orgid"));
            syncLog.setParentId(deptInfo.getString("parentid"));
            syncLog.setEnable(deptInfo.getString("enable"));
            syncLog.setModifiedtime(deptInfo.getDate("modifiedtime"));
            syncLog.setLastSyncDate(now);
            syncLog.setCreateTime(now);
            
            // 构建推送数据
            UFinterface uFinterface = convertToDeptSaveUFinterface(deptInfo);
            syncLog.setRequestData(JSON.toJSONString(uFinterface));
            
            // 推送到NCC
            JSONObject resp = ncOpenApiService.addDeptEx(uFinterface);
            syncLog.setResponseData(JSON.toJSONString(resp));
            
            // 判断推送结果
            if (resp.getBooleanValue("success") && 
                "1".equals(resp.getJSONObject("data").getJSONObject("ufinterface")
                    .getJSONArray("sendresult").getJSONObject(0).getString("resultcode"))) {
                syncLog.setSuccess("Y");
                log.info("部门{}推送成功", deptInfo.getString("code"));
            } else {
                syncLog.setSuccess("N");
                syncLog.setErrMsg("推送失败：" + resp.toJSONString());
                log.error("部门{}推送失败：{}", deptInfo.getString("code"), resp.toJSONString());
            }
            
        } catch (Exception e) {
            syncLog.setSuccess("N");
            syncLog.setErrMsg("推送异常：" + e.getMessage());
            syncLog.setErrStack(getStackTrace(e));
            log.error("部门{}推送异常", deptInfo.getString("code"), e);
        }
        
        // 保存同步日志
        try {
            syncLog = deptSyncLogService.save(syncLog);
        } catch (Exception e) {
            log.error("保存部门同步日志失败", e);
        }
        
        return syncLog;
    }

    @Override
    public List<DeptSyncLog> batchPushDeptToNC(List<JSONObject> deptList) {
        List<DeptSyncLog> syncLogs = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(deptList)) {
            return syncLogs;
        }
        
        for (JSONObject deptInfo : deptList) {
            DeptSyncLog syncLog = pushSingleDeptToNC(deptInfo);
            syncLogs.add(syncLog);
        }
        
        return syncLogs;
    }
    
    /**
     * 转换部门信息为NCC推送格式
     */
    private UFinterface convertToDeptSaveUFinterface(JSONObject deptInfo) {
        UFinterface deptUFinterface = UFinterface.init(ncOpenApiConfig.getBiz_center(), "dept",
                ncOpenApiConfig.getGroupcode());
        
        DeptBillHead deptBillHead = new DeptBillHead();
        deptBillHead.setCode(deptInfo.getString("code"));
        deptBillHead.setName(deptInfo.getString("name"));
        
        // 简称处理
        String shortName = deptInfo.getString("shortname");
        if (StringUtils.isNotEmpty(shortName)) {
            deptBillHead.setShortname(shortName);
        } else {
            deptBillHead.setShortname(deptInfo.getString("name"));
        }
        
        // 助记码
        deptBillHead.setMnecode(deptInfo.getString("code"));
        deptBillHead.setPk_group(ncOpenApiConfig.getGroupcode());
        
        // 上级部门处理
        String parentId = deptInfo.getString("parentid");
        if (StringUtils.isNotEmpty(parentId)) {
            deptBillHead.setPk_fatherorg(parentId);
        }
        
        // 所属组织
        String orgId = deptInfo.getString("orgid");
        if (StringUtils.isNotEmpty(orgId)) {
            deptBillHead.setPk_org(orgId);
        }
        
        // 部门类型：0=普通部门
        deptBillHead.setDepttype("0");
        
        // 启用状态：1=未启用,2=已启用,3=已停用
        Integer enable = deptInfo.getInteger("enable");
        if (enable != null && enable == 2) {
            deptBillHead.setEnablestate("2");  // 已启用
        } else {
            deptBillHead.setEnablestate("3");  // 已停用
        }
        
        // 创建时间
        Date createTime = deptInfo.getDate("creationtime");
        if (createTime != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            deptBillHead.setCreatedate(sdf.format(createTime));
        }
        
        // 其他固定字段
        deptBillHead.setHrcanceled("N");
        deptBillHead.setDisplayorder(deptInfo.getString("displayorder") == null ? "1" : deptInfo.getString("displayorder"));
        deptBillHead.setAddress(deptInfo.getString("address") == null ? "" : deptInfo.getString("address"));
        deptBillHead.setOrgtype13("Y");
        deptBillHead.setOrgtype17("N");
        deptBillHead.setTel(deptInfo.getString("telephone") == null ? "" : deptInfo.getString("telephone"));
        deptBillHead.setMemo(deptInfo.getString("description") == null ? "" : deptInfo.getString("description"));
        
        DeptBill deptBill = DeptBill.init(deptBillHead, deptInfo.getString("id"));
        deptUFinterface.setBill(deptBill);
        
        return deptUFinterface;
    }
    
    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }
}
