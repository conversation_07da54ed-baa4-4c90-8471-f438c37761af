package com.yonyou.ucf.mdf.rbsm.model;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import com.yonyou.iuap.yms.processor.BaseProcessor;

/**
 * <AUTHOR>
 *
 *         2025年3月27日
 */
public class StringListProcessor extends BaseProcessor {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public List<String> processResultSet(ResultSet rs) throws Exception {
		List<String> list = new ArrayList<>();
		while (rs.next()) {
			list.add(rs.getString(1));
		}
		return list;
	}

}
