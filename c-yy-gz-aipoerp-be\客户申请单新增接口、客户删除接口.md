# 1新增修改客户申请单
### 1.1请求类型 
POST
### 1.2请求地址
 http://IP:port/nccloud/api/uapbd/customermanage/customer/addCustomerPf

### 1.3请求参数说明

#### 1.3.1参数说明

  下面所列字段均为必输，其余需要传的扩展字段参考数据字典 （https://community.yonyou.com/datadict/datadict-2207/index.html） 
  billpf（表bd_customer_pf） 和bill（表bd_customer） 信息传值即可
  ```
{
  "billpf": {
"pk_group":"申请集团",
"pk_org":"申请组织",
"destorg":"(0=本组织,1=集团,2=全局)",
"customercode":"客户编码",
"customername":"客户名称",
"pk_custclass":"客户基本分类"（高级版档案，需要两边维护code一致）,
"update_cust":"主键"
  },
 "bill": 
 {
"custprop":"0"0=外部单位,1=内部单位,
"def10":"主键"
}
}

  ```
#### 1.3.2请求示例
  ```
{
    "billpf": {
        "pk_group": "A",
        "pk_org": "biptestid0",
        "destorg": "0",
        "customercode": "testcust1218002",
        "customername": "testcustname1218002-update",
        "pk_custclass": "1",
        "apply_type": "1",
        "update_cust": "bipcustid0002"
    },
    "bill": {
        "custprop": "0",
        "def10": "bipcustid0002"
    }
}
  ```

### 1.4返回示例
#### 1.4.1成功
```
{
    "success": true,
    "data": "插入成功",
    "code": "1000000000",
    "message": "0",
    "errorStack": null
}
```
#### 1.4.2失败
```
{
    "success": false,
    "data": null,
    "code": "1000000001",
    "message": "未找到旗舰版id（def1）biptestid3对应组织",
    "errorStack": "报错堆栈"
}
```


# 2删除客户

### 2.1请求类型 
POST
### 2.2请求地址
 http://IP:port/nccloud/api/uapbd/customermanage/customer/deleteCustbyBipId
### 2.3请求参数说明

#### 2.3.1参数说明
  ```
{
  "bipid": "旗舰版id",
  "version": "1"（固定值）
}
  ```
#### 2.3.2请求示例
  ```
{
  "bipid":"bipcustid0003",
  "version": "1"
}
  ```

### 2.4返回示例
#### 2.4.1成功
```
{
    "success": true,
    "data": "true",
    "code": "1000000000",
    "message": null,
    "errorStack": null
}
```
#### 2.4.2失败
```
{
    "success": false,
    "data": null,
    "code": "-1",
    "message": "根据主键(def10)bipcustid0003无法查询到对应的客户",
    "errorStack": null
}
```
