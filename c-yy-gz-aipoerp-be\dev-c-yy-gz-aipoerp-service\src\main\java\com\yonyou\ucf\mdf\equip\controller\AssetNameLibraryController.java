package com.yonyou.ucf.mdf.equip.controller;

import com.yonyou.ucf.mdf.equip.service.AssetNameLibraryService;
import com.yonyou.ypd.bill.response.ResultMessage;
import com.yonyou.ypd.mdf.adapter.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025/2/28 11:08
 * @DESCRIPTION 资产名称库控制类
 */
@RequestMapping("/assetNameLibrary")
@RestController
public class AssetNameLibraryController extends BaseController {
    @Autowired
    private AssetNameLibraryService nameLibraryService;

    @GetMapping("/pullFromMaterial")
    public void pullFromMaterial(HttpServletResponse response) {
        try {
            Integer res = nameLibraryService.pullFromMaterial();
            renderJson(response, ResultMessage.data(res));
        } catch (Exception e) {
            renderJson(response, ResultMessage.error(e.getMessage()));
        }
    }
}
