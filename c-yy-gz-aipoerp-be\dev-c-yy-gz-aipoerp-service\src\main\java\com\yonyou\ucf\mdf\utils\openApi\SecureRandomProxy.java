package com.yonyou.ucf.mdf.utils.openApi;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2021/3/16 19:32
 * @Version 1.0
 */
public class SecureRandomProxy {

    public static SecureRandom getRandomInstance() {
        SecureRandom random = null;
        try {
            random = SecureRandom.getInstance("SHA1PRNG");
//                random = SecureRandom.getInstance("NativePRNG");
//                random = SecureRandom.getInstance("NativePRNGNonBlocking");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        return random;
    }
}
