{"id": "2bf52275-eb44-45df-a437-a3803b3524a9", "enable": true, "encoderPattern": "%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%logger{36}] [%X{traceId}] [%X{spanId}] [%X{pSpanId}] [%X{rpcOccurrence}] [%X{code}] [%X{req.requestURL}] [%X{req.queryString}] [${spring.domain.name},${spring.application.name},%X{sysId},%X{tenantId},%X{userId},%X{profile},%X{agentId}] - %msg %ex%n", "basePath": "/data/logs/app/", "isolated": true, "env": "dev", "productCode": "", "appenderRef": ["rolling"], "lastUpdateTime": 1733728026045, "middleWare": [], "modules": [{"id": "", "logId": "", "name": "c-yy-gz-aipoerp", "file": "yms-application.log", "parent": "", "type": "", "appenderRef": [], "level": "ERROR", "additivity": false, "logger": [], "productCode": "", "isPrivate": false, "exclusiveValue": "", "truncate": true, "msgMaxLen": "5120"}], "ymsAppenders": [{"id": "1f46d0fe-daeb-4e91-adaf-041af60fc51a", "name": "rolling", "type": "ch.qos.logback.core.rolling.RollingFileAppender", "file": "yms-application.log", "encoderPattern": "%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%logger{36}] [%X{traceId}] [%X{spanId}] [%X{pSpanId}] [%X{rpcOccurrence}] [%X{code}] [%X{req.requestURL}] [%X{req.queryString}] [${spring.domain.name},${spring.application.name},%X{vtrace},%X{sysId},%X{tenantId},%X{userId},%X{profile},%X{agentId}] - %msg %ex%n", "appenderRef": "", "ymsFilter": null, "ymsRollingPolicy": {"type": "SizeAndTimeBasedRollingPolicy", "fileNamePattern": "yms-application-%d{yyyy-MM-dd}.%i.log.gz", "maxHistory": 30, "totalSizeCap": "5GB", "maxFileSize": "100MB"}}], "console": [], "async": [], "roll": [], "level": "ERROR", "createTime": 1625146252000, "updateTime": 1629078214000}