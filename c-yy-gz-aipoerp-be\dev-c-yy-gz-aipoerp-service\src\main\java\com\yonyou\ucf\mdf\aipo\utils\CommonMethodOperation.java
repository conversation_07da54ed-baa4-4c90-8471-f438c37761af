package com.yonyou.ucf.mdf.aipo.utils;


import com.yonyou.iuap.yms.param.SQLParameter;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Component
public class CommonMethodOperation {


    /**
     * 将实体对象转换为插入数据库的SQL语句。
     *
     * @param entity    实体对象
     * @param parameters
     * @return 构建好的SQL插入语句
     */
    public static String buildInsertSql(Object entity, SQLParameter parameters) throws IllegalAccessException {
        if (entity == null) {
            throw new IllegalArgumentException("Entity cannot be null.");
        }
        if (parameters == null) {
            throw new IllegalArgumentException("SQLParameter cannot be null.");
        }

        // 获取实体类的字段信息
        Field[] fields = entity.getClass().getDeclaredFields();
        List<String> columnNames = new ArrayList<>();

        for (Field field : fields) {
            field.setAccessible(true); // 确保私有字段也可以访问
            try {
                Object value = field.get(entity);
                if (value != null) {
                    columnNames.add(field.getName());
                    parameters.addParam(value);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Error accessing field value", e);
            }
        }

        StringBuilder sqlBuilder = new StringBuilder("INSERT INTO ");
        sqlBuilder.append(" c_yy_gz_aipoerp_db.event_sync_data_log ");
        sqlBuilder.append(" (");
        for (int i = 0; i < columnNames.size(); i++) {
            String skey = columnNames.get(i);
            if("ytenantId".equals(skey)){
                skey = "ytenant_id";
            }
            sqlBuilder.append(skey);
            if (i < columnNames.size() - 1) {
                sqlBuilder.append(", ");
            }
        }
        sqlBuilder.append(") VALUES (");
        for (int i = 0; i < columnNames.size(); i++) {
            sqlBuilder.append("?");
            if (i < columnNames.size() - 1) {
                sqlBuilder.append(", ");
            }
        }
        sqlBuilder.append(");");

        return sqlBuilder.toString();
    }


}
