package com.yonyou.ucf.mdf.insurance.rule;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;

import lombok.SneakyThrows;

/**
 * 社保缴交取消审批前规则
 * 
 * <AUTHOR>
 *
 *         2025年3月6日
 */
@Component("insurancePayUnauditBeforeRule")
public class InsurancePayUnauditBeforeRule implements IYpdCommonRul {

	@SneakyThrows
	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
		String action = rulCtxVO.getAction();
		if (!"unaudit".equals(action)) {
			return null;
		}
		List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
		if (CollectionUtils.isEmpty(bills)) {
			return null;
		}
		for (BizObject bizObject : bills) {
			bizObject.getId();
		}
		return null;
	}

}
