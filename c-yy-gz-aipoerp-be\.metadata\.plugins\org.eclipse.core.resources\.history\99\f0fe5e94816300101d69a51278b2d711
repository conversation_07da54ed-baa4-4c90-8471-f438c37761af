package com.yonyou.ucf.mdf.dept.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.dept.DeptBill;
import com.yonyou.aipierp.dto.ncapi.dept.DeptBillHead;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.dept.entity.DeptSyncLog;
import com.yonyou.ucf.mdf.dept.service.DeptSyncLogService;
import com.yonyou.ucf.mdf.dept.service.DeptSyncService;
import com.yonyou.ucf.mdf.service.impl.NCOpenApiConfig;
import com.yonyou.ucf.mdf.utils.AIPODaoHelper;
import com.yonyou.ypd.bill.basic.entity.WeakTypingDO;

import lombok.extern.slf4j.Slf4j;

/**
 * 部门同步服务实现类
 */
@Slf4j
@Service
public class DeptSyncServiceImpl implements DeptSyncService {

	// 部门元数据实体uri
	private static final String ADMINORGVO_URI = "bd.adminOrg.AdminOrgVO";

	// 常量定义
	private static final int DEPT_ORG_TYPE = 2; // 部门类型
	private static final int NOT_DELETED = 0; // 未删除状态
	private static final String SUCCESS_FLAG = "Y"; // 成功标识
	private static final String FAIL_FLAG = "N"; // 失败标识
	private static final String NCC_SUCCESS_CODE = "1"; // NCC成功返回码
	private static final String DEFAULT_DEPT_TYPE = "0"; // 默认部门类型
	private static final String DEFAULT_DISPLAY_ORDER = "1"; // 默认排序
	private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss"; // 日期格式
	private static final String DATE_FORMAT = "yyyy-MM-dd"; // 日期格式

	@Autowired
	private AIPODaoHelper aipoDaoHelper;

	@Autowired
	private NCOpenApiService ncOpenApiService;

	@Autowired
	private NCOpenApiConfig ncOpenApiConfig;

	@Autowired
	private DeptSyncLogService deptSyncLogService;

	@Override
	public JSONObject syncDeptToNC(Date beginTime, Date endTime, boolean forceSync, String deptCode) {
		if (StringUtils.isNotEmpty(deptCode)) {
			log.info("开始同步指定部门到NCC高级版，部门编码：{}", deptCode);
		} else {
			log.info("开始同步部门数据到NCC高级版，时间范围：{} - {}，强制同步：{}", beginTime, endTime, forceSync);
		}

		JSONObject result = new JSONObject();
		int totalCount = 0;
		int successCount = 0;
		int failCount = 0;
		int filteredCount = 0; // 过滤掉的数量

		try {
			List<JSONObject> deptList;

			// 如果指定了部门编码，则只查询该部门
			if (StringUtils.isNotEmpty(deptCode)) {
				deptList = queryDeptByCode(deptCode);
				totalCount = deptList.size();
				log.info("查询到指定部门{}条数据", totalCount);
			} else {
				// 查询时间范围内的部门数据
				deptList = queryDeptsByPubtsRange(beginTime, endTime);
				totalCount = deptList.size();
				log.info("查询到{}条部门数据", totalCount);

				// 如果不是强制同步，需要过滤已同步成功的部门
				if (!forceSync) {
					deptList = filterAlreadySyncedDepts(deptList);
					filteredCount = totalCount - deptList.size();
					log.info("过滤掉已同步成功的部门{}条，剩余{}条需要同步", filteredCount, deptList.size());
				}
			}

			// 批量推送部门数据
			List<DeptSyncLog> syncLogs = batchPushDeptToNC(deptList);

			// 统计结果
			for (DeptSyncLog syncLog : syncLogs) {
				if (SUCCESS_FLAG.equals(syncLog.getSuccess())) {
					successCount++;
				} else {
					failCount++;
				}
			}

			result.put("totalCount", totalCount);
			result.put("filteredCount", filteredCount);
			result.put("syncCount", deptList.size()); // 实际同步数量
			result.put("successCount", successCount);
			result.put("failCount", failCount);
			result.put("success", failCount == 0);

			log.info("部门同步完成，查询总数：{}，过滤数：{}，同步数：{}，成功：{}，失败：{}", totalCount, filteredCount, deptList.size(),
					successCount, failCount);

		} catch (Exception e) {
			log.error("部门同步过程中发生异常", e);
			result.put("totalCount", totalCount);
			result.put("filteredCount", filteredCount);
			result.put("syncCount", totalCount - filteredCount);
			result.put("successCount", successCount);
			result.put("failCount", totalCount - filteredCount - successCount);
			result.put("success", false);
			result.put("errorMsg", e.getMessage());
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<JSONObject> queryDeptsByPubtsRange(Date beginTime, Date endTime) {
		QuerySchema querySchema = QuerySchema.create().fullname(ADMINORGVO_URI).addSelect("*");

		QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(
				QueryCondition.name("pubts").between(beginTime, endTime), QueryCondition.name("dr").eq(NOT_DELETED), // 未删除
				QueryCondition.name("orgtype").eq(DEPT_ORG_TYPE) // 部门类型
		);
		querySchema.appendQueryCondition(queryConditionGroup);
		querySchema.addOrderBy("pubts");

		try {
			List<WeakTypingDO> deptList = (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
			return convertToJsonList(deptList);
		} catch (Exception e) {
			log.error("根据时间范围查询部门数据失败", e);
			throw new BusinessException("根据时间范围查询部门数据失败，请稍后再试");
		}
	}

	/**
	 * 根据部门编码查询部门数据
	 *
	 * @param deptCode 部门编码
	 * @return 部门数据列表
	 */
	@SuppressWarnings("unchecked")
	public List<JSONObject> queryDeptByCode(String deptCode) {
		QuerySchema querySchema = QuerySchema.create().fullname(ADMINORGVO_URI).addSelect("*");

		QueryConditionGroup queryConditionGroup = QueryConditionGroup.and(QueryCondition.name("code").eq(deptCode),
				QueryCondition.name("dr").eq(NOT_DELETED), // 未删除
				QueryCondition.name("orgtype").eq(DEPT_ORG_TYPE) // 部门类型
		);
		querySchema.appendQueryCondition(queryConditionGroup);

		try {
			List<WeakTypingDO> deptList = (List<WeakTypingDO>) aipoDaoHelper.queryOuter(querySchema);
			return convertToJsonList(deptList);
		} catch (Exception e) {
			log.error("根据部门编码查询部门数据失败，部门编码：{}", deptCode, e);
			throw new BusinessException("根据部门编码查询部门数据失败，请稍后再试");
		}
	}

	@Override
	public DeptSyncLog pushSingleDeptToNC(JSONObject deptInfo) {
		DeptSyncLog syncLog = new DeptSyncLog();
		Date now = new Date();

		try {
			// 设置基本信息
			syncLog.setDeptId(deptInfo.getString("id"));
			syncLog.setDeptCode(deptInfo.getString("code"));
			syncLog.setDeptName(deptInfo.getString("name"));
			syncLog.setOrgId(deptInfo.getString("parentorgid"));
			syncLog.setParentId(deptInfo.getString("parentid"));
			syncLog.setEnable(deptInfo.getString("enable"));
			syncLog.setModifiedtime(deptInfo.getDate("modifiedtime"));
			SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN);
			syncLog.setBusinessDate(ADMINORGVO_URI);
			syncLog.setLastSyncDate(now);
			syncLog.setCreateTime(now);
			syncLog.set_status(ActionEnum.INSERT.getValueInt());

			// 构建推送数据
			UFinterface uFinterface = convertToDeptSaveUFinterface(deptInfo);
			syncLog.setRequestData(JSON.toJSONString(uFinterface));

			// 推送到NCC
			JSONObject resp = ncOpenApiService.addDeptEx(uFinterface);
			syncLog.setResponseData(JSON.toJSONString(resp));

			// 判断推送结果
			if (isNCPushSuccess(resp)) {
				syncLog.setSuccess(SUCCESS_FLAG);
				log.info("部门{}推送成功", deptInfo.getString("code"));
			} else {
				syncLog.setSuccess(FAIL_FLAG);
				syncLog.setErrMsg("推送失败：" + resp.toJSONString());
				log.error("部门{}推送失败：{}", deptInfo.getString("code"), resp.toJSONString());
			}

		} catch (Exception e) {
			syncLog.setSuccess(FAIL_FLAG);
			syncLog.setErrMsg("推送异常：" + e.getMessage());
			syncLog.setErrStack(getStackTrace(e));
			log.error("部门{}推送异常", deptInfo.getString("code"), e);
		}

		// 保存同步日志
		try {
			syncLog = deptSyncLogService.save(syncLog);
		} catch (Exception e) {
			log.error("保存部门同步日志失败", e);
		}

		return syncLog;
	}

	@Override
	public List<DeptSyncLog> batchPushDeptToNC(List<JSONObject> deptList) {
		List<DeptSyncLog> syncLogs = new ArrayList<>();

		if (CollectionUtils.isEmpty(deptList)) {
			return syncLogs;
		}

		for (JSONObject deptInfo : deptList) {
			DeptSyncLog syncLog = pushSingleDeptToNC(deptInfo);
			syncLogs.add(syncLog);
		}

		return syncLogs;
	}

	/**
	 * 转换部门信息为NCC推送格式
	 */
	private UFinterface convertToDeptSaveUFinterface(JSONObject deptInfo) {
		UFinterface deptUFinterface = UFinterface.init(ncOpenApiConfig.getBiz_center(), "dept",
				ncOpenApiConfig.getGroupcode());

		DeptBillHead deptBillHead = new DeptBillHead();
		deptBillHead.setCode(deptInfo.getString("code"));
		deptBillHead.setName(deptInfo.getString("name"));

		// 简称处理
		String shortName = deptInfo.getString("shortname");
		if (StringUtils.isNotEmpty(shortName)) {
			deptBillHead.setShortname(shortName);
		} else {
			deptBillHead.setShortname(deptInfo.getString("name"));
		}

		// 助记码
		deptBillHead.setMnecode(deptInfo.getString("code"));
		deptBillHead.setPk_group(ncOpenApiConfig.getGroupcode());

		// 上级部门处理 - 参考原实现类逻辑
		String parentId = deptInfo.getString("parentid"); // 上级组织部门id
		String parentOrgId = deptInfo.getString("parentorgid"); // 上级组织id

		// 设置上级部门
		if (parentId != null && parentId.equals(parentOrgId)) {
			// 上级组织 == 上级组织部门，此部门为顶级部门
			deptBillHead.setPk_fatherorg(null);
		} else {
			deptBillHead.setPk_fatherorg(parentId);
		}

		// 所属组织
		if (StringUtils.isNotEmpty(parentOrgId)) {
			deptBillHead.setPk_org(parentOrgId);
		}

		// 部门类型：0=普通部门
		deptBillHead.setDepttype(DEFAULT_DEPT_TYPE);

		// 启用状态：0=未启用,1=已启用,2=已停用 -> NCC: 1=未启用,2=已启用,3=已停用
		String deptEnable = deptInfo.getString("enable");
		if ("0".equals(deptEnable)) {
			deptBillHead.setEnablestate("1"); // 未启用
		} else if ("1".equals(deptEnable)) {
			deptBillHead.setEnablestate("2"); // 已启用
		} else if ("2".equals(deptEnable)) {
			deptBillHead.setEnablestate("3"); // 已停用
		}

		// 创建时间
		Date createTime = deptInfo.getDate("creationtime");
		if (createTime != null) {
			SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN);
			deptBillHead.setCreatedate(sdf.format(createTime));
		}

		// 其他固定字段
		deptBillHead.setHrcanceled("N");
		String displayOrder = deptInfo.getString("displayorder");
		deptBillHead.setDisplayorder(StringUtils.isNotEmpty(displayOrder) ? displayOrder : DEFAULT_DISPLAY_ORDER);
		deptBillHead.setAddress(""); // 参考原实现类，设置为空字符串
		deptBillHead.setOrgtype13("Y");
		deptBillHead.setOrgtype17("N");
		deptBillHead.setTel(deptInfo.getString("telephone"));
		deptBillHead.setMemo(""); // 参考原实现类，设置为空字符串

		DeptBill deptBill = DeptBill.init(deptBillHead, deptInfo.getString("id"));
		deptUFinterface.setBill(deptBill);

		return deptUFinterface;
	}

	/**
	 * 过滤已同步成功的部门 通过对比部门的修改时间和最新同步日志的时间来判断是否需要重新同步
	 *
	 * @param deptList 部门列表
	 * @return 过滤后的部门列表
	 */
	private List<JSONObject> filterAlreadySyncedDepts(List<JSONObject> deptList) {
		if (CollectionUtils.isEmpty(deptList)) {
			return deptList;
		}

		List<JSONObject> filteredList = new ArrayList<>();

		for (JSONObject deptInfo : deptList) {
			String deptId = deptInfo.getString("id");
			Date deptModifiedTime = deptInfo.getDate("modifiedtime");

			// 如果部门修改时间为空，使用创建时间
			if (deptModifiedTime == null) {
				deptModifiedTime = deptInfo.getDate("creationtime");
			}

			// 查询该部门最新的同步成功日志
			List<DeptSyncLog> syncLogs = deptSyncLogService.findByDeptId(deptId);

			boolean needSync = true;

			if (CollectionUtils.isNotEmpty(syncLogs)) {
				// 找到最新的成功同步记录
				DeptSyncLog latestSuccessLog = null;
				for (DeptSyncLog log : syncLogs) {
					if (SUCCESS_FLAG.equals(log.getSuccess())) {
						if (latestSuccessLog == null || isMoreRecent(log, latestSuccessLog)) {
							latestSuccessLog = log;
						}
					}
				}

				// 如果存在成功的同步记录，且部门修改时间早于或等于同步时间，则不需要重新同步
				if (latestSuccessLog != null && latestSuccessLog.getLastSyncDate() != null
						&& deptModifiedTime != null) {

					// 如果部门的修改时间早于或等于最后同步时间，则不需要重新同步
					if (!deptModifiedTime.after(latestSuccessLog.getLastSyncDate())) {
						needSync = false;
						log.debug("部门{}已同步成功且无更新，跳过同步。部门修改时间：{}，最后同步时间：{}", deptInfo.getString("code"),
								deptModifiedTime, latestSuccessLog.getLastSyncDate());
					}
				}
			}

			if (needSync) {
				filteredList.add(deptInfo);
			}
		}

		return filteredList;
	}

	/**
	 * 获取异常堆栈信息
	 */
	private String getStackTrace(Exception e) {
		java.io.StringWriter sw = new java.io.StringWriter();
		java.io.PrintWriter pw = new java.io.PrintWriter(sw);
		e.printStackTrace(pw);
		return sw.toString();
	}

	/**
	 * 将WeakTypingDO列表转换为JSONObject列表
	 *
	 * @param deptList WeakTypingDO列表
	 * @return JSONObject列表
	 */
	private List<JSONObject> convertToJsonList(List<WeakTypingDO> deptList) {
		List<JSONObject> result = new ArrayList<>();

		if (CollectionUtils.isNotEmpty(deptList)) {
			for (WeakTypingDO dept : deptList) {
				result.add((JSONObject) JSON.toJSON(dept.toMap()));
			}
		}

		return result;
	}

	/**
	 * 判断NCC推送是否成功
	 *
	 * @param resp NCC响应结果
	 * @return 是否成功
	 */
	private boolean isNCPushSuccess(JSONObject resp) {
		try {
			return resp.getBooleanValue("success") && NCC_SUCCESS_CODE.equals(resp.getJSONObject("data")
					.getJSONObject("ufinterface").getJSONArray("sendresult").getJSONObject(0).getString("resultcode"));
		} catch (Exception e) {
			log.warn("解析NCC推送结果失败", e);
			return false;
		}
	}

	/**
	 * 判断同步日志是否更新
	 *
	 * @param current 当前日志
	 * @param latest  最新日志
	 * @return 是否更新
	 */
	private boolean isMoreRecent(DeptSyncLog current, DeptSyncLog latest) {
		return current.getLastSyncDate() != null && latest.getLastSyncDate() != null
				&& current.getLastSyncDate().after(latest.getLastSyncDate());
	}
}
