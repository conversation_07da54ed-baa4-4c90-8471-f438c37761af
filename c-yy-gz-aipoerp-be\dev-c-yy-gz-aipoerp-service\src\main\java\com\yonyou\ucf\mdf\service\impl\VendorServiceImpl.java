package com.yonyou.ucf.mdf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.customer.CustomerBill;
import com.yonyou.aipierp.dto.ncapi.customer.CustomerBillPf;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IVendorService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class VendorServiceImpl  implements IVendorService {

    NCOpenApiConfig ncOpenApiConfig;
    NCOpenApiService ncOpenApiService;
    AIPORepository aipoRepository;
//    SimpleDateFormat MerchantDateFormat;


    public VendorServiceImpl(NCOpenApiConfig ncOpenApiConfig,
                               NCOpenApiService ncOpenApiService,
                               AIPORepository aipoRepository) {
        this.ncOpenApiConfig = ncOpenApiConfig;
        this.ncOpenApiService = ncOpenApiService;
        this.aipoRepository = aipoRepository;
//        this.MerchantDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    }

    @Override
    public JSONObject pushVendorToNC(JSONObject oriSaveReq, JSONObject saveRuleReturn) {
        // 供应商档案暂不翻译子表
//         getFinalSavedMerchant(merchantId,oriSaveReq,saveRuleReturn);
        JSONObject saveBody = convertToNcSaveJsonBody(saveRuleReturn);
        JSONObject resp = ncOpenApiService.saveCustomer(saveBody);
        if (!resp.getBooleanValue("success") ){ // 客户申请单保存只校验success字段就行
            log.error("同步供应商档案信息至高级版系统失败 saveBody-->{},resp-->{}", saveBody, resp);
            throw new BusinessException("同步供应商档案信息至高级版系统失败，错误信息："+ resp.toJSONString());
        }
        return resp;
    }

    @Override
    public JSONObject deleteVendorFromNC(String vendorId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bipid",vendorId);
        jsonObject.put("version","1");
        JSONObject resp = ncOpenApiService.deleteCustomer(jsonObject);
        if (!resp.getBooleanValue("success")) {
            log.error("从高级版删除客户申请信息失败 req-->{},resp-->{}", jsonObject, resp);
            throw new BusinessException("从高级版删除客户申请信息失败，错误信息："+ resp.toJSONString());
        }
        return resp;
    }


    private JSONObject convertToNcSaveJsonBody(JSONObject bipVendorInfo){
        // 先查一下客户分类信息
        JSONObject vendorClass = aipoRepository.queryVendorClassById(bipVendorInfo.getString("vendorclass"));

        JSONObject res = new JSONObject();
        // 客户主表信息
        CustomerBill customerBill = new CustomerBill();
        // 客户申请单信息
        CustomerBillPf customerBillPf = new CustomerBillPf();

        // 先处理客户申请单
        customerBillPf.setPk_group(ncOpenApiConfig.getGroupcode());
        customerBillPf.setPk_org(bipVendorInfo.getString("org"));
        if ("666666".equals(bipVendorInfo.getString("org"))){
            customerBillPf.setDestorg("1"); // 企业账号级就是选【集团】
        }else {
            customerBillPf.setDestorg("0"); // 否则选【本组织】
        }

        customerBillPf.setCustomercode(bipVendorInfo.getString("code"));
        JSONObject mlName = bipVendorInfo.getJSONObject("name");
        if (mlName!=null){
            customerBillPf.setCustomername(mlName.getString("zh_CN"));
        }
//        customerBillPf.setCustomername(bipVendorInfo.getString("name")); 使用多语名称
        if (vendorClass!=null){
            customerBillPf.setPk_custclass(vendorClass.getString("code"));
        }
        customerBillPf.setUpdate_cust(bipVendorInfo.getString("id"));

        // 再处理客户档案主表
        if (bipVendorInfo.getBooleanValue("internalunit")){
            customerBill.setCustprop("1");
        }else {
            customerBill.setCustprop("0");
        }
        customerBill.setDef10(bipVendorInfo.getString("id"));

        res.put("billpf",customerBillPf);
        res.put("bill",customerBill);
        return res;
    }


}
