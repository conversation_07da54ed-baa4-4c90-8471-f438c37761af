package com.yonyou.ucf.mdf.travelexpense.service.impl;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QueryJoin;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.travelexpense.service.IPeerPsnService;
import com.yonyou.ucf.mdf.travelexpense.service.ITravelMemoApplyQryService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年4月2日
 */
@Slf4j
@Service
public class PeerPsnServiceImpl implements IPeerPsnService {

	@Autowired
	private ITravelMemoApplyQryService travelMemoApplyQryService;

	@Override
	public List<String> getPeerPsnIds(BizObject bizObject) {
		List<BizObject> bizObjects = bizObject.getBizObjects("expensebillbs", BizObject.class);
		if (CollectionUtils.isEmpty(bizObjects)) {
			return Collections.emptyList();
		}
		// 取出开始日期最早的一条
		BizObject startBiz = bizObjects.stream().sorted(Comparator
				.comparing(v -> ((BizObject) v).getString("dbegindate"), Comparator.nullsLast(String::compareTo))
				.thenComparing(v -> ((BizObject) v).getString("dbegintime"), Comparator.nullsLast(String::compareTo)))
				.findFirst().get();
		// 取结束日期最晚的一条
		BizObject endBiz = bizObjects.stream()
				.sorted(Comparator
						.comparing(v -> ((BizObject) v).getString("denddate"),
								Comparator.nullsLast(Comparator.reverseOrder()))
						.thenComparing(v -> ((BizObject) v).getString("dendtime"),
								Comparator.nullsLast(Comparator.reverseOrder())))
				.findFirst().get();

		String startBegindate = startBiz.getString("dbegindate");
		String startBegintime = startBiz.getString("dbegintime");
		String endEnddate = endBiz.getString("denddate");
		String endEndtime = endBiz.getString("dendtime");

		String transtypeCode = bizObject.getString("transtypeCode");

		return queryTravelMemoApplyPsn(startBegindate, startBegintime, endEnddate, endEndtime, transtypeCode);
	}

	/**
	 * 根据时间范围查询出差申请单在时间范围内有出差申请的人员id（只要出差申请时间范围有交叉就行） 只判断日期交叉的情况，时间不管，不然逻辑太复杂
	 * 
	 * @param startBegindate
	 * @param startBegintime
	 * @param endEnddate
	 * @param endEndtime
	 * @param transtypeCode  TODO
	 * @return
	 */
	private List<String> queryTravelMemoApplyPsn(String startBegindate, String startBegintime, String endEnddate,
			String endEndtime, String transtypeCode) {
		QuerySchema schema = QuerySchema.create();
		schema.distinct();
		// 设置主表字段
		schema.addSelect("MemoapplyBVO.pk_handlepsn as pk_handlepsn"); // 申请人
		schema.distinct();
		schema.addSelect(new QueryField("bustype", "bustype", null, "bd.bill.TransType/id"));
		schema.addJoin(new QueryJoin("MemoapplyBVO", null, "left"));

		// 起始日期小于
		QueryConditionGroup cond1 = QueryConditionGroup.and(
				QueryCondition.name("MemoapplyBVO.dbegindate").elt(startBegindate),
				QueryCondition.name("MemoapplyBVO.denddate").egt(startBegindate));

		QueryConditionGroup cond2 = QueryConditionGroup.and(
				QueryCondition.name("MemoapplyBVO.dbegindate").egt(startBegindate),
				QueryCondition.name("MemoapplyBVO.dbegindate").elt(endEnddate));

		QueryConditionGroup condition = QueryConditionGroup.and(QueryCondition.name("verifystate").eq("2"),
				QueryCondition.name("isclose").eq("0"), QueryConditionGroup.or(cond1, cond2));

		log.error("transtypeCode----------:{}", transtypeCode);
		if ("RBSM00604".equals(transtypeCode)) {
			condition.addCondition(QueryCondition.name("bustype.code").eq("WXSQ1"));
		} else {
			condition.addCondition(QueryCondition.name("bustype.code").eq("RBSM001"));
		}

		schema.addCondition(condition);

		List<Map<String, Object>> result = travelMemoApplyQryService.queryTravelMemoApply(schema);
		if (result.isEmpty()) {
			return Collections.emptyList();
		}

		return result.stream().map(m -> {
			return m.get("pk_handlepsn").toString();
		}).collect(Collectors.toList());
	}
}
