package com.yonyou.ucf.mdf.aipo.model;

import lombok.Data;
import java.io.Serializable;

@Data
public class UpdateMessage implements Serializable {

    /**
     * 业务系统待办主键
     */
    private String taskId;

    //接收人编号（手机号）,多值
    //时用英文状态下的逗号分隔
    private String receiver;
    /**
     * 状态，0未办理，1已办理
     */
    private String state;

    /**
     * 处理后状态：0/1/2/3同意已办/不同意已办/取消/驳回
     */
    private String subState;

    //PC 端穿透 URL 地址
    private String pcUrl;
    //移动端穿透 URL 地址
    private String appUrl;
    //原生应用穿透命令，穿透命令 需 要 按 这 个 顺 序 ：
    //iphone|ipad|android|wp
    private String appParam;

    //备注
    private String remarks;



}

