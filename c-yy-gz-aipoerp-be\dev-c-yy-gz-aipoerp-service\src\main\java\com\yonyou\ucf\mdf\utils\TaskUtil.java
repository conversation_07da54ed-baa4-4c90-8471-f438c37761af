package com.yonyou.ucf.mdf.utils;

import static org.apache.commons.codec.digest.MessageDigestAlgorithms.MD5;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;


public class TaskUtil {

    public static Object ok(String id, String msg, String content) {
        JSONObject res = new JSONObject();
        res.put("status","1");//执行成功status传1 失败传0
        res.put("id",id);
        res.put("msg",msg);
        res.put("content",content);
        return res;
    }

    public static Object error(String id, String msg, String content) {
        JSONObject res = new JSONObject();
        res.put("status","0");//执行成功status传1 失败传0
        res.put("id",id);
        res.put("msg",msg);
        res.put("content",content);
        return res;
    }

    /**
     * MD5加密--数据中台接口加密
     *
     * @param data 待加密字符串
     * @return 加密后的字符串
     */
    public static String MD5(String data) {
        if (StringUtils.isBlank(data)) {
            throw new RuntimeException("加密字符串不能为空");
        }

        // 获取MessageDigest对象，并指定使用MD5算法
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance(MD5);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5加密失败");
        }

        // 计算输入字符串的哈希值
        byte[] digest = md.digest(data.getBytes());
        // 将字节数组转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

}
