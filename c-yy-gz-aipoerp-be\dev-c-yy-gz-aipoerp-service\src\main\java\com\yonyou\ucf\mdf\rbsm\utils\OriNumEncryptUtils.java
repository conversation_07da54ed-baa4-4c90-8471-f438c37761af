package com.yonyou.ucf.mdf.rbsm.utils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.context.ApplicationContext;

import com.yonyou.cloud.utils.CollectionUtils;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.api.IYmsJdbcApi;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.utils.AIPOSpringContextHolder;
import com.yonyou.yht.sdkutils.StringUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OriNumEncryptUtils {

	public static final String secretKey = "{0}-dataenc";

	private static IYmsJdbcApi ymsJdbcApi = null;
	private static ApplicationContext appContext = null;

	private static final Map<String, String> tenantKeys = new HashMap<>();

	public static void setAppContext(ApplicationContext applicationContext) {
		appContext = applicationContext;
	}

	private static IYmsJdbcApi getYmsJdbcApi() {
		if (Objects.isNull(ymsJdbcApi)) {
			IYmsJdbcApi tmp = AIPOSpringContextHolder.getBean(IYmsJdbcApi.class);
			if (Objects.isNull(tmp)) {
				throw new RuntimeException("找不到 insureBaseDAO bean");
			}
			ymsJdbcApi = tmp;
		}
		return ymsJdbcApi;
	}

	private static String getKey() {
		return tenantKeys.computeIfAbsent(InvocationInfoProxy.getTenantid(), tenantId -> {
			SQLParameter sqlParameter = new SQLParameter();
			sqlParameter.addParam("encryption");
			sqlParameter.addParam(tenantId);
			List<Map<String, Object>> results = getYmsJdbcApi().queryForList(
					"select id , code , extend_field1  from hr_sinsurance.ss_tenant_config_table where enable = 1 and dr = 0 and code = ? and ytenant_id = ?",
					sqlParameter, new MapListProcessor());
			if (CollectionUtils.isNotEmpty(results)
					&& StringUtils.isNotBlank((String) results.get(0).get("extend_field1"))) {
				return (String) results.get(0).get("extend_field1");
			}
			return tenantId;
		});
	}

	public static Map<String, String> getTenantKeys() {
		return tenantKeys;
	}

	public static BigDecimal encrypt(BigDecimal bigDecimal) {

		bigDecimal = bigDecimal.setScale(8, BigDecimal.ROUND_HALF_UP);
		String numString = bigDecimal.toPlainString();
		String secretNumKey = String.valueOf(Math.abs(MessageFormat.format(secretKey, getKey()).hashCode()));
		return new BigDecimal(secret(numString, secretNumKey, true));
	}

	public static String getKeyForConfig() {
		String secretNumKey = String.valueOf(Math.abs(MessageFormat.format(secretKey, getKey()).hashCode()));
		return secretNumKey;
	}

	public static BigDecimal decode(BigDecimal bigDecimal) {
		BigDecimal result = NumEncryptUtils.decode(bigDecimal, MessageFormat.format(secretKey, getKey()));
		return result.setScale(8, BigDecimal.ROUND_HALF_UP);
	}

	public static String decode(String bigDecimal) {
		if (StringUtils.isBlank(bigDecimal)) {
			return null;
		}
		return decode(new BigDecimal(bigDecimal)).toString();
	}

	public static String secret(String numString, String secretNumKey, boolean isEncrypt) {
		boolean negative = numString.startsWith("-");
		if (negative) {
			numString = numString.substring(1);
		}

		char[] numChars = numString.toCharArray();
		int offset = isEncrypt ? 0 : 1;
		int numLength = numChars.length - offset;
		if (!isEncrypt && numLength < 2) {
			throw new RuntimeException("decode fail, This number is illegal!");

		} else {
			char[] keyChars = secretNumKey.toCharArray();
			StringBuffer newValue = new StringBuffer();
			int keyLength = keyChars.length + offset;

			for (int i = offset; i < numLength; ++i) {
				if ('.' != numChars[i] && i < keyLength) {
					int tmpNum = Integer.valueOf(numChars[i]) ^ Integer.valueOf(keyChars[i - offset]);
					tmpNum = tmpNum < 10 ? tmpNum : Integer.valueOf(numChars[i]) - 48;
					newValue.append(tmpNum);
				} else {
					newValue.append(numChars[i]);
				}
			}

			if (isEncrypt) {
				newValue.insert(0, keyChars[0]);
				newValue.append(keyChars[0]);
			}

			if (negative) {
				newValue.insert(0, "-");
			}

			return String.valueOf(newValue);
		}
	}

	public static String getEncColumn(String colName) {
		String secretNumKey = "cast(diwork_wa_mdd.encodestr(cast(" + colName + " as char(36)) ,'"
				+ String.valueOf(Math.abs(MessageFormat.format(secretKey, getKey()).hashCode()))
				+ "' ,0) as decimal(36,9))";
		return secretNumKey;// MessageFormat.format("encodestr({0},"+secretNumKey+",0) as
							// {1}",colName,colName);
	}

	public static String getEncColumnWithoutCast(String colName) {
		String secretNumKey = "encodestr(" + colName + ",'"
				+ String.valueOf(Math.abs(MessageFormat.format(secretKey, getKey()).hashCode())) + "' ,0)";
		return secretNumKey;// MessageFormat.format("encodestr({0},"+secretNumKey+",0) as
							// {1}",colName,colName);
	}

	public static void main(String[] args) {
//          InvocationInfoProxy.setExtendAttribute("tenantId","h5b7uh2p");
		log.debug(MessageFormat.format(secretKey, getKey()));
		log.debug(getEncColumn("f_n_1"));
		log.debug(OriNumEncryptUtils.encrypt(new BigDecimal("0.0")) + "");
	}

	public static BigDecimal decodeExt(BigDecimal bigDecimal) {
		if (bigDecimal != null) {
			BigDecimal result = NumEncryptUtils.decode(bigDecimal, MessageFormat.format(secretKey, getKey()));
			return result.setScale(2, BigDecimal.ROUND_HALF_UP);
		}
		return null;
	}
}
