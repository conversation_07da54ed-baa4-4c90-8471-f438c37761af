package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IProjectService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Deprecated
@Slf4j
@Component("aipoProjectAfterDeleteRule")
public class AIPOProjectAfterDeleteRule implements IYpdCommonRul {

    @Autowired
    IProjectService projectService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        JSONObject deleteParams = JSON.parseObject(JSON.toJSONString(params));
        if (MapUtils.isEmpty(deleteParams.getJSONObject("requestData"))){
            log.error("无法获取到项目删除的详细信息以删除高级版项目，请重试 deleteParams-->{}",deleteParams);
            throw new BusinessException("无法获取到项目删除的详细信息以删除高级版项目，请重试");
        }else {
            JSONObject requestData = deleteParams.getJSONObject("requestData");
            if ("666666".equals(requestData.getString("orgid"))){
                log.error("所属组织为【企业账号级】不是公司级，不同步 requestData-->{}", requestData);
            }else {
                projectService.deleteProjectFromNC(requestData.getString("id"),requestData.getString("orgid"));
            }

        }


        return result;
    }
}
