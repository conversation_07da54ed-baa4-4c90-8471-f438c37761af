package com.yonyou.ucf.mdf.travelexpense.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QuerySchema;

/**
 * <AUTHOR>
 *
 * 2025年4月14日
 */
public interface ITravelExpenseQryService {

	/**
	 * 根据账单发生日期和报销人员，陪同人员查询差旅费报销单
	 * 
	 * @param handlepsnMap
	 * @return
	 */
	List<Map<String, Object>> queryByDcostdateAndHandlepsn(Map<String, Set<String>> handlepsnMap);

	/**
	 * 根据id查询差旅费报销单（该方法只查询主表，不会查询出子表数据）
	 * 
	 * @param id
	 * @return
	 */
	BizObject queryByid(String id);

	/**
	 * 根据查询方案查询
	 * 
	 * @param schema
	 * @return
	 */
	List<Map<String, Object>> queryBySchema(QuerySchema schema);

}
