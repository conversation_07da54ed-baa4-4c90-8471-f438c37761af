package com.yonyou.ucf.mdf.iris.model;

/**
 * <AUTHOR>
 * @date 2025/4/16 18:28
 * @DESCRIPTION 类描述
 */
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileNode {
    private List<FileNode> children;
    private String code;
    private Boolean disabled;
    private Boolean expanded;
    private Boolean hidden;
    private String id;
    private Boolean leaf;
    private FileProperties properties;
    private String text;
    private Integer type;
    private String url;
}

