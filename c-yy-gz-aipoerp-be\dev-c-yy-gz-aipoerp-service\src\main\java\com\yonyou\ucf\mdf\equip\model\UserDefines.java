package com.yonyou.ucf.mdf.equip.model;

import java.util.Map;
import com.yonyou.ypd.bill.basic.bean.CharacteristicsEntity;

/**
 * <AUTHOR>
 * @description 自定义特征项
 * @Date 2025-04-08 16:18:37
 * @since 2023/11/28
 **/
public class UserDefines extends CharacteristicsEntity {
    public static final String ENTITY_NAME = "ZCEDI.ZCEDI.userDefines";
    public UserDefines () {}
    public UserDefines (Map<String, Object> extProperty) {super(extProperty);}
    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().contains("#PT#.")) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
