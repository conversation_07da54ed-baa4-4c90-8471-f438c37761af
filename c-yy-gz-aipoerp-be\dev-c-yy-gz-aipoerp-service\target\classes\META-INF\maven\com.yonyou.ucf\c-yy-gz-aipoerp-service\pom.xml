<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>c-yy-gz-aipoerp</artifactId>
        <groupId>com.yonyou.ucf</groupId>
        <version>ddm-3.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>c-yy-gz-aipoerp-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yonyou.iuap</groupId>
            <artifactId>iuap-ap-ypd-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>iuap-ap-bizflow_mdd-sdk</artifactId>
                    <groupId>com.yonyou.iuap</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yonyou.ucf</groupId>
            <artifactId>c-yy-gz-aipoerp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-dysmsapi20170525</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!--   r6调度任务sdk     -->
        <dependency>
            <groupId>com.yonyou.iuap</groupId>
            <artifactId>iuap-ap-dispatch-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yonyoucloud.ctm</groupId>
            <artifactId>stwb-plugin-define</artifactId>
            <version>8.2.129-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.cfca</groupId>
            <artifactId>sadk</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>com.cfca</groupId>
            <artifactId>logback-cfca</artifactId>
            <version>4.2.0.1</version>
        </dependency>
    </dependencies>

</project>