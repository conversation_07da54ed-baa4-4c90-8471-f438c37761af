package com.yonyou.ucf.mdf.rbsm.model;

import java.sql.ResultSet;

import com.yonyou.iuap.yms.processor.BaseProcessor;

/**
 * <AUTHOR>
 *
 *         2025年3月12日
 */
public class StringProcessor extends BaseProcessor {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	public String processResultSet(ResultSet rs) throws Exception {
		if (rs.next()) {
			return rs.getString(1);
		}
		return null;
	}

}
