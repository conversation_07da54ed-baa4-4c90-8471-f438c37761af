package com.yonyou.ucf.mdf.rbsm.model;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 薪资发放数据
 * 
 * <AUTHOR>
 *
 *         2025年3月10日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WaData {
	private List<WaPayfile> waPayfileVOList; // List对象，存储返回的所有发放单对象
	private Map<String, List<WaItem>> itemList; // Map对象，key是发放单id，value是一个存储对应发放单下的薪资项目的List
	private Map<String, List<JSONObject>> waPayfileDetailList; // Map对象，key是发放单id，value存储对应发放单下人员发薪详情信息的List

}
