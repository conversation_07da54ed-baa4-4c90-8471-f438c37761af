package com.yonyou.ucf.mdf.travelexpense.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.rbsm.model.StringProcessor;
import com.yonyou.ucf.mdf.travelexpense.service.IBustypeQryService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

/**
 * <AUTHOR>
 *
 * 2025年4月1日
 */
@Service
public class BustypeQryServiceImpl implements IBustypeQryService {

	@Autowired
	private IBillRepository billRepository;

	@Override
	public String queryBusTypeCodeById(String id) {
		if (StringUtils.isBlank(id)) {
			return null;
		}
		String sql = "select code from iuap_apdoc_basedoc.bd_transtype where id = ?";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(id);

		return billRepository.queryForObject(sql, parameter, new StringProcessor());

	}

}
