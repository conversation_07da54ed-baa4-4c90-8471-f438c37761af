package com.yonyou.ucf.mdf.aipo.model;

import java.util.List;
import java.util.Map;

public class MessageValue {

    /**
     * 待办标题
     */
    private String title;
    /**
     * 移动端链接
     */
    private String mUrl;
    /**
     * web端链接
     */
    private String webUrl;
    /**
     * 发起人id
     */
    private List<String> yyUserIds;
    /**
     * 提交人id
     */
    private String commitUserId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 待办发起时间
     */
    private long commitTsLong;

    /**
     * businessKey
     */
    private String businessKey;

    /**
     * todoTemplateVars
     */
    private Map<String, String> todoTemplateVars;

    public MessageValue() {
    }

    public MessageValue(String title, String mUrl, String webUrl, List<String> yyUserIds, String commitUserId, String content, Long commitTsLong, String businessKey, Map<String, String> todoTemplateVars) {
        this.title = title;
        this.mUrl = mUrl;
        this.webUrl = webUrl;
        this.yyUserIds = yyUserIds;
        this.commitUserId = commitUserId;
        this.content = content;
        this.commitTsLong = commitTsLong;
        this.businessKey = businessKey;
        this.todoTemplateVars = todoTemplateVars;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getmUrl() {
        return mUrl;
    }

    public void setmUrl(String mUrl) {
        this.mUrl = mUrl;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public List<String> getYyUserIds() {
        return yyUserIds;
    }

    public void setYyUserIds(List<String> yyUserIds) {
        this.yyUserIds = yyUserIds;
    }

    public String getCommitUserId() {
        return commitUserId;
    }

    public void setCommitUserId(String commitUserId) {
        this.commitUserId = commitUserId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getCommitTsLong() {
        return commitTsLong;
    }

    public void setCommitTsLong(Long commitTsLong) {
        this.commitTsLong = commitTsLong;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public Map<String, String> getTodoTemplateVars() {
        return todoTemplateVars;
    }

    public void setTodoTemplateVars(Map<String, String> todoTemplateVars) {
        this.todoTemplateVars = todoTemplateVars;
    }

    @Override
    public String toString() {
        return "MessageValue{" +
                "title='" + title + '\'' +
                ", mUrl='" + mUrl + '\'' +
                ", webUrl='" + webUrl + '\'' +
                ", yyUserIds=" + yyUserIds +
                ", commitUserId='" + commitUserId + '\'' +
                ", content='" + content + '\'' +
                ", commitTsLong=" + commitTsLong +
                ", businessKey='" + businessKey + '\'' +
                ", todoTemplateVars=" + todoTemplateVars +
                '}';
    }
}