package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPaymentInstitutionService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 *         2025年4月7日
 */
@Service
public class PaymentInstitutionService implements IPaymentInstitutionService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@Override
	public Map<String, Map<String, Object>> queryAllForMap() {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("id,piCode,piName,enable,piRemark,piType");
		QueryConditionGroup cond = QueryConditionGroup.and(QueryCondition.name("enable").eq("1"),
				QueryCondition.name("dr").eq("0"), QueryCondition.name("tenant").eq(InvocationInfoProxy.getTenantid()));
		schema.addCondition(cond);
		List<Map<String, Object>> result = billQryRepository.queryMapBySchema("si.payinst.PaymentInstitution", schema,
				"hrcloud-sinsurance");
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyMap();
		}
		return result.stream().collect(Collectors.toMap(v -> v.get("id").toString(), v -> v, (v1, v2) -> v2));
	}

}
