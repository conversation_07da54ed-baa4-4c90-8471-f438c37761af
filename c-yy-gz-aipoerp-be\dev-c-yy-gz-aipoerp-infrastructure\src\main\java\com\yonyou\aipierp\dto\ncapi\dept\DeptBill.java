package com.yonyou.aipierp.dto.ncapi.dept;

import com.alibaba.fastjson.JSONObject;

public class DeptBill extends JSONObject {

    /**
     * 承载单据
     */
    public String BILLHEAD = "billhead";

    /**
     * "testid1209-1"(bip旗舰版的部门id)
     */
    public String ID = "id";

    public static DeptBill init(DeptBillHead billHead, String id){
        DeptBill deptBill = new DeptBill();
        deptBill.setBillHead(billHead);
        deptBill.setId(id);
        return deptBill;
    }

    public DeptBillHead getBillHead() {
        return (DeptBillHead) this.getJSONObject(BILLHEAD);
    }

    public void setBillHead(DeptBillHead billhead) {
        this.put(BILLHEAD, billhead);
    }

    public String getId() {
        return this.getString(ID);
    }

    public void setId(String id) {
        this.put(ID, id);
    }

}
