package com.yonyou.ucf.mdf.dept.service.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.dept.entity.DeptSyncLog;
import com.yonyou.ucf.mdf.dept.service.DeptSyncLogService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * 部门同步日志服务实现类
 */
@Service
public class DeptSyncLogServiceImpl implements DeptSyncLogService {

    @Autowired
    private IBillCommonRepository billCommonRepository;
    @Autowired
    private IBillQueryRepository billQryRepository;

    @Override
    public DeptSyncLog save(DeptSyncLog syncLog) {
        try {
            List<IBillDO> billDOs = Lists.newArrayList(syncLog);
            billDOs = billCommonRepository.commonSaveBill(billDOs, "DeptSyncLog");
            return (DeptSyncLog) billDOs.get(0);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DeptSyncLog> findByDeptId(String deptId) {
        if (StringUtils.isBlank(deptId)) {
            return Collections.emptyList();
        }
        QuerySchema schema = QuerySchema.create().addSelect("*");
        schema.addCondition(QueryConditionGroup.and(QueryCondition.name("deptId").eq(deptId)));
        schema.addOrderBy("createTime desc");
        return findBySchema(schema);
    }

    @Override
    public List<DeptSyncLog> findByDeptCode(String deptCode) {
        if (StringUtils.isBlank(deptCode)) {
            return Collections.emptyList();
        }
        QuerySchema schema = QuerySchema.create().addSelect("*");
        schema.addCondition(QueryConditionGroup.and(QueryCondition.name("deptCode").eq(deptCode)));
        schema.addOrderBy("createTime desc");
        return findBySchema(schema);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<DeptSyncLog> findBySchema(QuerySchema schema) {
        return (List<DeptSyncLog>) billQryRepository
                .queryBySchema("AIPOERPCREATE.AIPOERPCREATE.DeptSyncLog", schema);
    }

    @Override
    public List<DeptSyncLog> findAllFailed() {
        QuerySchema schema = QuerySchema.create().addSelect("*");
        schema.addCondition(QueryConditionGroup.and(QueryCondition.name("success").eq("N")));
        schema.addOrderBy("createTime desc");
        return findBySchema(schema);
    }

    @Override
    public List<DeptSyncLog> findByOrgId(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return Collections.emptyList();
        }
        QuerySchema schema = QuerySchema.create().addSelect("*");
        schema.addCondition(QueryConditionGroup.and(QueryCondition.name("orgId").eq(orgId)));
        schema.addOrderBy("createTime desc");
        return findBySchema(schema);
    }

    @Override
    public List<DeptSyncLog> findBySuccess(String success) {
        if (StringUtils.isBlank(success)) {
            return Collections.emptyList();
        }
        QuerySchema schema = QuerySchema.create().addSelect("*");
        schema.addCondition(QueryConditionGroup.and(QueryCondition.name("success").eq(success)));
        schema.addOrderBy("createTime desc");
        return findBySchema(schema);
    }
}
