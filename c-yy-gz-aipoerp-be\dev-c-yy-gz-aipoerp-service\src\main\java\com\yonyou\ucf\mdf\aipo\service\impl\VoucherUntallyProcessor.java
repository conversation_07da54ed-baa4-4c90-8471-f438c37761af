package com.yonyou.ucf.mdf.aipo.service.impl;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.aipo.dto.MsgForwardDto;
import com.yonyou.ucf.mdf.aipo.model.AIPOVoucherEventLog;
import com.yonyou.ucf.mdf.aipo.model.EventContent;
import com.yonyou.ucf.mdf.aipo.model.EventProcessResult;
import com.yonyou.ucf.mdf.aipo.model.EventProcessStatus;
import com.yonyou.ucf.mdf.aipo.model.EventType;
import com.yonyou.ucf.mdf.aipo.model.VoucherEventLogDetail;
import com.yonyou.ucf.mdf.aipo.service.AIPOVoucherEventLogService;
import com.yonyou.ucf.mdf.aipo.service.IAipoVoucherSyncLogService;
import com.yonyou.ucf.mdf.aipo.service.IMsgForwardService;
import com.yonyou.ucf.mdf.aipo.service.VoucherEventProcessor;
import com.yonyou.ucf.mdf.aipo.utils.DateTimeFormatterUtil;
import com.yonyou.ucf.mdf.aipo.vo.AIPOVoucherSyncLog;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 凭证取消记账事件处理器
 */
@Component
@Slf4j
public class VoucherUntallyProcessor extends VoucherEventProcessor {

	// 常量定义
	private static final String SUCCESS_CODE = "200";
	private static final int MAX_STACK_TRACE_LENGTH = 4000;
	private static final String VOUCHER_SYNC_UNTALLY = "凭证同步-取消记账";

	@Autowired
	private AIPOVoucherEventLogService eventLogService;
	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private IMsgForwardService msgForwardService;
	@Autowired
	private IAipoVoucherSyncLogService syncLogService;

	public VoucherUntallyProcessor() {
		super(EventType.GL_VOUCHER_EVENT_UNTALLY_AFLTER);
	}

	@Override
	protected void doProcessEvent(EventContent eventContent) {
		long start = System.currentTimeMillis();
		String content = eventContent.getContent();
		log.error("处理凭证取消记账事件，内容：{}", content);
		AIPOVoucherEventLog eventLog = getEventLog(eventContent);
		if (eventLog == null) {
			return;
		}
		// 先保存事件日志
		eventLog = eventLogService.save(eventLog);
		try {
			boolean successFlag = true;
			List<VoucherEventLogDetail> details = eventLog.getVoucherEventLogDetailList();
			for (VoucherEventLogDetail voucherEventLogDetail : details) {
				long begin = System.currentTimeMillis();
				AIPOVoucherSyncLog syncLog = getVoucherSyncLog(eventLog, voucherEventLogDetail);

				try {

					// 先保存日志
					syncLog = syncLogService.save(syncLog);

					// 获取凭证审核信息
					JSONObject voucher = queryVoucherData(syncLog.getBillid(), syncLog.getYtenantId());

					// 设置同步日志凭证信息
					setSyncLogVoucherInfo(syncLog, voucher);

					// 封装凭证同步信息
					voucher.put("action", eventLog.getEventType());
					MsgForwardDto msgForwardDto = new MsgForwardDto();
					msgForwardDto.setReqType("post");
					msgForwardDto.setUrl("pz_action");
					msgForwardDto.setData(voucher);

					// 推送前设置日志推送数据信息
					syncLog.setSendmsg(voucher.toJSONString());
					// 调用接口同步凭证到NCC高级版
					String req = msgForwardService.msgForward(msgForwardDto);
					// 推送后日志记录返回信息
					syncLog.setRetmsg(req);

					validateResponse(req);

					// 设置日志同步成功标识
					syncLog.setIssuccess(EventProcessResult.Y.getCode());
					syncLog.set_status(ActionEnum.UPDATE.getValueInt());

					// 记录事件明细处理结果和状态，以及耗费时间
					long end = System.currentTimeMillis();
					long costTime = end - begin;
					voucherEventLogDetail.setCostTime((int) costTime);
					voucherEventLogDetail.setRequestData(syncLog.getSendmsg());
					voucherEventLogDetail.setResponseData(syncLog.getRetmsg());
					voucherEventLogDetail.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
					voucherEventLogDetail.setProcessResult(EventProcessResult.Y.getCode());
					voucherEventLogDetail.set_status(ActionEnum.UPDATE.getValueInt());

				} catch (Exception e) {
					log.error("凭证同步失败：{}", e.getMessage(), e);
					// 记录存在失败的处理
					successFlag = false;
					String stackTrace = ExceptionUtils.getStackTrace(e);
					if (stackTrace != null && stackTrace.length() > MAX_STACK_TRACE_LENGTH) {
						stackTrace = stackTrace.substring(0, MAX_STACK_TRACE_LENGTH - 2);
					}

					// 设置日志记录异常信息，并更新同步结果为失败
					syncLog.setIssuccess(EventProcessResult.N.getCode());
					syncLog.setErrormsg(e.getMessage());
					syncLog.set_status(ActionEnum.UPDATE.getValueInt());

					// 记录事件明细处理结果和状态，以及耗费时间
					long end = System.currentTimeMillis();
					long costTime = end - begin;
					voucherEventLogDetail.setCostTime((int) costTime);
					voucherEventLogDetail.setErrorMsg(e.getMessage());
					voucherEventLogDetail.setProcessResult(EventProcessResult.N.getCode());
					voucherEventLogDetail.setRequestData(syncLog.getSendmsg());
					voucherEventLogDetail.setResponseData(syncLog.getRetmsg());
					voucherEventLogDetail.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
					voucherEventLogDetail.setExceptionStack(stackTrace); // 记录异常堆栈信息
					voucherEventLogDetail.set_status(ActionEnum.UPDATE.getValueInt());
				} finally {
					// 最后一定要保存每一条同步日志记录
					syncLogService.save(syncLog);
					// 更新凭证同步状态
					updateVoucherStatus(syncLog.getBillid(), syncLog.getErrormsg() , syncLog.getIssuccess());
				}

			}

			// 记录整个事件处理耗费时间
			long end = System.currentTimeMillis();
			long costTime = end - start;
			eventLog.setCostTime((int) costTime);
			if (successFlag) { // 如果所有明细都正常同步成功，则整体时间处理成功
				eventLog.setProcessResult(EventProcessResult.Y.getCode());
			} else { // 否则算处理失败
				eventLog.setProcessResult(EventProcessResult.N.getCode());
			}
			eventLog.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
			eventLog.set_status(ActionEnum.UPDATE.getValueInt());
		} catch (Exception e) {
			log.error("处理凭证取消记账事件失败", e);

			// 如果事件处理发生异常，记录异常信息
			long end = System.currentTimeMillis();
			long costTime = end - start;
			eventLog.setCostTime((int) costTime);
			eventLog.setProcessResult(EventProcessResult.N.getCode());
			eventLog.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
			eventLog.setErrorMsg(e.getMessage());
			eventLog.set_status(ActionEnum.UPDATE.getValueInt());
		} finally {
			// 最后保存整个事件处理日志记录（包含事件明细处理记录）
			eventLogService.save(eventLog);
		}
	}

	/**
	 * 更新凭证状态
	 */
	private void updateVoucherStatus(String voucherId, String errorMsg, String isSuccess) {
		try {
			String sql = "UPDATE figl.fi_voucher set def25 = ?, def26 = ? WHERE id = ?";
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(errorMsg);
			parameter.addParam(isSuccess);
			parameter.addParam(voucherId);
			billRepository.update(sql, parameter);
		} catch (Exception e) {
			log.error("更新凭证状态异常", e);
		}
	}

	/**
	 * 验证响应结果
	 */
	private void validateResponse(String response) {
		JSONObject jsonObject = JSONObject.parseObject(response);
		if (!SUCCESS_CODE.equals(jsonObject.getString("code"))) {
			throw new RuntimeException("同步失败:" + jsonObject.getString("message"));
		}
	}

	/**
	 * 设置同步日志凭证信息
	 * 
	 * @param syncLog
	 * @param voucher
	 */
	private void setSyncLogVoucherInfo(AIPOVoucherSyncLog syncLog, JSONObject voucher) {
		syncLog.setBillcode(voucher.getOrDefault("billcode", "").toString());
		syncLog.setVoucherperiod(voucher.getOrDefault("period", "").toString());
		syncLog.setAcccode(voucher.getOrDefault("acccode", "").toString());
		syncLog.setAccname(voucher.getOrDefault("acccname", "").toString());
		syncLog.setVouchertypecode(voucher.getOrDefault("vouchertypecode", "").toString());
	}

	/**
	 * 查询凭证审核、记账记录
	 * 
	 * @param voucherId
	 * @param ytenantId
	 * @return
	 */
	private JSONObject queryVoucherData(String voucherId, String ytenantId) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select                                      ");
		sql.append(" 	aa.id,									 ");
		sql.append(" 	aa.id as voucherid,						 ");
		sql.append(" 	aa.billcode,							 ");
		sql.append(" 	aa.period,								 ");
		sql.append(" 	ee.code as vouchertypecode,				 ");
		sql.append(" 	ea.code as acccode,						 ");
		sql.append(" 	ea.name as acccname ,					 ");
		sql.append(" 	ff.code as auditorcode,					 ");
		sql.append(" 	ff.user_id as auditorid,				 ");
		sql.append(" 	ff.mobile as auditmobile,				 ");
		sql.append(" 	gg.code as auditstaffcode,				 ");
		sql.append(" 	gg.id as auditstaffid,					 ");
		sql.append(" 	aa.audittime ,							 ");
		sql.append(" 	aa.tallytime ,							 ");
		sql.append(" 	hh.code as tallymancode,				 ");
		sql.append(" 	hh.user_id as tallymanid,				 ");
		sql.append(" 	hh.mobile as tallymanmobile,			 ");
		sql.append(" 	jj.code as tallymanstaffcode,			 ");
		sql.append(" 	jj.id as tallymanstaffid				 ");
		sql.append(" from										 ");
		sql.append(" 	figl.fi_voucher aa						 ");
		sql.append(" inner join FIEPUB.epub_accountbook ea on	 ");
		sql.append(" 	ea.id = aa.accbook						 ");
		sql.append(" inner join FIEPUB.epub_vouchertype ee on	 ");
		sql.append(" 	ee.id = aa.vouchertype					 ");
		sql.append(" left join IUAP_APCOM_AUTH.ba_user ff on		 ");
		sql.append(" 	aa.auditor = ff.user_id					 ");
		sql.append(" left join IUAP_APDOC_BASEDOC.bd_staff gg on	 ");
		sql.append(" 	gg.user_id = ff.user_id					 ");
		sql.append(" left join IUAP_APCOM_AUTH.ba_user hh on		 ");
		sql.append(" 	aa.tallyman = hh.user_id				 ");
		sql.append(" left join IUAP_APDOC_BASEDOC.bd_staff jj on	 ");
		sql.append(" 	jj.user_id = hh.user_id					 ");
		sql.append(" where										 ");
		sql.append(" 	aa.dr = 0								 ");
		sql.append(" 	and aa.tenantid = ?			             ");
		sql.append(" 	and ea.dr = 0							 ");
		sql.append(" 	and ee.dr = 0							 ");
		sql.append(" 	and aa.id = ?	                         ");

		SQLParameter parameter = new SQLParameter();
		parameter.addParam(ytenantId);
		parameter.addParam(voucherId);

		List<Map<String, Object>> voucherMap = billRepository.queryForList(sql.toString(), parameter,
				new MapListProcessor());

		if (CollectionUtils.isEmpty(voucherMap)) {
			log.error("根据凭证id：{}，tenantId：{}，未查询到凭证取消记账信息！", voucherId, ytenantId);
			String msg = String.format("根据凭证id：%s，tenantId：%s，未查询到凭证取消记账信息！", voucherId, ytenantId);
			throw new RuntimeException(msg);
		}
		return JSONObject.parseObject(JSONObject.toJSONString(voucherMap.get(0)));
	}

	/**
	 * 生成同步日志
	 * 
	 * @param eventLog
	 * @param voucherEventLogDetail
	 * @return
	 */
	private AIPOVoucherSyncLog getVoucherSyncLog(AIPOVoucherEventLog eventLog,
			VoucherEventLogDetail voucherEventLogDetail) {
		AIPOVoucherSyncLog syncLog = new AIPOVoucherSyncLog();
		syncLog.setEntertime(DateTimeFormatterUtil.hmsDate(LocalDateTime.now()));
		syncLog.setYtenantId(eventLog.getYtenantId());
		syncLog.setDef1(eventLog.getBusinessDate());
		syncLog.setEventid(eventLog.getEventid());
		syncLog.setBillid(voucherEventLogDetail.getBillid());
		syncLog.setBillcode(voucherEventLogDetail.getBillcode());
		syncLog.setVoucherperiod(voucherEventLogDetail.getVoucherperiod());
		syncLog.setSynctype(VOUCHER_SYNC_UNTALLY);
		syncLog.setSyncdata(eventLog.getEventType());
		syncLog.setIssuccess(EventProcessResult.Y.getCode());
		syncLog.set_status(ActionEnum.INSERT.getValueInt());
		return syncLog;
	}

	/**
	 * 获取事件日志，先查询是否已存在事件
	 * 
	 * @param eventContent
	 * @return
	 */
	private AIPOVoucherEventLog getEventLog(EventContent eventContent) {
		String eventId = eventContent.getEventId();
		AIPOVoucherEventLog eventLogVO = eventLogService.findByEventId(eventId);
		if (eventLogVO == null) {
			eventLogVO = initializeVoucherEventLogVO(eventContent);
		} else {
			if (EventProcessResult.Y.getCode().equals(eventLogVO.getProcessResult())) {
				log.error("事件类型：{}，eventid：{},已经处理成功，无需处理！");
				return null;
			}
			if (EventProcessStatus.PROCESSING.getCode().equals(eventLogVO.getProcessStatus())) {
				log.error("事件类型：{}，eventid：{},已经在处理中，无需处理！");
				return null;
			}
			eventLogVO.set_status(ActionEnum.UPDATE.getValueInt());
			eventLogVO.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
			eventLogVO.setProcessResult(null);
			eventLogVO.setCostTime(null);
			eventLogVO.setProcessCount(eventLogVO.getProcessCount() + 1);
			// 过滤出未处理成功的事件明细
			List<VoucherEventLogDetail> details = eventLogVO.getVoucherEventLogDetailList();
			details = details.stream().filter(d -> !EventProcessResult.Y.getCode().equals(d.getProcessResult())
					&& !EventProcessStatus.PROCESSING.getCode().equals(d.getProcessStatus())).map(d -> {
						d.set_status(ActionEnum.UPDATE.getValueInt());
						d.setProcessResult(null);
						d.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
						d.setCostTime(null);
						d.setProcessCount(d.getProcessCount() + 1);
						return d;
					}).collect(Collectors.toList());

			eventLogVO.setVoucherEventLogDetailList(details);

		}
		return eventLogVO;
	}

	/**
	 * 初始化事件日志
	 * 
	 * @param eventContent
	 * @return
	 */
	private AIPOVoucherEventLog initializeVoucherEventLogVO(EventContent eventContent) {
		String content = eventContent.getContent();
		JSONArray contentJson = JSONArray.parseArray(content);
		if (contentJson == null || contentJson.isEmpty()) {
			log.info("凭证数据id为空，无需处理");
			return null;
		}
		AIPOVoucherEventLog voucherEventLogVO = new AIPOVoucherEventLog();
		voucherEventLogVO.set_status(ActionEnum.INSERT.getValueInt());
		voucherEventLogVO.setEventContent(JSONObject.toJSONString(eventContent));
		voucherEventLogVO.setEventid(eventContent.getEventId());
		voucherEventLogVO.setEventType(eventContent.getType().toString());
		voucherEventLogVO.setYtenantId(
				eventContent.getTenantId() == null ? InvocationInfoProxy.getTenantid() : eventContent.getTenantId());
		voucherEventLogVO.setEventValid("Y");
		voucherEventLogVO.setCreateTime(new Date());
		Long timestamp = eventContent.getTimestamp();
		if (timestamp != null) {
			voucherEventLogVO.setBusinessDate(DateTimeFormatterUtil.ymdSpeDate(timestamp));
		} else {
			voucherEventLogVO.setBusinessDate(DateTimeFormatterUtil.hmsDate(LocalDateTime.now()));
		}
		voucherEventLogVO.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
		voucherEventLogVO.setProcessCount(1);
		List<VoucherEventLogDetail> details = Lists.newArrayList();
		for (int i = 0; i < contentJson.size(); i++) {
			String voucherId = contentJson.getString(i);
			VoucherEventLogDetail voucherEventLogDetailVO = new VoucherEventLogDetail();
			voucherEventLogDetailVO.set_status(ActionEnum.INSERT.getValueInt());
			voucherEventLogDetailVO.setBillid(voucherId);
			voucherEventLogDetailVO.setBillcode(null);
			voucherEventLogDetailVO.setVoucherperiod(null);
			voucherEventLogDetailVO.setAccbookId(null);
			voucherEventLogDetailVO.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
			voucherEventLogDetailVO.setProcessCount(1);
			voucherEventLogDetailVO.setCreateTime(voucherEventLogVO.getCreateTime());
			voucherEventLogDetailVO.setYtenantId(voucherEventLogVO.getYtenantId());
			details.add(voucherEventLogDetailVO);
		}
		voucherEventLogVO.setVoucherEventLogDetailList(details);
		return voucherEventLogVO;
	}
}
