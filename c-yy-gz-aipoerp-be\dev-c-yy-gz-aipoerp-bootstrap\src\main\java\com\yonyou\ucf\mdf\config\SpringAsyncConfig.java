package com.yonyou.ucf.mdf.config;

import iuap.yms.thread.api.YmsExecutors;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;

import java.util.concurrent.Executor;

@Configuration
public class SpringAsyncConfig implements AsyncConfigurer {

    /**
     * 重写此方法会使所有@Async注解方法具有上下文复制的功能
     *
     * @return
     */
    @Override
    public Executor getAsyncExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setTaskDecorator(YmsContextWrappers::wrapRunnable);
//        // 定义其他属性
//        executor.initialize();
//        return executor;
        return YmsExecutors.getYmsExecutor();

    }
}