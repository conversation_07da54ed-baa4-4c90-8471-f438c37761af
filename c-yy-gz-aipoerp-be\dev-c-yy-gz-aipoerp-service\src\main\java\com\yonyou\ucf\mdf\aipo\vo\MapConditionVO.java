package com.yonyou.ucf.mdf.aipo.vo;

import lombok.Data;

import java.util.List;

@Data
public class MapConditionVO{
        //组织PK
        private String pk_org_value;
        //组织名称
        private String pk_org_text;

        private String pk_org_title;

        private String pk_org_op;

        private String audittime_value;

        private String audittime_title;

        private String audittime_op;

        private List<String> pk_ownerorg_value;

        private String pk_ownerorg_text;

        private String pk_ownerorg_title;

        private String pk_ownerorg_op;

        private List<String> pk_category_value;

        private String pk_category_text;

        private String pk_category_title;

        private String pk_category_op;

        private List<String> pk_used_status_value;

        private String pk_used_status_text;

        private String pk_used_status_title;

        private String pk_used_status_op;

        private String org_type_value;

        private String org_type_title;

        private String org_type_op;

        private String pk_plan_value;

        private String pk_plan_text;

        private String pk_plan_title;

        private String pk_plan_op;

        private List<String> itemNames;

        private String range;
}
