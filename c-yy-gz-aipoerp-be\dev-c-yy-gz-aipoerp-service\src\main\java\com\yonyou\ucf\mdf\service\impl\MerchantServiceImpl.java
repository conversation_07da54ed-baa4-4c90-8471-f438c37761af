package com.yonyou.ucf.mdf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.customer.CustomerBill;
import com.yonyou.aipierp.dto.ncapi.customer.CustomerBillPf;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IMerchantService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.List;

@Component
@Slf4j
public class MerchantServiceImpl implements IMerchantService {

    NCOpenApiConfig ncOpenApiConfig;
    NCOpenApiService ncOpenApiService;
    AIPORepository aipoRepository;
//    SimpleDateFormat MerchantDateFormat;


    public MerchantServiceImpl(NCOpenApiConfig ncOpenApiConfig,
                              NCOpenApiService ncOpenApiService,
                              AIPORepository aipoRepository) {
        this.ncOpenApiConfig = ncOpenApiConfig;
        this.ncOpenApiService = ncOpenApiService;
        this.aipoRepository = aipoRepository;
//        this.MerchantDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    }

    @Override
    public JSONObject pushMerchantToNC(JSONObject oriSaveReq, JSONObject saveRuleReturn) {
        String merchantId = saveRuleReturn.getString("id");
        // 客户档案暂不翻译子表
//         getFinalSavedMerchant(merchantId,oriSaveReq,saveRuleReturn);
        JSONObject saveBody = convertToNcSaveJsonBody(saveRuleReturn);
        JSONObject resp = ncOpenApiService.saveCustomer(saveBody);
        if (!resp.getBooleanValue("success")) { // 客户申请单保存只校验success字段就行
            log.error("同步客户档案信息至高级版系统失败 saveBody-->{},resp-->{}", saveBody, resp);
            throw new BusinessException("同步客户档案信息至高级版系统失败，错误信息：" + resp.toJSONString());
        }
        return resp;
    }

    @Override
    public JSONObject deleteMerchantFromNC(String merchantId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bipid",merchantId);
        jsonObject.put("version","1");
        JSONObject resp = ncOpenApiService.deleteCustomer(jsonObject);
        if (!resp.getBooleanValue("success")) {
            log.error("从高级版删除客户申请信息失败 req-->{},resp-->{}", jsonObject, resp);
            throw new BusinessException("从高级版删除客户申请信息失败，错误信息：" + resp.toJSONString());
        }
        return resp;
    }


    /**
     * 获取最终保存的客户档案结果<br/>
     *  执行扩展规则时，保存事务还未结束、提交，此时查库无法查到更新后的数据，所以如果涉及子表更新，则必须拼接下 【数据库查询结果+请求requestbody+saveRuleReturn】 <br/>
     *  todo 以适用范围为例
     * @param  merchantId 客户档案id
     * @param oriSaveReq  初始保存请求
     * @param saveRuleReturn rule返回的return中的信息
     * @return {@link JSONObject }
     */
    private JSONObject getFinalSavedMerchant(String merchantId, JSONObject oriSaveReq, JSONObject saveRuleReturn) {
        if (oriSaveReq.getString("_status") != null && !"insert".equals(oriSaveReq.getString("_status").toLowerCase())) {  // 非新增时
            // 从数据库里查下客户档案信息
            JSONObject dataBaseMerchant = aipoRepository.queryMerchantById(merchantId);
            List<JSONObject> dataBaseMerchantApplyRangeList = JSON.parseArray(dataBaseMerchant.getString("merchantApplyRanges"), JSONObject.class);

            if (oriSaveReq.get("merchantApplyRanges") != null) {
                List<JSONObject> oriReqMerchantApplyRangeList = JSON.parseArray(oriSaveReq.getString("merchantApplyRanges"), JSONObject.class);
                if (CollectionUtils.isNotEmpty(oriReqMerchantApplyRangeList)) {
                    // 此时说明更新了子表信息，需要把所有项目范围子表都补充上
                    List<JSONObject> saveRuleReturnMerchantApplyRangeList = JSON.parseArray(saveRuleReturn.getString("merchantApplyRanges"), JSONObject.class);
                    // 1,dataBaseMerchantApplyRangeList 删去request中删除的MerchantApplyRange
                    oriReqMerchantApplyRangeList.stream().forEach(reqMerchantApplyRange -> {
                        if (reqMerchantApplyRange.getString("_status").toLowerCase().equals("delete")) {
                            String deleteId = reqMerchantApplyRange.getString("id");
                            dataBaseMerchantApplyRangeList.removeIf(MerchantApplyRange -> MerchantApplyRange.getString("id").equals(deleteId));
                            saveRuleReturnMerchantApplyRangeList.removeIf(MerchantApplyRange -> MerchantApplyRange.getString("id").equals(deleteId));
                        }
                    });
                    // 2,saveReturn中的所有子表，相同id覆盖到 dataBaseMerchantApplyRangeList 中，不能覆盖就新增
                    saveRuleReturnMerchantApplyRangeList.stream().forEach(saveReturn -> {
                        boolean saveReturnOverWrite = false;
                        for (int i = 0; i < dataBaseMerchantApplyRangeList.size(); i++) {
                            JSONObject dataBaseMerchantApplyRange = dataBaseMerchantApplyRangeList.get(i);
                            if (dataBaseMerchantApplyRange.getString("id").equals(saveReturn.getString("id"))) {
                                dataBaseMerchantApplyRangeList.set(i, saveReturn);
                                saveReturnOverWrite = true;
                                break;
                            }
                        }
                        if (!saveReturnOverWrite) {
                            dataBaseMerchantApplyRangeList.add(saveReturn);
                        }
                    });

                }
            }
            // 3，把database子表全部替换到saveReturn中
            saveRuleReturn.put("MerchantApplyRangeList", dataBaseMerchantApplyRangeList);
        }
        return saveRuleReturn;
    }

    private JSONObject convertToNcSaveJsonBody(JSONObject bipMerchantInfo){
        // 先查一下客户分类信息
        JSONObject customerClass = aipoRepository.queryMerchantClassById(bipMerchantInfo.getString("customerClass"));

        JSONObject res = new JSONObject();
        // 客户主表信息
        CustomerBill customerBill = new CustomerBill();
        // 客户申请单信息
        CustomerBillPf customerBillPf = new CustomerBillPf();

        // 先处理客户申请单
        customerBillPf.setPk_group(ncOpenApiConfig.getGroupcode());
        customerBillPf.setPk_org(bipMerchantInfo.getString("createOrg"));
        if ("666666".equals(bipMerchantInfo.getString("createOrg"))){
            customerBillPf.setDestorg("1"); // 企业账号级就是选【集团】
        }else {
            customerBillPf.setDestorg("0"); // 否则选【本组织】
        }

        customerBillPf.setCustomercode(bipMerchantInfo.getString("code"));
        JSONObject mlName = bipMerchantInfo.getJSONObject("name");
        if (mlName!=null){
            customerBillPf.setCustomername(mlName.getString("zh_CN"));
        }
//        customerBillPf.setCustomername(bipMerchantInfo.getString("name"));    使用多语名称
        if (customerClass!=null){
            customerBillPf.setPk_custclass(customerClass.getString("code"));
        }
        customerBillPf.setUpdate_cust(bipMerchantInfo.getString("id"));

        // 再处理客户档案主表
        if (bipMerchantInfo.getBooleanValue("internalOrg")){
            customerBill.setCustprop("1");
        }else {
            customerBill.setCustprop("0");
        }
        customerBill.setDef10(bipMerchantInfo.getString("id"));

        res.put("billpf",customerBillPf);
        res.put("bill",customerBill);
        return res;
    }
}
