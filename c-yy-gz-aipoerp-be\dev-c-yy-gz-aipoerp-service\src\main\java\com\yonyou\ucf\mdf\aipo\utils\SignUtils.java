package com.yonyou.ucf.mdf.aipo.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.TreeMap;

/**
 *
 * <AUTHOR>
 * @since 2024-09-06 15:39:39
 */
public class SignUtils {
    /**
     * 加密
     * @param appKey appKey
     * @param appSecret appSecret
     * @param timestamp 时间戳
     * @return
     */
    public static String sign(String appKey, String appSecret, String timestamp) {
        TreeMap<String, String> treeMap = new TreeMap<>();
        treeMap.put("appKey", appKey);
        treeMap.put("timestamp", timestamp);
        StringBuilder signKey = new StringBuilder();
        treeMap.forEach((key, value) -> signKey.append(key).append(value));
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(signKey.toString().getBytes(StandardCharsets.UTF_8));
            String base64String = Base64.getEncoder().encodeToString(signData);
            return URLEncoder.encode(base64String, "UTF-8");
        } catch(Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        String timestamp = "1732243089975";
        String sign = sign("8de7fdcaa81b42b9974adfac90adb376", "be898e757ab2f69d70cf424d0a3538739b2e72ff", timestamp);
        System.out.println(sign);
    }
}
