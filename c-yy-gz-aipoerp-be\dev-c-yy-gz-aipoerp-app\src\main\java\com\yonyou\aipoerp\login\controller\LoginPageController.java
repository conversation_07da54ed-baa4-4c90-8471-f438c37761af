package com.yonyou.aipoerp.login.controller;

import java.nio.file.Files;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 *
 * 2025年3月31日
 */
@RestController
@RequestMapping("/aipo")
public class LoginPageController {

    @SneakyThrows
    @GetMapping(value = "/zone", produces = "text/html")
    public String loginPage() {
        Resource resource = new ClassPathResource("static/login.html");
        String html = new String(Files.readAllBytes(resource.getFile().toPath()));
        return html;
    }

}
