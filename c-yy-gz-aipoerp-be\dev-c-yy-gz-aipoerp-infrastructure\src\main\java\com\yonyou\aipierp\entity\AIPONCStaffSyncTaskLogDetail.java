package com.yonyou.aipierp.entity;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.base.*;
import java.util.Date;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 艾珀高级版员工数据同步任务日志
 * @Date 2024-12-16 16:56:06
 * @since 2023/11/28
 **/
@YMSEntity(name = "AIPOERPCREATE.AIPOERPCREATE.AIPONCStaffSyncTaskLogDetail", domain = "c-yy-gz-aipoerp")
public class AIPONCStaffSyncTaskLogDetail extends SuperDO implements ILogicDelete {
    public static final String ENTITY_NAME = "AIPOERPCREATE.AIPOERPCREATE.AIPONCStaffSyncTaskLogDetail";
    public static final String DATATYPE = "dataType";
    public static final String SYNCRESULT = "syncResult";
    public static final String BIPORISTAFFINFO = "bipOriStaffInfo";
    public static final String DATASYNCREQ = "dataSyncReq";
    public static final String DATASYNCRESP = "dataSyncResp";
    public static final String STAFFNAME = "staffName";
    public static final String STAFFCODE = "staffCode";
    public static final String STAFFORG = "staffOrg";
    public static final String STAFFDEPT = "staffDept";
    public static final String STAFFPHONE = "staffPhone";
    public static final String FOREIGNERKEY = "foreignerKey";
    public static final String CREATETIME = "createTime";
    public static final String CREATOR = "creator";
    public static final String DR = "dr";
    public static final String ID = "id";
    public static final String MODIFIER = "modifier";
    public static final String MODIFYTIME = "modifyTime";
    public static final String PUBTS = "pubts";
    public static final String YTENANTID = "ytenantId";

    /* 数据类型 */
    private String dataType;
    /* 同步结果 */
    private String syncResult;
    /* bip原始员工信息 */
    private String bipOriStaffInfo;
    /* nc同步请求体 */
    private String dataSyncReq;
    /* nc同步响应体 */
    private String dataSyncResp;
    /* 员工名称 */
    private String staffName;
    /* 员工编码 */
    private String staffCode;
    /* 员工组织 */
    private String staffOrg;
    /* 员工部门 */
    private String staffDept;
    /* 手机号 */
    private String staffPhone;
    /* 艾珀高级版员工数据同步任务 */
    private String foreignerKey;
    /* 创建时间 */
    private Date createTime;
    /* 创建人 */
    private String creator;
    /* 逻辑删除 */
    private Short dr;
    /* id */
    private String id;
    /* 修改人 */
    private String modifier;
    /* 修改时间 */
    private Date modifyTime;
    /* pubts */
    private Date pubts;
    /* 租户id */
    private String ytenantId;

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public void setSyncResult(String syncResult) {
        this.syncResult = syncResult;
    }

    public void setBipOriStaffInfo(String bipOriStaffInfo) {
        this.bipOriStaffInfo = bipOriStaffInfo;
    }

    public void setDataSyncReq(String dataSyncReq) {
        this.dataSyncReq = dataSyncReq;
    }

    public void setDataSyncResp(String dataSyncResp) {
        this.dataSyncResp = dataSyncResp;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public void setStaffCode(String staffCode) {
        this.staffCode = staffCode;
    }

    public void setStaffOrg(String staffOrg) {
        this.staffOrg = staffOrg;
    }

    public void setStaffDept(String staffDept) {
        this.staffDept = staffDept;
    }

    public void setStaffPhone(String staffPhone) {
        this.staffPhone = staffPhone;
    }

    public void setForeignerKey(String foreignerKey) {
        this.foreignerKey = foreignerKey;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setDr(Short dr) {
        this.dr = dr;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public void setPubts(Date pubts) {
        this.pubts = pubts;
    }

    public void setYtenantId(String ytenantId) {
        this.ytenantId = ytenantId;
    }

    public String getDataType() {
        return dataType;
    }

    public String getSyncResult() {
        return syncResult;
    }

    public String getBipOriStaffInfo() {
        return bipOriStaffInfo;
    }

    public String getDataSyncReq() {
        return dataSyncReq;
    }

    public String getDataSyncResp() {
        return dataSyncResp;
    }

    public String getStaffName() {
        return staffName;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public String getStaffOrg() {
        return staffOrg;
    }

    public String getStaffDept() {
        return staffDept;
    }

    public String getStaffPhone() {
        return staffPhone;
    }

    public String getForeignerKey() {
        return foreignerKey;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getCreator() {
        return creator;
    }

    public Short getDr() {
        return dr;
    }

    public String getId() {
        return id;
    }

    public String getModifier() {
        return modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public Date getPubts() {
        return pubts;
    }

    public String getYtenantId() {
        return ytenantId;
    }

    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
