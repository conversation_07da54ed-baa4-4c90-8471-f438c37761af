package com.yonyou.ucf.mdf.aipo.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.aipo.service.IAipoVoucherSyncLogService;
import com.yonyou.ucf.mdf.aipo.vo.AIPOVoucherSyncLog;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AipoVoucherSyncLogServiceImpl implements IAipoVoucherSyncLogService {

	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private IBillCommonRepository billCommonRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public List<AIPOVoucherSyncLog> queryFailedSyncLogs() {
		// 构建查询SQL
		String sql = "SELECT * FROM c_yy_gz_aipoerp_db.event_sync_data_log " + "WHERE issuccess = 'N' " + "AND dr = 0 "
				+ // 未删除的记录
				"AND ytenant_id = '" + tenantId + "' " + "ORDER BY create_time DESC";

		log.debug("查询失败凭证同步日志SQL: {}", sql);

		try {
			List<Map<String, Object>> resultList = billRepository.queryForObject(sql, null, new MapListProcessor());
			return convertToVoucherSyncLogs(resultList);
		} catch (Exception e) {
			log.error("查询失败凭证同步日志异常", e);
			throw new RuntimeException("查询失败凭证同步日志异常: " + e.getMessage());
		}
	}

	/**
	 * 将Map列表转换为AIPOVoucherSyncLog对象列表
	 */
	private List<AIPOVoucherSyncLog> convertToVoucherSyncLogs(List<Map<String, Object>> mapList) {
		List<AIPOVoucherSyncLog> result = new ArrayList<>();
		for (Map<String, Object> map : mapList) {
			AIPOVoucherSyncLog log = new AIPOVoucherSyncLog();
			log.setEventid((String) map.get("eventid"));
			log.setBillid((String) map.get("billid"));
			log.setSyncdata((String) map.get("syncdata"));
			log.setAcccode((String) map.get("acccode"));
			log.setAccname((String) map.get("accname"));
			log.setVoucherperiod((String) map.get("voucherperiod"));
			log.setBillcode((String) map.get("billcode"));
			log.setVouchertypecode((String) map.get("vouchertypecode"));
			log.setSynctype((String) map.get("synctype"));
			log.setIssuccess((String) map.get("issuccess"));
			log.setErrormsg((String) map.get("errormsg"));
			log.setSendmsg((String) map.get("sendmsg"));
			log.setRetmsg((String) map.get("retmsg"));
			log.setEntertime((String) map.get("entertime"));
			log.setDef1((String) map.get("def1"));
			result.add(log);
		}
		return result;
	}

	@Override
	public AIPOVoucherSyncLog save(AIPOVoucherSyncLog syncLog) {
		try {
			List<IBillDO> billDOs = Lists.newArrayList(syncLog);
			billDOs = billCommonRepository.commonSaveBill(billDOs, "AIPOVoucherSyncLog");
			return (AIPOVoucherSyncLog) billDOs.get(0);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}