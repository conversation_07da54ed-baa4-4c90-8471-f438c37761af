package com.yonyou.aipierp.dto.ncapi.dept;

import com.alibaba.fastjson.JSONObject;

public class DeptBillHead extends JSONObject {

    /**
     * "dept1552"（部门编码）
     */
    public String CODE = "code";
    /**
     * "dept1552"（部门名称）
     */
    public String NAME = "name";
    /**
     * "dept1552"（简称）
     */
    public String SHORTNAME = "shortname";
    /**
     * "dept1552"（助记码）
     */
    public String MNECODE = "mnecode";
    /**
     * "0001"（集团编码）
     */
    public String PK_GROUP = "pk_group";
    /**
     * "testid1209"(上级部门id（旗舰版），为空就是没上级)
     */
    public String PK_FATHERORG = "pk_fatherorg";
    /**
     * "0000102"（旗舰版业务单元id）
     */
    public String PK_ORG = "pk_org";
    /**
     * "0"（0=普通部门,1=虚拟部门）
     */
    public String DEPTTYPE = "depttype";
    /**
     * "2"（1=未启用,2=已启用,3=已停用）
     */
    public String ENABLESTATE = "enablestate";
    /**
     * "2011-04-28 11:40:51"（创建时间）
     */
    public String CREATEDATE = "createdate";
    /**
     * "N"（HR撤销标志）
     */
    public String HRCANCELED = "hrcanceled";
    /**
     * "1"（显示顺序）
     */
    public String DISPLAYORDER = "displayorder";
    /**
     * ""（地址）
     */
    public String ADDRESS = "address";
    /**
     * "Y"（报表）
     */
    public String ORGTYPE13 = "orgtype13";
    /**
     * "N"（预算）
     */
    public String ORGTYPE17 = "orgtype17";
    /**
     * "62437400"（联系电话）
     */
    public String TEL = "tel";
    /**
     * "备注"
     */
    public String MEMO = "memo";


    public String getCode() {
        return this.getString(CODE);
    }

    public void setCode(String code) {
        this.put(CODE, code);
    }

    public String getName() {
        return this.getString(NAME);
    }

    public void setName(String name) {
        this.put(NAME, name);
    }

    public String getShortname() {
        return this.getString(SHORTNAME);
    }

    public void setShortname(String shortname) {
        this.put(SHORTNAME, shortname);
    }

    public String getMnecode() {
        return this.getString(MNECODE);
    }

    public void setMnecode(String mnecode) {
        this.put(MNECODE, mnecode);
    }

    public String getPk_group() {
        return this.getString(PK_GROUP);
    }

    public void setPk_group(String pk_group) {
        this.put(PK_GROUP, pk_group);
    }

    public String getPk_fatherorg() {
        return this.getString(PK_FATHERORG);
    }

    public void setPk_fatherorg(String pk_fatherorg) {
        this.put(PK_FATHERORG, pk_fatherorg);
    }

    public String getPk_org() {
        return this.getString(PK_ORG);
    }

    public void setPk_org(String pk_org) {
        this.put(PK_ORG, pk_org);
    }

    public String getDepttype() {
        return this.getString(DEPTTYPE);
    }

    public void setDepttype(String depttype) {
        this.put(DEPTTYPE, depttype);
    }

    public String getEnablestate() {
        return this.getString(ENABLESTATE);
    }

    public void setEnablestate(String enablestate) {
        this.put(ENABLESTATE, enablestate);
    }

    public String getCreatedate() {
        return this.getString(CREATEDATE);
    }

    public void setCreatedate(String createdate) {
        this.put(CREATEDATE, createdate);
    }

    public String getHrcanceled() {
        return this.getString(HRCANCELED);
    }

    public void setHrcanceled(String hrcanceled) {
        this.put(HRCANCELED, hrcanceled);
    }

    public String getDisplayorder() {
        return this.getString(DISPLAYORDER);
    }

    public void setDisplayorder(String displayorder) {
        this.put(DISPLAYORDER, displayorder);
    }

    public String getAddress() {
        return this.getString(ADDRESS);
    }

    public void setAddress(String address) {
        this.put(ADDRESS, address);
    }

    public String getOrgtype13() {
        return this.getString(ORGTYPE13);
    }

    public void setOrgtype13(String orgtype13) {
        this.put(ORGTYPE13, orgtype13);
    }

    public String getOrgtype17() {
        return this.getString(ORGTYPE17);
    }

    public void setOrgtype17(String orgtype17) {
        this.put(ORGTYPE17, orgtype17);
    }

    public String getTel() {
        return this.getString(TEL);
    }

    public void setTel(String tel) {
        this.put(TEL, tel);
    }

    public String getMemo() {
        return this.getString(MEMO);
    }

    public void setMemo(String memo) {
        this.put(MEMO, memo);
    }
}
