<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename local variable &apos;expenseItems&apos; in &apos;com.yonyou.ucf.mdf.iris.plugin.PayIndexRequestPluginImpl.setFundUsage(...)&apos; to &apos;settleItems&apos;&#x0D;&#x0A;- Original project: &apos;dev-c-yy-gz-aipoerp-service&apos;&#x0D;&#x0A;- Original element: &apos;com.yonyou.ucf.mdf.iris.plugin.PayIndexRequestPluginImpl.setFundUsage(Long, JSONObject).expenseItems&apos;&#x0D;&#x0A;- Renamed element: &apos;expenseItems&apos;&#x0D;&#x0A;- Update references to refactored element" description="Rename local variable &apos;expenseItems&apos;" id="org.eclipse.jdt.ui.rename.local.variable" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.yonyou.ucf.mdf.iris.plugin{PayIndexRequestPluginImpl.java[PayIndexRequestPluginImpl~setFundUsage~QLong;~QJSONObject;@expenseItems!6160!6314!6186!6197!QList\&lt;QMap\&lt;QString;QObject;&gt;;&gt;;!0!false" name="settleItems" references="true" stamp="1752480412582" version="1.0"/>&#x0A;<refactoring comment="Move element &apos;DeptSyncLog.java&apos; to &apos;dev-c-yy-gz-aipoerp-service/src/main/java/com.yonyou.ucf.mdf.dept.entity&apos;&#x0D;&#x0A;- Original project: &apos;dev-c-yy-gz-aipoerp-service&apos;&#x0D;&#x0A;- Destination element: &apos;dev-c-yy-gz-aipoerp-service/src/main/java/com.yonyou.ucf.mdf.dept.entity&apos;&#x0D;&#x0A;- Original element: &apos;com.yonyou.ucf.mdf.bill.entity.DeptSyncLog.java&apos;&#x0D;&#x0A;- Update references to refactored element" description="Move compilation unit" destination="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.yonyou.ucf.mdf.dept.entity" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.yonyou.ucf.mdf.bill.entity{DeptSyncLog.java" files="0" flags="589830" folders="0" id="org.eclipse.jdt.ui.move" patterns="*" policy="org.eclipse.jdt.ui.moveResources" qualified="false" references="true" stamp="1752733761859" units="1" version="1.0"/>&#x0A;<refactoring accessors="true" comment="Delete element from project &apos;dev-c-yy-gz-aipoerp-service&apos;&#x0D;&#x0A;- Original project: &apos;dev-c-yy-gz-aipoerp-service&apos;&#x0D;&#x0A;- Original element: &apos;dev-c-yy-gz-aipoerp-service/src/main/java/com.yonyou.ucf.mdf.bill.entity&apos;" description="Delete element" element1="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.yonyou.ucf.mdf.bill.entity" element2="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.yonyou.ucf.mdf.bill" elements="2" flags="589830" id="org.eclipse.jdt.ui.delete" resources="0" stamp="1752733766697" subPackages="false" version="1.0"/>
</session>