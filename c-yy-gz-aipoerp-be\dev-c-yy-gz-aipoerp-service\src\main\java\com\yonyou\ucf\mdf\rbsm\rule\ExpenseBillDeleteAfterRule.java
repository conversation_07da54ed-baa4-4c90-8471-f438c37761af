package com.yonyou.ucf.mdf.rbsm.rule;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.SimpleCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ucf.mdf.rbsm.model.BustypeSimple;
import com.yonyou.ucf.mdf.rbsm.model.FieldRef;
import com.yonyou.ucf.mdf.rbsm.service.itf.IFieldRefService;
import com.yonyou.workbench.util.Lists;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.SneakyThrows;

/**
 * <AUTHOR>
 *
 * 2025年3月6日
 */
@Component("expenseBillDeleteAfterRule")
public class ExpenseBillDeleteAfterRule implements IYpdCommonRul {

    @Autowired
    private IBillRepository billRepository;

    @Autowired
    private IFieldRefService fieldRefService;

    @SneakyThrows
    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {

        String action = rulCtxVO.getAction();
        if (!"delete".equals(action)) {
            return null;
        }

        List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
        if (CollectionUtils.isEmpty(bills)) {
            return null;
        }
        List<String> ids = Lists.newArrayList();
        for (BizObject bizObject : bills) {
            Object id = bizObject.getId();
            ids.add(id.toString());
        }

        String bustype = bills.get(0).getString("bustype");

        Map<String, String> bustypeCodeMap = Maps.newHashMap();
        String sql = "select id,code,name from iuap_apdoc_basedoc.bd_transtype where id = ?";
        SQLParameter parameter = new SQLParameter();
        parameter.addParam(bustype);
        List<BustypeSimple> bustypeList = billRepository.queryForDTOList(sql, parameter, BustypeSimple.class);
        if (CollectionUtils.isNotEmpty(bustypeList)) {
            bustypeCodeMap = bustypeList.stream()
                    .collect(Collectors.toMap(BustypeSimple::getId, BustypeSimple::getCode));
        }

        List<FieldRef> fieldRefList = fieldRefService.getFieldRefByKkFields(
                Arrays.asList("securityBustype", "laborBustype", "incomeTaxBustype", "fundBustype"));

        Map<String, String> fieldMap = fieldRefList.stream()
                .collect(Collectors.toMap(FieldRef::getKkField, FieldRef::getRefField));

        if (StringUtils.equals(fieldMap.get("securityBustype"), bustypeCodeMap.get(bustype))) {
            SimpleCondition condition1 = new SimpleCondition("expenseId", ConditionOperator.in, ids);
            SimpleCondition condition2 = new SimpleCondition("socialType", ConditionOperator.eq, "社保");
            billRepository.batchRemove("SMR001.SMR001.SocialSecurityRecord", Arrays.asList(condition1, condition2));

        } else if (StringUtils.equals(fieldMap.get("fundBustype"), bustypeCodeMap.get(bustype))) {
            SimpleCondition condition1 = new SimpleCondition("expenseId", ConditionOperator.in, ids);
            SimpleCondition condition2 = new SimpleCondition("socialType", ConditionOperator.eq, "公积金");

            billRepository.batchRemove("SMR001.SMR001.SocialSecurityRecord", Arrays.asList(condition1, condition2));
        } else if (StringUtils.equals(fieldMap.get("laborBustype"), bustypeCodeMap.get(bustype))) {
            SimpleCondition condition = new SimpleCondition("expenseId", ConditionOperator.in, ids);
            SimpleCondition condition2 = new SimpleCondition("sumType", ConditionOperator.eq, "工会会费");

            billRepository.batchRemove("SMR002.SMR002.WaPayRecord", Arrays.asList(condition, condition2));

        } else if (StringUtils.equals(fieldMap.get("incomeTaxBustype"), bustypeCodeMap.get(bustype))) {
            SimpleCondition condition = new SimpleCondition("expenseId", ConditionOperator.in, ids);
            SimpleCondition condition2 = new SimpleCondition("sumType", ConditionOperator.eq, "个税税费");

            billRepository.batchRemove("SMR002.SMR002.WaPayRecord", Arrays.asList(condition, condition2));
        }

        return null;
    }

}
