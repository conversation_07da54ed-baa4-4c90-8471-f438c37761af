package com.yonyou.ucf.mdf.equip.model;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.base.*;
import java.util.Date;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 数据对比明细
 * @Date 2025-02-19 16:09:53
 * @since 2023/11/28
 **/
@YMSEntity(name = "ZCEDI.ZCEDI.EquipCardDataContrastDetailVo", domain = "c-yy-gz-aipoerp")
public class EquipCardDataContrastDetailVo extends SuperDO {
    public static final String ENTITY_NAME = "ZCEDI.ZCEDI.EquipCardDataContrastDetailVo";
    public static final String CHANGE_AFTER_VALUE_NAME = "change_after_value_name";
    public static final String CHANGE_BEFORE_VALUE = "change_before_value";
    public static final String CHANGE_FIELD_NAME = "change_field_name";
    public static final String EQUIP_CODE = "equip_code";
    public static final String CHANGE_BEFORE_VALUE_NAME = "change_before_value_name";
    public static final String CHANGE_AFTER_VALUE = "change_after_value";
    public static final String CHANGE_FIELD = "change_field";
    public static final String FOREIGNERKEY = "foreignerKey";
    public static final String CREATETIME = "createTime";
    public static final String CREATOR = "creator";
    public static final String ID = "id";
    public static final String MODIFIER = "modifier";
    public static final String MODIFYTIME = "modifyTime";
    public static final String PUBTS = "pubts";
    public static final String YTENANTID = "ytenantId";

    /* 变动后值 */
    private String change_after_value_name;
    /* 变动前值id */
    private String change_before_value;
    /* 变动字段 */
    private String change_field_name;
    /* 资产编码 */
    private String equip_code;
    /* 变动前值 */
    private String change_before_value_name;
    /* 变动后值id */
    private String change_after_value;
    /* 变动信息 */
    private String change_field;
    /* 资产卡片数据 */
    private String foreignerKey;
    /* 创建时间 */
    private Date createTime;
    /* 创建人 */
    private String creator;
    /* id */
    private String id;
    /* 修改人 */
    private String modifier;
    /* 修改时间 */
    private Date modifyTime;
    /* pubts */
    private Date pubts;
    /* 租户id */
    private String ytenantId;

    public void setChange_after_value_name(String change_after_value_name) {
        this.change_after_value_name = change_after_value_name;
    }

    public void setChange_before_value(String change_before_value) {
        this.change_before_value = change_before_value;
    }

    public void setChange_field_name(String change_field_name) {
        this.change_field_name = change_field_name;
    }

    public void setEquip_code(String equip_code) {
        this.equip_code = equip_code;
    }

    public void setChange_before_value_name(String change_before_value_name) {
        this.change_before_value_name = change_before_value_name;
    }

    public void setChange_after_value(String change_after_value) {
        this.change_after_value = change_after_value;
    }

    public void setChange_field(String change_field) {
        this.change_field = change_field;
    }

    public void setForeignerKey(String foreignerKey) {
        this.foreignerKey = foreignerKey;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public void setPubts(Date pubts) {
        this.pubts = pubts;
    }

    public void setYtenantId(String ytenantId) {
        this.ytenantId = ytenantId;
    }

    public String getChange_after_value_name() {
        return change_after_value_name;
    }

    public String getChange_before_value() {
        return change_before_value;
    }

    public String getChange_field_name() {
        return change_field_name;
    }

    public String getEquip_code() {
        return equip_code;
    }

    public String getChange_before_value_name() {
        return change_before_value_name;
    }

    public String getChange_after_value() {
        return change_after_value;
    }

    public String getChange_field() {
        return change_field;
    }

    public String getForeignerKey() {
        return foreignerKey;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getCreator() {
        return creator;
    }

    public String getId() {
        return id;
    }

    public String getModifier() {
        return modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public Date getPubts() {
        return pubts;
    }

    public String getYtenantId() {
        return ytenantId;
    }

    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
