#以下配置需要根据环境修改
# yms配置地址。优先级为：本地目录>YMS控制台下载。
# YMS配置本地目录(yms.config.path)和YMS控制台地址(yms.domain.url)至少填写一个。
#yms.config.path=C:\\Users\\<USER>\\Downloads\\2\\setup\\yms.json

#在线yms配置（默认）
yms.console.url=https://tech-devr6.yybip.cn/confcenter/
#yms环境编码，默认为dev开发环境
yms.console.env=dev
yms.console.accessKey=fuaJUwsE69mccrCl
yms.console.accessSecret=evQeAcSH8jm2UysqVNbsNvDEG4a0C8

#产品线分类编码,不能为空
productLine=c
#产品线分类名称,不能为空，默认为客开产品
productLineName=客开产品
#领域编码,不能为空，默认为客开
domain=udm-domain
#领域名称,不能为空，默认为客开领域
domainName=客开领域
#子领域编码,不能为空
subDomain=udm
#子领域名称,不能为空，默认为客开产品
subDomainName=客开产品
#产品盘名称,不能为空，默认为引擎名称
productName=艾珀ERP项目
#产品盘编码,不能为空
productCode=c-yy-gz-aipoerp
#访问地址,不能为空
address=/
#主版本,不能为空，默认为当前主版本
majorVersion=V3
#里程碑版本,不能为空，默认为当前里程碑版本
version=R6_2407
innerCode=0403062407
#产品盘描述信息
description=描述信息