package com.yonyou.ucf.mdf.rbsm.constants;

/**
 * 人力支付类型
 * 
 * <AUTHOR>
 *
 *         2025年4月10日
 */
public enum PayTypeEnum {

	SOCIAL_SECURITY("1", "社保"),
	FUND("2", "公积金"),
	LABOR("3", "工会费用"),
	INCOME_TAX("4", "个税费用");

	PayTypeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	private String code;
	private String name;

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
