package com.yonyou.ucf.mdf.utils.openApi;

import java.security.KeyPair;

/**
 * KeyPairs
 * 	Ö÷¼ü¶Ô
 *
 * <AUTHOR>
 * @date: 2019-5-20ÏÂÎç4:34:51
 *
 */
public class KeyPairs {

	private KeyPair keyPair;

	public KeyPairs(KeyPair keyPair){
		this.keyPair = keyPair;
	}

	public String getPublicKey(){
		return Base64Util.encryptBASE64(keyPair.getPublic().getEncoded());
	}

	public String getPrivateKey(){
		return Base64Util.encryptBASE64(keyPair.getPrivate().getEncoded());
	}
}
