package com.yonyou.ucf.mdf.rbsm.service.itf;

import java.util.List;
import java.util.Map;

import com.yonyou.ucf.mdf.rbsm.model.VendorInfo;

public interface IVendorQryervice {
	/**
	 * 通过QuerySchema的方式查询银行档案信息，参照没有翻译
	 *
	 * @param code 供应商编码
	 * @return
	 */
	List<Map<String, Object>> queryVendorByCode(String code);

	/**
	 * 通过id调用openapi接口获取供应商信息
	 *
	 * @param id
	 * @return
	 */
	VendorInfo queryVendorById(String id);

	/**
	 * 先根据编码查询出id，再通过id调用api查询出供应商信息
	 * 
	 * @param code
	 * @return
	 */
	VendorInfo queryVendorByCode2(String code);

	/**
	 * 根据id批量查询
	 * 
	 * @param vendorIds
	 * @return
	 */
	Map<String, VendorInfo> queryByIds(List<String> vendorIds);
}
