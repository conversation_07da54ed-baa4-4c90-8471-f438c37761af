package com.yonyou.ucf.mdf.travelexpense.rule;

import java.util.List;
import java.util.Map;

import org.imeta.orm.base.BizObject;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.bill.dto.BillDataDto;
import com.yonyou.ucf.mdd.ext.bill.rule.base.AbstractCommonRule;
import com.yonyou.ucf.mdd.ext.bill.rule.base.CommonRuleUtils;
import com.yonyou.ucf.mdd.ext.model.BillContext;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 人员参照选择弹窗查询前规则
 * 
 * <AUTHOR>
 *
 *         2025年4月1日
 */
@Slf4j
@Component("psnRefQueryBeforeRule")
public class PsnRefQueryBeforeRule extends AbstractCommonRule {

	@Override
	public RuleExecuteResult execute(BillContext billContext, Map<String, Object> paramMap) throws Exception {
		log.error("---------进入参照查询前规则--------start");
		// 判空
		if (null == billContext) {
			return new RuleExecuteResult();
		}
		// 取到规则链参数
		BillDataDto billDataDto = (BillDataDto) getParam(paramMap);
		// 得到参照的类型
		String refType = billDataDto.getRefEntity().refType;

		List<BizObject> bills = CommonRuleUtils.getBills(billContext, paramMap);

		log.error("---------billDataDto:{}", JSONUtil.toJson(billDataDto));
		log.error("---------bills:{}", JSONUtil.toJson(bills));

		log.error("---------进入参照查询前规则--------end");
		return null;
	}

}
