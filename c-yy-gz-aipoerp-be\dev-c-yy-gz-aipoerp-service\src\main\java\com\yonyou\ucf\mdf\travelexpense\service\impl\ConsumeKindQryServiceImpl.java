package com.yonyou.ucf.mdf.travelexpense.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.travelexpense.service.IConsumeKindQryService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 *         2025年3月26日
 */
@Service
public class ConsumeKindQryServiceImpl implements IConsumeKindQryService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public List<String> queryIdByCodes(List<String> consumeKindCodes) {

		List<? extends IBillDO> ids = billQryRepository.findIdsByCodes("znbzbx.consumekind.ConsumeKindVO",
				consumeKindCodes);
		if (CollectionUtils.isEmpty(ids)) {
			return Collections.emptyList();
		}
		return ids.stream().map(id -> id.getPrimaryKey().toString()).collect(Collectors.toList());
	}

	@Override
	public Map<String, String> queryIdCodeMap() {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("id,code");
		schema.addCondition(QueryConditionGroup.and(QueryCondition.name("stopstatus").eq("0"),
				QueryCondition.name("ytenant").eq(tenantId)));
		List<? extends IBillDO> result = billQryRepository.queryBySchema("znbzbx.consumekind.ConsumeKindVO", schema,
				"znbzbx");
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyMap();
		}
		return result.stream().collect(Collectors.toMap(IBillDO::getPrimaryKey, v -> {
			return v.getAttrValue("code").toString();
		}));
	}

}
