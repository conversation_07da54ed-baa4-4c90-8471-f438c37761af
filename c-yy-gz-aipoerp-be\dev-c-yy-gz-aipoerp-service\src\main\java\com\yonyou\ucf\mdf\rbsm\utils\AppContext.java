package com.yonyou.ucf.mdf.rbsm.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.hrcloud.employeedoc.addressbook.BaseResponse;
import com.yonyou.iuap.staff.model.StaffInfoQueryParam;
import com.yonyou.iuap.staff.service.StaffInfoQueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.ucf.mdf.rbsm.model.CurUserInfo;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class AppContext {

    @Autowired
    private StaffInfoQueryService staffInfoQueryService;

    @Autowired
    private IBillQueryRepository billQryRepository;

    /**
     * 获取当前登录用户信息
     *
     * @return
     */
    public CurUserInfo getCurUserInfo() {
        CurUserInfo userInfo = new CurUserInfo();
        String busDate = InvocationInfoProxy.getBusinessDate();
        if (StringUtils.isBlank(busDate)) {
            LocalDate curDate = LocalDate.now();
            busDate = curDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        String userId = InvocationInfoProxy.getUserid();
        String userName = InvocationInfoProxy.getUsername();
        userInfo.setUserId(userId);
        userInfo.setUserName(userName);
        userInfo.setBusDate(busDate);

        // 查询用户信息
        QuerySchema schema = QuerySchema.create();
        schema.addSelect("userId,code,name,mobile,email");
        QueryConditionGroup cond = new QueryConditionGroup();
        cond.addCondition(new QueryCondition<String>("enable", ConditionOperator.eq, "1"));
        cond.addCondition(new QueryCondition<String>("userId", ConditionOperator.eq, userId));
        schema.addCondition(cond);
        List<Map<String, Object>> userList = billQryRepository.queryMapBySchema("GZTACT.bipuser.bipUserIdentityMgr", schema, "iuap-apcom-auth");
        if (CollectionUtils.isNotEmpty(userList)) {
            Map<String, Object> user = userList.get(0);
            if (user.get("code") != null) {
                userInfo.setUserCode(user.get("code").toString());
            }
            if (user.get("mobile") != null) {
                userInfo.setMobile(user.get("mobile").toString());
            }
            if (user.get("email") != null) {
                userInfo.setEmail(user.get("email").toString());
            }
        }

        // 查询当前员工信息
        StaffInfoQueryParam param = new StaffInfoQueryParam();
        param.setUserIds(Arrays.asList(InvocationInfoProxy.getUserid()));
        param.setTenantId(InvocationInfoProxy.getTenantid());
        param.setOnlyMainJobInfo(true);
        param.setContainsEndMainJobInfo("false");
        param.setContainsDisableStaff("false");
        param.setContainsPtJob("false");
        param.setContainsEndptJobInfo("false");
        BaseResponse resp = staffInfoQueryService.getStaffByStaffUserIds(param);

        if (resp == null || resp.getCode() != 1) {
            throw new RuntimeException("获取当前员工信息失败！");
        }

        Map<String, Object> data = resp.getData();
        if (data.get("data") != null) {
            JSONArray jarr = JSONArray.parseArray(JSONArray.toJSONString(data.get("data")));
            JSONObject staffInfo = (JSONObject) jarr.iterator().next();
            JSONArray mainjobinfo = staffInfo.getJSONArray("mainjobinfo");
            JSONObject mainjob = (JSONObject) mainjobinfo.iterator().next();

            userInfo.setStaffId(mainjob.getString("staffId"));
            userInfo.setOrgId(mainjob.getString("orgId"));
            userInfo.setDeptId(mainjob.getString("deptId"));
        }

        return userInfo;
    }
}
