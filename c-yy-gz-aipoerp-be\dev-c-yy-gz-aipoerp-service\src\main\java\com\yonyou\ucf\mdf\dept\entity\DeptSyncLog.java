/*--------------------JavaPOJO for 部门同步日志(DeptSyncLog)--------------------*/
package com.yonyou.ucf.mdf.dept.entity;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 部门同步日志
 * @Date 2025-07-17 14:26:54
 * @since 2023/11/28
 **/
@YMSEntity(name = "AIPOERPCREATE.AIPOERPCREATE.DeptSyncLog", domain = "c-yy-gz-aipoerp")
public class DeptSyncLog extends SuperDO {
	public static final String ENTITY_NAME = "AIPOERPCREATE.AIPOERPCREATE.DeptSyncLog";
	public static final String DEPTID = "deptId";
	public static final String DEPTCODE = "deptCode";
	public static final String DEPTNAME = "deptName";
	public static final String ORGID = "orgId";
	public static final String ORGCODE = "orgCode";
	public static final String ORGNAME = "orgName";
	public static final String PARENTID = "parentId";
	public static final String PARENTCODE = "parentCode";
	public static final String PARENTNAME = "parentName";
	public static final String ENABLE = "enable";
	public static final String MODIFIEDTIME = "modifiedtime";
	public static final String LASTSYNCDATE = "lastSyncDate";
	public static final String SUCCESS = "success";
	public static final String ERRMSG = "errMsg";
	public static final String REQUESTDATA = "requestData";
	public static final String RESPONSEDATA = "responseData";
	public static final String ERRSTACK = "errStack";
	public static final String BUSINESSDATE = "businessDate";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 部门id */
	private String deptId;
	/* 部门编码 */
	private String deptCode;
	/* 部门名称 */
	private String deptName;
	/* 所属组织id */
	private String orgId;
	/* 所属组织编码 */
	private String orgCode;
	/* 所属组织名称 */
	private String orgName;
	/* 上级部门id */
	private String parentId;
	/* 上级部门编码 */
	private String parentCode;
	/* 上级部门名称 */
	private String parentName;
	/* 启停用状态 */
	private String enable;
	/* 部门修改时间 */
	private Date modifiedtime;
	/* 最近一次同步时间 */
	private Date lastSyncDate;
	/* 同步结果 */
	private String success;
	/* 错误信息 */
	private String errMsg;
	/* 推送数据 */
	private String requestData;
	/* 返回数据 */
	private String responseData;
	/* 错误堆栈 */
	private String errStack;
	/* 单据日期 */
	private String businessDate;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public void setEnable(String enable) {
		this.enable = enable;
	}

	public void setModifiedtime(Date modifiedtime) {
		this.modifiedtime = modifiedtime;
	}

	public void setLastSyncDate(Date lastSyncDate) {
		this.lastSyncDate = lastSyncDate;
	}

	public void setSuccess(String success) {
		this.success = success;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public void setRequestData(String requestData) {
		this.requestData = requestData;
	}

	public void setResponseData(String responseData) {
		this.responseData = responseData;
	}

	public void setErrStack(String errStack) {
		this.errStack = errStack;
	}

	public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getDeptId() {
		return deptId;
	}

	public String getDeptCode() {
		return deptCode;
	}

	public String getDeptName() {
		return deptName;
	}

	public String getOrgId() {
		return orgId;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public String getParentId() {
		return parentId;
	}

	public String getParentCode() {
		return parentCode;
	}

	public String getParentName() {
		return parentName;
	}

	public String getEnable() {
		return enable;
	}

	public Date getModifiedtime() {
		return modifiedtime;
	}

	public Date getLastSyncDate() {
		return lastSyncDate;
	}

	public String getSuccess() {
		return success;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public String getRequestData() {
		return requestData;
	}

	public String getResponseData() {
		return responseData;
	}

	public String getErrStack() {
		return errStack;
	}

	public String getBusinessDate() {
		return businessDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
