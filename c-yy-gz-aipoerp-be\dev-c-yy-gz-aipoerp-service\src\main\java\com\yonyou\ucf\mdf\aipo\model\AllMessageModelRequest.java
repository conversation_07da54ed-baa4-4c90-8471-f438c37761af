package com.yonyou.ucf.mdf.aipo.model;

import java.io.Serializable;
import java.util.List;

public class AllMessageModelRequest implements Serializable {

    //注册系统编码
    private String registerCode;

    //消息集成对象
    private List<AllMessageModel> messages;

    public AllMessageModelRequest() {
    }

    public AllMessageModelRequest(String registerCode, List<AllMessageModel> messages) {
        this.registerCode = registerCode;
        this.messages = messages;
    }

    public String getRegisterCode() {
        return registerCode;
    }

    public void setRegisterCode(String registerCode) {
        this.registerCode = registerCode;
    }

    public List<AllMessageModel> getMessages() {
        return messages;
    }

    public void setMessages(List<AllMessageModel> messages) {
        this.messages = messages;
    }

    @Override
    public String toString() {
        return "{" +
                "registerCode='" + registerCode + '\'' +
                ", messages=" + messages +
                '}';
    }
}
