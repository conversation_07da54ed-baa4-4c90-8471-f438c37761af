package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.rbsm.constants.PayTypeEnum;
import com.yonyou.ucf.mdf.rbsm.model.PayOrgConfig;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPayOrgConfigQryService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 *         2025年4月7日
 */
@Service
public class PayOrgConfigQryServiceImpl implements IPayOrgConfigQryService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, PayOrgConfig> queryAllForMap() {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("payorg,cfinaceorg,cfinacedept");
		List<PayOrgConfig> result = (List<PayOrgConfig>) billQryRepository
				.queryBySchema("PAY_ORG_CFG.PAY_ORG_CFG.PayOrgConfig", schema);
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyMap();
		}
		return result.stream().collect(Collectors.toMap(PayOrgConfig::getPayorg, v -> v, (v1, v2) -> v2));
	}

	@Override
	public List<Map<String, Object>> queryByPayType(PayTypeEnum payType) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("payType,payorg,cfinaceorg,cfinacedept,explain,pkBalatype");
		// 重命名enterprisebank，返回的数据原字段enterprisebankacct会被重命名
		schema.addSelect(
				new QueryField("enterprisebankacct", "enterprisebank", null, "bd.enterprise.OrgFinBankacctVO/id"));
		// enterprisebankacct.必须使用原始字段(不能使用重命名后的字段enterprisebank，否则报错找不到属性)，如果不加as重命名，则生成的字段为enterprisebank_acctName（使用重命名后的动态字段，.号变成_）
		schema.addSelect(
				"enterprisebankacct.orgid as orgid, enterprisebankacct.acctName as acctName,enterprisebankacct.account as account,enterprisebankacct.acctType as acctType,enterprisebankacct.bank as bank,enterprisebankacct.bankNumber as bankNumber");
		schema.addSelect(new QueryField("supplier", "supplier", null, "aa.vendor.Vendor/id"));
		schema.addSelect("supplier.id as supplierId,supplier.code as supplierCode,supplier.name as supplierName");
		QueryConditionGroup condition = QueryConditionGroup.and(QueryCondition.name("payType").eq(payType.getCode()));
		schema.addCondition(condition);
		List<Map<String, Object>> result = billQryRepository.queryMapBySchema("PAY_ORG_CFG.PAY_ORG_CFG.PayOrgConfig",
				schema);
		return result;
	}

}
