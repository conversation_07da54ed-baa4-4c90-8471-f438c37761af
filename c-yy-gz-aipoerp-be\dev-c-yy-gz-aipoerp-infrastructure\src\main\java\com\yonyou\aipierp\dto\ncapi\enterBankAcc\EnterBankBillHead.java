package com.yonyou.aipierp.dto.ncapi.enterBankAcc;

import com.alibaba.fastjson.JSONObject;

public class EnterBankBillHead extends JSONObject {

    /**
     * "2"(0=个人,1=客户,2=公司,3=供应商 ),必输
     */
    public String ACCCLASS = "accclass";
    /**
     * "0001A910000000000ZVZ"(组织旗舰版组织id),必输
     */
    public String PK_ORG = "pk_org";
    /**
     *  "A"（集团）,必输
     */
    public String PK_GROUP = "pk_group";
    /**
     * testnum001"（账号）,必输
     */
    public String ACCNUM = "accnum";
    /**
     * "testname001"（户名）,必输
     */
    public String ACCNAME = "accname";
    /**
     * "testnum001"（编码）,必输
     */
    public String CODE = "code";
    /**
     * "testname001"（名称）,必输
     */
    public String NAME = "name";
    /**
     * "pk_bankdoc"（开户银行）,必输
     */
    public String PK_BANKDOC = "pk_bankdoc";
    /**
     * "pk_banktype"（银行类别）,
     */
    public String PK_BANKTYPE = "pk_banktype";
    /**
     * "111"（助记码）,
     */
    public String MNECODE = "mnecode";
    /**
     * "2019-04-13"（开户日期）,必输
     */
    public String ACCOPENDATE = "accopendate";
    /**
     * "2(0=收入,1=支出,2=收支)",必输
     */
    public String ARAPPROP = "arapprop";
    /**
     * "2"网银开通状态（0=未开通,1=开通查询,2=开通查询及支付,3=开通落地支付）,必输
     */
    public String NETQUERYFLAG = "netqueryflag";
    /**
     * "3"账户属性（0=基本,1=临时,2=一般,3=专用）,必输
     */
    public String ACCATTRIBUTE = "accattribute";
    /**
     * "2"总分属性（0=总账户,1=分账户,2=独立账户）,必输
     */
    public String GENEBRANPROP = "genebranprop";
    /**
     * "N"（是否集团账户）,必输
     */
    public String GROUPACCOUNT = "groupaccount";
    /**
     * "0"账号状态（0=正常,1=冻结,2=部分冻结,3=销户）,必输
     */
    public String ACCSTATE = "accstate";
    /**
     * "0"账户性质（0=公司,1=个人）,必输
     */
    public String ACCOUNTPROPERTY = "accountproperty";
    /**
     * "永丰路68号"（所在地）,
     */
    public String ADDRESS = "address";
    /**
     * "王"（联系人）,
     */
    public String CONTACTPSN = "contactpsn";
    /**
     * 电话
     */
    public String TEL = "tel";
    /**
     * 题目要大要长题目要大要长"（备注）,
     */
    public String MEMO = "memo";
    /**
     * "00017"（网银接口类别）,
     */
    public String PK_NETBANKINFTP = "pk_netbankinftp";
    /**
     * "111"（联行号
     */
    public String COMBINENUM = "combinenum";
    /**
     * "111"（机构号/分行号）,
     */
    public String ORGNUMBER = "orgnumber";
    /**
     * 海淀分行上地支行"（开户地区）,
     */
    public String BANKAREA = "bankarea";
    /**
     * "北京"（省份）,
     */
    public String PROVINCE = "province";
    /**
     * "海淀"（地区）,
     */
    public String CITY = "city";
    /**
     * 客户编号
     */
    public String CUSTOMERNUMBER = "customernumber";
    /**
     * "N"（签约）,
     */
    public String ISSIGNED = "issigned";
    /**
     * "org1"（开户单位，旗舰版组织id）,必输
     */
    public String FINANCEORG = "financeorg";
    /**
     * "org1"（核算归属组织，旗舰版组织id）,必输
     */
    public String CONTROLORG = "controlorg";
    /**
     *  "2"启用状态（1=未启用,2=已启用,3=已停用）,必输
     */
    public String ENABLESTATE = "enablestate";
    /**
     * "a"（人行联行名称）,
     */
    public String COMBINEACCNAME = "combineaccname";
    /**
     * "0"（查询余额接口 0=独立户余额接口,1=归集户余额接口）,
     */
    public String QRYBALANCEITF = "qrybalanceitf";
    /**
     * 币种子表
     */
    public String BANKACCSUB = "bankaccsub";


    public void setAccclass(String accclass) {
        this.put(ACCCLASS, accclass);
    }

    public void setPk_org(String pk_org) {
        this.put(PK_ORG, pk_org);
    }

    public void setPk_group(String pk_group) {
        this.put(PK_GROUP, pk_group);
    }

    public void setAccnum(String accnum) {
        this.put(ACCNUM, accnum);
    }

    public void setAccname(String accname) {
        this.put(ACCNAME, accname);
    }

    public void setCode(String code) {
        this.put(CODE, code);
    }

    public void setName(String name) {
        this.put(NAME, name);
    }

    public void setPk_bankdoc(String pk_bankdoc) {
        this.put(PK_BANKDOC, pk_bankdoc);
    }

    public void setPk_banktype(String pk_banktype) {
        this.put(PK_BANKTYPE, pk_banktype);
    }

    public void setMnecode(String mnecode) {
        this.put(MNECODE, mnecode);
    }

    public void setAccopendate(String accopendate) {
        this.put(ACCOPENDATE, accopendate);
    }

    public void setArapprop(String arapprop) {
        this.put(ARAPPROP, arapprop);
    }

    public void setNetqueryflag(String netqueryflag) {
        this.put(NETQUERYFLAG, netqueryflag);
    }

    public void setAccattribute(String accattribute) {
        this.put(ACCATTRIBUTE, accattribute);
    }

    public void setGenebranprop(String genebranprop) {
        this.put(GENEBRANPROP, genebranprop);
    }

    public void setGroupaccount(String groupaccount) {
        this.put(GROUPACCOUNT, groupaccount);
    }

    public void setAccstate(String accstate) {
        this.put(ACCSTATE, accstate);
    }

    public void setAccountproperty(String accountproperty) {
        this.put(ACCOUNTPROPERTY, accountproperty);
    }

    public void setAddress(String address) {
        this.put(ADDRESS, address);
    }

    public void setContactpsn(String contactpsn) {
        this.put(CONTACTPSN, contactpsn);
    }

    public void setTel(String tel) {
        this.put(TEL, tel);
    }

    public void setMemo(String memo) {
        this.put(MEMO, memo);
    }

    public void setPk_netbankinftp(String pk_netbankinftp) {
        this.put(PK_NETBANKINFTP, pk_netbankinftp);
    }

    public void setCombinenum(String combinenum) {
        this.put(COMBINENUM, combinenum);
    }

    public void setOrgnumber(String orgnumber) {
        this.put(ORGNUMBER, orgnumber);
    }

    public void setBankarea(String bankarea) {
        this.put(BANKAREA, bankarea);
    }

    public void setProvince(String province) {
        this.put(PROVINCE, province);
    }

    public void setCity(String city) {
        this.put(CITY, city);
    }

    public void setCustomernumber(String customernumber) {
        this.put(CUSTOMERNUMBER, customernumber);
    }

    public void setIssigned(String issigned) {
        this.put(ISSIGNED, issigned);
    }

    public void setFinanceorg(String financeorg) {
        this.put(FINANCEORG, financeorg);
    }

    public void setControlorg(String controlorg) {
        this.put(CONTROLORG, controlorg);
    }

    public void setEnablestate(String enablestate) {
        this.put(ENABLESTATE, enablestate);
    }

    public void setCombineaccname(String combineaccname) {
        this.put(COMBINEACCNAME, combineaccname);
    }

    public void setQrybalanceitf(String qrybalanceitf) {
        this.put(QRYBALANCEITF, qrybalanceitf);
    }

    public void setBankaccsub(JSONObject bankaccsub) {
        this.put(BANKACCSUB, bankaccsub);
    }
}
