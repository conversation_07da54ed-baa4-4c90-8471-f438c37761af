package com.yonyou.aipierp.dto.ncapi.customer;

import com.alibaba.fastjson.JSONObject;

public class CustomerBillPf extends JSONObject {

    /**
     * "申请集团"
     */
    String PK_GROUP ="pk_group";
    /**
     * "申请组织"
     */
    String PK_ORG = "pk_org";
    /**
     * "(0=本组织,1=集团,2=全局)",
     */
    String DESTORG = "destorg";
    /**
     * "客户编码",
     */
    String CUSTOMERCODE = "customercode";
    /**
     * 客户名称
     */
    String CUSTOMERNAME = "customername";
    /**
     * "客户基本分类"（高级版档案，需要两边维护code一致）,
     */
    String PK_CUSTCLASS = "pk_custclass";
    /**
     * 主键
     */
    String UPDATE_CUST = "update_cust";

    public void setPk_group(String pk_group) {
        this.put(PK_GROUP, pk_group);
    }

    public void setPk_org(String pk_org) {
        this.put(PK_ORG, pk_org);
    }

    public void setDestorg(String destorg) {
        this.put(DESTORG, destorg);
    }

    public void setCustomercode(String customercode) {
        this.put(CUSTOMERCODE, customercode);
    }

    public void setCustomername(String customername) {
        this.put(CUSTOMERNAME, customername);
    }

    public void setPk_custclass(String pk_custclass) {
        this.put(PK_CUSTCLASS, pk_custclass);
    }

    public void setUpdate_cust(String update_cust) {
        this.put(UPDATE_CUST, update_cust);
    }
}
