package com.yonyou.ucf.mdf.iris.config;

import com.yonyou.cloud.middleware.rpc.RPCBeanFactory;
import com.yonyou.cloud.middleware.rpc.SpringExecuteTartgetLoader;
import com.yonyoucloud.ctm.stwb.plugin.plugin.rpc.payindex.IPayIndexReqeustPlugin;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2025/4/10 15:54
 * @DESCRIPTION IRIS接口注册配置类
 */
@Configuration
public class RpcConfigBean {

    // IRIS注册需要的appCode 需要替换成真实的appCode
    private static final String APPCODE = "c-yy-gz-aipoerp";
    // IRIS注册需要的beanId 需要替换成真实的beanId
    private static final String BEANID = " payIndexRequestPluginImpl";

    /**
     * 将接口实现注入到RPC的bean工厂，供调用方动态调用
     * @return RPCBeanFactory
     */
    @Bean
    public RPCBeanFactory outputBeanFactory() {
        // IPayIndexReqeustPlugin.class是对接需要实现的接口 需要替换成真实接口
        SpringExecuteTartgetLoader.registerRemoteBean(IPayIndexReqeustPlugin.class, BEANID, APPCODE);
        return new RPCBeanFactory(APPCODE, Collections.singletonList(IPayIndexReqeustPlugin.class.getName()));
    }
}
