package com.yonyou.ucf.mdf.aipo.utils;

import com.yonyou.ucf.mdd.ext.model.BillContext;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import org.imeta.biz.base.Objectlizer;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.base.Json;

import java.util.ArrayList;
import java.util.List;

public class BillInfoUtils {
    public static List<BizObject> decodeBills(BillContext billContext, Object obj) throws Exception {
        List<BizObject> bills = new ArrayList();
        if (obj instanceof String) {
            String data = (String)obj;
            Json json = new Json(data);
            bills = Objectlizer.decode(json, billContext.getFullname());
        } else if (obj instanceof List) {
            bills = (List)obj;
        }

        return (List)bills;
    }
}
