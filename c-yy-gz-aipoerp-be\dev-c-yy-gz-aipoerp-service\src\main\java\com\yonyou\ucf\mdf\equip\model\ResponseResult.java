package com.yonyou.ucf.mdf.equip.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/2/14 16:38
 * @DESCRIPTION 响应结果
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseResult<T> {
    public static final String SUCCESS_CODE = "00000";
    public static final String SUCCESS_CODE2 = "200";
    private String code;
    private String message;
    private T data;
    public boolean isSuccess(String customCode) {
        if (StringUtils.isNotBlank(customCode)) {
            return customCode.equals(code);
        }
        return isSuccess();
    }
    public boolean isSuccess2() {
        return SUCCESS_CODE2.equals(code);
    }
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(code);
    }
}
