package com.yonyou.aipoerp.controller;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.IStaffService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/dispatchtask")
@Slf4j
public class AIPODispatchTaskController {

    @Autowired
    IStaffService staffService;

    @RequestMapping("/staffsync")
    public Map<String, Object> syncCollectionAndPaymentFromJHX(HttpServletRequest request, @RequestBody(required = false) JSONObject paramMap) {
        Map<String, Object> resMap = new HashMap<>(2);
        String logId = Optional.ofNullable(request.getHeader("logId")).orElse("");
        Date beginDate = null;
        Date endDate = null;


        if (paramMap != null) {
            beginDate = paramMap.getDate("beginDate");
            endDate = paramMap.getDate("endDate");
        }
        staffService.asyncHandleAccBalanceUpdateTask(logId, beginDate, endDate);
        resMap.put("asynchronized", "true");
        return resMap;
    }
}
