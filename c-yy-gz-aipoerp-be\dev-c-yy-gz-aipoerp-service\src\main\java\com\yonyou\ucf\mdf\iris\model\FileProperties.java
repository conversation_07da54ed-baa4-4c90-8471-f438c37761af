package com.yonyou.ucf.mdf.iris.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileProperties {
    private String creator;
    private Integer orientation;
    private Integer iselecvmatched;
    private Boolean isrepair;
    private Integer inecessity;
    private String vfileid;
    private Integer ipagecount;
    private String vfilepath;
    private Integer ifiletype;
    private String downloadUrlForPreview;
    private String time;
    private List<String> segmentList;
    private Integer ipagenum; // Only present in parent node
}
