<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoldenGate</title>
    <style>
        /* 全局样式 */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: Arial, sans-serif;
            overflow: hidden; /* 隐藏滚动条 */
        }

        /* 动态背景 */
        body {
            background: linear-gradient(-45deg, #0f0c29, #302b63, #24243e, #0f0c29);
            background-size: 400% 400%;
            animation: gradientBG 10s ease infinite; /* 加快动画速度 */
        }

        @keyframes gradientBG {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* 标题样式 */
        h1 {
            font-size: 3rem;
            color: #fff;
            margin: 0;
            text-align: center;
            text-shadow: 0 0 10px rgba(0, 123, 255, 0.8), 0 0 20px rgba(0, 123, 255, 0.6);
        }

        /* 描述文字样式 */
        p {
            font-size: 1.2rem;
            color: #ccc;
            margin: 10px 0 20px 0;
            text-align: center;
            text-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        }

        /* 按钮容器样式 */
        .button-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        /* 按钮样式 */
        a.button {
            display: inline-block;
            width: 200px;
            padding: 10px 20px;
            background-color: #007BFF;
            color: white;
            text-decoration: none;
            font-size: 1rem;
            border-radius: 5px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
        }

        /* 按钮悬停效果 */
        a.button:hover {
            background-color: #0056b3;
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(0, 123, 255, 0.8);
        }

        /* 页面内容居中 */
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            flex-direction: column;
        }
    </style>
</head>
<body>
    <!-- 页面内容容器 -->
    <div class="container">
        <!-- 标题 -->
        <h1>AIPO ZONE</h1>

        <!-- 描述文字 -->
        <p>Hi! Welcom To AIPO Zone!</p>

        <!-- 按钮容器 -->
        <div class="button-container">
            <!-- AIPO员工登录 按钮 -->
            <a href="https://yonyoubip-test.aipocloud.com/cas/thirdSaml2Login?thirdUCId=ffhi84rg&service=https%3A%2F%2Fyonyoubip-test.aipocloud.com%2Flogin%3FtenantId%3De4lxhnr7" class="button">AIPO员工登录</a>

            <!-- 系统运维登录 按钮 -->
            <a href="https://yonyoubip-test.aipocloud.com/" class="button">系统运维登录</a>

            <!-- 供应商登录 按钮 -->
            <a href="https://yonyoubip-test.aipocloud.com/yonbip-cpu-nodesvr/h/e4lxhnr7" class="button">供应商登录</a>
        </div>
    </div>
</body>
</html>