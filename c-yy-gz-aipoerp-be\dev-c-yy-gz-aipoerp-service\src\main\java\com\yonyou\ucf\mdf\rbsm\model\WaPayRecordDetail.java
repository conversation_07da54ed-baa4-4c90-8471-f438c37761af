package com.yonyou.ucf.mdf.rbsm.model;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 薪资发放推送费控记录明细
 * @Date 2025-03-12 17:03:17
 * @since 2023/11/28
 **/
@YMSEntity(name = "SMR002.SMR002.WaPayRecordDetail", domain = "c-yy-gz-aipoerp")
public class WaPayRecordDetail extends SuperDO {
	public static final String ENTITY_NAME = "SMR002.SMR002.WaPayRecordDetail";
	public static final String CODE = "code";
	public static final String NAME = "name";
	public static final String SCNAME = "scName";
	public static final String PAYPERIODNAME = "payPeriodName";
	public static final String PAYDATE = "payDate";
	public static final String PKPAYFILE = "pkPayfile";
	public static final String PKPAYFILEDETAIL = "pkPayfileDetail";
	public static final String STAFFID = "staffId";
	public static final String LABORPAYPERSONALAMOUNT = "laborPayPersonalAmount";
	public static final String LABORPAYCOMPANYAMOUNT = "laborPayCompanyAmount";
	public static final String PERSONALINCOMETAX = "personalIncomeTax";
	public static final String FOREIGNERKEY = "foreignerKey";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 发放单编码 */
	private String code;
	/* 发放单名称 */
	private String name;
	/* 薪资方案名称 */
	private String scName;
	/* 发放期间 */
	private String payPeriodName;
	/* 发放日期 */
	private String payDate;
	/* 发放单id */
	private String pkPayfile;
	/* 发放单明细id */
	private String pkPayfileDetail;
	/* 员工 */
	private String staffId;
	/* 【个人】工会会费 */
	private String laborPayPersonalAmount;
	/* 【单位】工会会费 */
	private String laborPayCompanyAmount;
	/* 个人所得税 */
	private String personalIncomeTax;
	/* 薪资发放推费控记录 */
	private String foreignerKey;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setCode(String code) {
		this.code = code;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setScName(String scName) {
		this.scName = scName;
	}

	public void setPayPeriodName(String payPeriodName) {
		this.payPeriodName = payPeriodName;
	}

	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}

	public void setPkPayfile(String pkPayfile) {
		this.pkPayfile = pkPayfile;
	}

	public void setPkPayfileDetail(String pkPayfileDetail) {
		this.pkPayfileDetail = pkPayfileDetail;
	}

	public void setStaffId(String staffId) {
		this.staffId = staffId;
	}

	public void setLaborPayPersonalAmount(String laborPayPersonalAmount) {
		this.laborPayPersonalAmount = laborPayPersonalAmount;
	}

	public void setLaborPayCompanyAmount(String laborPayCompanyAmount) {
		this.laborPayCompanyAmount = laborPayCompanyAmount;
	}

	public void setPersonalIncomeTax(String personalIncomeTax) {
		this.personalIncomeTax = personalIncomeTax;
	}

	public void setForeignerKey(String foreignerKey) {
		this.foreignerKey = foreignerKey;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public String getScName() {
		return scName;
	}

	public String getPayPeriodName() {
		return payPeriodName;
	}

	public String getPayDate() {
		return payDate;
	}

	public String getPkPayfile() {
		return pkPayfile;
	}

	public String getPkPayfileDetail() {
		return pkPayfileDetail;
	}

	public String getStaffId() {
		return staffId;
	}

	public String getLaborPayPersonalAmount() {
		return laborPayPersonalAmount;
	}

	public String getLaborPayCompanyAmount() {
		return laborPayCompanyAmount;
	}

	public String getPersonalIncomeTax() {
		return personalIncomeTax;
	}

	public String getForeignerKey() {
		return foreignerKey;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
