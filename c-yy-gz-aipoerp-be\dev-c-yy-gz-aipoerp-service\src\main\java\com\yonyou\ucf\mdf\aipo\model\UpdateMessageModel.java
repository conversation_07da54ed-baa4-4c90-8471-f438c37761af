package com.yonyou.ucf.mdf.aipo.model;

import java.io.Serializable;

public class UpdateMessageModel implements Serializable {

    /**
     * 业务系统待办主键
     */
    private String taskId;

    /**
     * 注册系统编码
     */
    private String registerCode;

    /**
     * 状态，0未办理，1已办理
     */
    private int state;

    /**
     * 处理后状态：0/1/2/3同意已办/不同意已办/取消/驳回
     */
    private int subState;

    public UpdateMessageModel() {
    }

    public UpdateMessageModel(String taskId, String registerCode, int state, int subState) {
        this.taskId = taskId;
        this.registerCode = registerCode;
        this.state = state;
        this.subState = subState;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getRegisterCode() {
        return registerCode;
    }

    public void setRegisterCode(String registerCode) {
        this.registerCode = registerCode;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }

    @Override
    public String toString() {
        return "UpdateMessageModel{" +
                "taskId='" + taskId + '\'' +
                ", registerCode='" + registerCode + '\'' +
                ", state=" + state +
                ", subState=" + subState +
                '}';
    }
}

