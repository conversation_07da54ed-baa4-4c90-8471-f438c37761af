package com.yonyou.ucf.mdf.aipo.model;

import java.io.Serializable;
import java.util.Date;

public class AllMessageModel implements Serializable {

    /**
     * 系统注册编码
     */
    private String thirdpartyRegisterCode;

    /**
     * 业务系统消息主键
     */
    private String thirdpartyMessageId;

    /**
     * 消息内容
     */
    private String messageContent;

    /**
     * 消息创建日期
     */
    private String creation_date;

    /**
     * H5穿透地址
     */
    private String messageH5URL;

    /**
     * web穿透地址
     */
    private String messageURL;

    /**
     * 发送方----登录人名称
     */
    private String noneBindingSender;

    /**
     * 接收方---登录人名称
     */
    private String noneBindingReceiver;

    public AllMessageModel() {
    }

    public AllMessageModel(String thirdpartyRegisterCode, String thirdpartyMessageId, String messageContent, String creation_date, String messageH5URL, String messageURL, String noneBindingSender, String noneBindingReceiver) {
        this.thirdpartyRegisterCode = thirdpartyRegisterCode;
        this.thirdpartyMessageId = thirdpartyMessageId;
        this.messageContent = messageContent;
        this.creation_date = creation_date;
        this.messageH5URL = messageH5URL;
        this.messageURL = messageURL;
        this.noneBindingSender = noneBindingSender;
        this.noneBindingReceiver = noneBindingReceiver;
    }

    public String getThirdpartyRegisterCode() {
        return thirdpartyRegisterCode;
    }

    public void setThirdpartyRegisterCode(String thirdpartyRegisterCode) {
        this.thirdpartyRegisterCode = thirdpartyRegisterCode;
    }

    public String getThirdpartyMessageId() {
        return thirdpartyMessageId;
    }

    public void setThirdpartyMessageId(String thirdpartyMessageId) {
        this.thirdpartyMessageId = thirdpartyMessageId;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getCreation_date() {
        return creation_date;
    }

    public void setCreation_date(String creation_date) {
        this.creation_date = creation_date;
    }

    public String getMessageH5URL() {
        return messageH5URL;
    }

    public void setMessageH5URL(String messageH5URL) {
        this.messageH5URL = messageH5URL;
    }

    public String getMessageURL() {
        return messageURL;
    }

    public void setMessageURL(String messageURL) {
        this.messageURL = messageURL;
    }

    public String getNoneBindingSender() {
        return noneBindingSender;
    }

    public void setNoneBindingSender(String noneBindingSender) {
        this.noneBindingSender = noneBindingSender;
    }

    public String getNoneBindingReceiver() {
        return noneBindingReceiver;
    }

    public void setNoneBindingReceiver(String noneBindingReceiver) {
        this.noneBindingReceiver = noneBindingReceiver;
    }

    @Override
    public String toString() {
        return "AllMessageModel{" +
                "thirdpartyRegisterCode='" + thirdpartyRegisterCode + '\'' +
                ", thirdpartyMessageId='" + thirdpartyMessageId + '\'' +
                ", messageContent='" + messageContent + '\'' +
                ", creation_date='" + creation_date + '\'' +
                ", messageH5URL='" + messageH5URL + '\'' +
                ", messageURL='" + messageURL + '\'' +
                ", noneBindingSender='" + noneBindingSender + '\'' +
                ", noneBindingReceiver='" + noneBindingReceiver + '\'' +
                '}';
    }
}
