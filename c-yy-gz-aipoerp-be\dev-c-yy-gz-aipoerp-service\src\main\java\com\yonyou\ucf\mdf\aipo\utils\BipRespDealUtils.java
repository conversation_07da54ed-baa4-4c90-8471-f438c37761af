package com.yonyou.ucf.mdf.aipo.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @since 2024-08-14 15:32:04
 */
@Slf4j
public class BipRespDealUtils {
    /**
     * 判断调用状态并返回data
     * @param respJson
     * @return
     */
    public static void verifyRespJson(String respJson) throws Exception {
        if(respJson == null) {
            throw new Exception("未知的错误");
        }
        JSONObject respObj = JSON.parseObject(respJson);
        if(!"200".equals(respObj.getString("code")) && !"00000".equals(respObj.getString("code"))) {
            log.error("OpenApi调用失败，失败信息为：{}", respJson);
            throw new Exception(respObj.getString("message"));
        }
    }
}
