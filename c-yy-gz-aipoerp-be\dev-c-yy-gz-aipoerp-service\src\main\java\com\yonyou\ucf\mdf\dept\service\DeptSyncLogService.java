package com.yonyou.ucf.mdf.dept.service;

import java.util.List;

import org.imeta.orm.schema.QuerySchema;

import com.yonyou.ucf.mdf.dept.entity.DeptSyncLog;

/**
 * 部门同步日志服务接口
 */
public interface DeptSyncLogService {
    
    /**
     * 保存部门同步日志
     * 
     * @param syncLog 部门同步日志对象
     * @return 保存的部门同步日志
     */
    DeptSyncLog save(DeptSyncLog syncLog);

    /**
     * 根据部门ID查询同步日志
     * 
     * @param deptId 部门ID
     * @return 同步日志列表
     */
    List<DeptSyncLog> findByDeptId(String deptId);

    /**
     * 根据部门编码查询同步日志
     * 
     * @param deptCode 部门编码
     * @return 同步日志列表
     */
    List<DeptSyncLog> findByDeptCode(String deptCode);

    /**
     * 根据查询方案查询同步日志
     * 
     * @param schema 查询方案
     * @return 同步日志列表
     */
    List<DeptSyncLog> findBySchema(QuerySchema schema);

    /**
     * 查询所有同步失败的日志
     * 
     * @return 同步失败的日志列表
     */
    List<DeptSyncLog> findAllFailed();

    /**
     * 根据组织ID查询同步日志
     * 
     * @param orgId 组织ID
     * @return 同步日志列表
     */
    List<DeptSyncLog> findByOrgId(String orgId);

    /**
     * 根据同步结果查询日志
     * 
     * @param success 同步结果 (Y-成功, N-失败)
     * @return 同步日志列表
     */
    List<DeptSyncLog> findBySuccess(String success);
}
