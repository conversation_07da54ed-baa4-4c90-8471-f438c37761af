package com.yonyou.ucf.mdf.equip.controller;

import com.yonyou.ucf.mdf.equip.model.BatchResult;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import com.yonyou.ucf.mdf.equip.service.EquipDataImportService;
import com.yonyou.ypd.bill.response.ResultMessage;
import com.yonyou.ypd.mdf.adapter.controller.BaseController;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/21 12:00
 * @DESCRIPTION 资产卡片导入数据控制器
 */
@RequestMapping("/equip")
@RestController
public class EquipDataImportController extends BaseController {
    @Autowired
    EquipDataImportService dataImportService;

    @PostMapping("/pushChangeBill")
    public void pushDownChangeBill(@RequestBody List<BizObject> bizObjects, HttpServletResponse response) {
        try {
            ResponseResult<BatchResult> result = dataImportService.pushDown(bizObjects);
            if (result.isSuccess2()) {
                renderJson(response, ResultMessage.data(result.getData()));
            } else {
                throw new RuntimeException("推送失败：" + result.getMessage());
            }
        } catch (Exception e) {
            renderJson(response, ResultMessage.error(e.getMessage()));
        }
    }
}
