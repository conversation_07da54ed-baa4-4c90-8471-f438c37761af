package com.yonyou.ucf.mdf.travelexpense.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.travelexpense.service.ITravelMemoApplyQryService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 *         2025年4月2日
 */
@Service
public class TravelMemoApplyQryServiceImpl implements ITravelMemoApplyQryService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@Override
	public List<Map<String, Object>> queryTravelMemoApply(QuerySchema schema) {
		if (schema == null) {
			return Collections.emptyList();
		}
		List<Map<String, Object>> result = billQryRepository
				.queryMapBySchema("znbzbx.travelmemoapply.TravelMemoApplyVO", schema, "znbzbx");
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyList();
		}
		return result;
	}

}
