package com.yonyou.ucf.mdf.aipo.model;

public class TokenEntity {

    /**
     * rest账号
     */
   private  String userName;

    /**
     * rest密码
     */
   private String password;

    public TokenEntity() {
    }

    public TokenEntity(String userName, String password) {
        this.userName = userName;
        this.password = password;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "TokenEntity{" +
                "userName='" + userName + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
