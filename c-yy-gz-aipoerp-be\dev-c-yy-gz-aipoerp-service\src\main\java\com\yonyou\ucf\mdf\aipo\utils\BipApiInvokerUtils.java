package com.yonyou.ucf.mdf.aipo.utils;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.ypd.bill.utils.YpdAppContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * OpenApi调用器
 *
 * <AUTHOR>
 * @since 2024-08-14 15:06:20
 */
@Component
@Slf4j
public class BipApiInvokerUtils {
    public static String getToken() throws Exception {
        //
        String appKey = YpdAppContextUtil.getEnvSerivce().getProperty("YonBIP.appKey", "11");
        String appSecret = YpdAppContextUtil.getEnvSerivce().getProperty("YonBIP.appSecret", "22");

        String timestamp = Long.toString(System.currentTimeMillis());
        String url = System.getProperty("domain.iuap-api-auth") + "/open-auth/selfAppAuth/getAccessToken?appKey=" + appKey + "&timestamp=" + timestamp + "&signature=" + SignUtils.sign(appKey, appSecret, timestamp);
        return JSONObject.parseObject(getObj(url)).getJSONObject("data").getString("access_token");
    }

    /**
     * get方法调用客开发布的OpenAPI
     * @param url 不用加/iuap-api-gateway/租户id和access_token，这两个会自动拼接上
     * @return
     * @throws Exception
     */
    public static String getCustomerBipApi(String url) throws Exception {
        return getObj(getCustomerBipUrl(url));
    }

    /**
     * post方法调用客开发布的OpenAPI
     * @param url 不用加/iuap-api-gateway/租户id和access_token，这两个会自动拼接上
     * @return
     * @throws Exception
     */
    public static String postCustomerBipApi(String url, String reqJson) throws Exception {
        return postObj(getCustomerBipUrl(url), reqJson);
    }

    public static String postCustomerBipApi(String url, String reqJson, Map<String,String> headerMap) throws Exception {
        return postObj(getCustomerBipUrl(url), reqJson,headerMap);
    }

    /**
     * post方法调用原厂OpenAPI
     * @param url 不用加/iuap-api-gateway和access_token，这两个会自动拼接上
     * @return
     * @throws Exception
     */
    public  static String getSystemBipApi(String url) throws Exception {
        return getObj(getSystemBipUrl(url));
    }

    /**
     * post方法调用原厂OpenAPI
     * @param url 不用加/iuap-api-gateway和access_token，这两个会自动拼接上
     * @return
     * @throws Exception
     */
    public static String postSystemBipApi(String url, String reqJson) throws Exception {
        return postObj(getSystemBipUrl(url), reqJson);
    }

    public static String getObj(String url) throws Exception {
        String respJson = HttpUtil.get(url);
        BipRespDealUtils.verifyRespJson(respJson);
        return respJson;
    }

    public static String postObj(String url, String reqJson) throws Exception {
        String respJson = HttpUtil.post(url, reqJson);
        BipRespDealUtils.verifyRespJson(respJson);
        return respJson;
    }
    public static String postObj(String url, String reqJson,Map<String,String> headerMap) throws Exception {
        String respJson = HttpUtil.post(url, reqJson,headerMap);
        BipRespDealUtils.verifyRespJson(respJson);
        return respJson;
    }

    /**
     * 获取请求接口的URL
     * @param url
     * @return
     */
    private static String getCustomerBipUrl(String url) throws Exception {
        return getBipUrl(url, true, true);
    }

    /**
     * 获取请求接口的URL
     * @param url
     * @return
     */
    private static String getSystemBipUrl(String url) throws Exception {
        return getBipUrl(url, false, true);
    }

    /**
     * 获取请求接口的URL
     * @param url
     * @param hasTenantId
     * @param hasToken
     * @return
     */
    private static String getBipUrl(String url, boolean hasTenantId, boolean hasToken) throws Exception {
        return System.getProperty("domain.iuap-api-gateway") + (hasTenantId ? "/" + InvocationInfoProxy.getTenantid() : "") + url + (hasToken ? (url.contains("?") ? "&" : "?") + "access_token=" + getToken() : "");
    }
}
