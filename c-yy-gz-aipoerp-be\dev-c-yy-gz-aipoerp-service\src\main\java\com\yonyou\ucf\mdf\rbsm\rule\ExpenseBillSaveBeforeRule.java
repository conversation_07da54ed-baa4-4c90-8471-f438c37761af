package com.yonyou.ucf.mdf.rbsm.rule;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ucf.mdf.rbsm.model.BustypeSimple;
import com.yonyou.ucf.mdf.rbsm.model.FieldRef;
import com.yonyou.ucf.mdf.rbsm.service.itf.IFieldRefService;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用报销单保存前校验
 * 
 * <AUTHOR>
 *
 *         2025年3月25日
 */
@Component("expenseBillSaveBeforeRule")
@Slf4j
public class ExpenseBillSaveBeforeRule implements IYpdCommonRul {

	@Autowired
	private IBillRepository billRepository;

	@Autowired
	private IFieldRefService fieldRefService;

	@SneakyThrows
	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {

		String action = rulCtxVO.getAction();
		if (!"delete".equals(action)) {
			return null;
		}

		List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
		if (CollectionUtils.isEmpty(bills)) {
			return null;
		}

		log.error("进入通用报销单保存前规则-----------------单据：{}", JSONUtil.toJson(bills));

		String bustype = bills.get(0).getString("bustype");

		Map<String, String> bustypeCodeMap = Maps.newHashMap();
		String sql = "select id,code,name from iuap_apdoc_basedoc.bd_transtype where id = ?";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(bustype);
		List<BustypeSimple> bustypeList = billRepository.queryForDTOList(sql, parameter, BustypeSimple.class);
		if (CollectionUtils.isNotEmpty(bustypeList)) {
			bustypeCodeMap = bustypeList.stream()
					.collect(Collectors.toMap(BustypeSimple::getId, BustypeSimple::getCode));
		}

		List<FieldRef> fieldRefList = fieldRefService
				.getFieldRefByKkFields(Arrays.asList("entertainmentExpenseBustype"));

		Map<String, String> fieldMap = fieldRefList.stream()
				.collect(Collectors.toMap(FieldRef::getKkField, FieldRef::getRefField));

		if (!StringUtils.equals(fieldMap.get("entertainmentExpenseBustype"), bustypeCodeMap.get(bustype))) {
			return null;
		}

		return null;
	}

}
