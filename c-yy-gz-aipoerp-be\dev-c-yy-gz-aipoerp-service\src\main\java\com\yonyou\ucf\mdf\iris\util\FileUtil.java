package com.yonyou.ucf.mdf.iris.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2025/4/14 21:17
 * @DESCRIPTION 文件工具
 */
public class FileUtil {
    public static String urlToBase64(String fileUrl) throws IOException {
        try (InputStream in = new URL(fileUrl).openStream();
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            byte[] fileBytes = bos.toByteArray();
            return Base64.getEncoder().encodeToString(fileBytes);
        }
    }
}
