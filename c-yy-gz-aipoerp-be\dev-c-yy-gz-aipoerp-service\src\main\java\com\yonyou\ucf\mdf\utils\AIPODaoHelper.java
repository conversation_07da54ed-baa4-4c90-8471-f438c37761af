package com.yonyou.ucf.mdf.utils;

import com.yonyou.iuap.yms.imeta.MetaEntityHelper;
import com.yonyou.ypd.bill.action.save.BillSaveProcessor;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;
import lombok.extern.slf4j.Slf4j;
import com.yonyou.iuap.yms.param.BaseEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.imeta.core.model.Entity;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Component
@Slf4j
public class AIPODaoHelper {

    @Autowired
    private BillSaveProcessor billSaveProcessor;
    @Autowired
    private IBillRepository billRepository;
    @Autowired
    private IBillQueryRepository billQueryRepository;


    /**
     * 批量保存
     *
     * @param busiObj
     * @param billDOs
     * @return {@link List }<{@link T }>
     * @throws Exception
     */
    public <T extends IBillDO> List<T> batchSave(String busiObj, List<T> billDOs) throws Exception {
        return this.billSaveProcessor.commonSaveBill(billDOs, busiObj);
    }

    /**
     * 查询自建应用的数据
     *
     * @param schema
     * @return {@link List }<{@link ? } {@link extends } {@link IBillDO }>
     * @throws Exception
     */
    public List<? extends IBillDO> queryInner(QuerySchema schema) throws Exception {
        return this.query(schema, (String) null);
    }

    /**
     * 查询其他领域的表
     *
     * @param schema
     * @return {@link List }<{@link ? } {@link extends } {@link IBillDO }>
     * @throws Exception
     */
    public List<? extends IBillDO> queryOuter(QuerySchema schema) throws Exception {
        String fullName = schema.fullname();
        String group = getGroup(fullName);
        return this.query(schema,group);
    }

    public List<? extends IBillDO> query(QuerySchema schema, String group) throws Exception {
        List<? extends IBillDO> resList = this.billQueryRepository.queryBySchema(schema.fullname(), schema, group);
        return CollectionUtils.isNotEmpty(resList) ? resList : new ArrayList<IBillDO>();
    }



    /**
     * 获取实体的domain，查询时需要填domain信息
     *
     * @param fullName 实体uri
     * @return {@link String }
     */
    public static String getGroup(String fullName){
        Entity entity = MetaEntityHelper.getMetaEntity(fullName);
        return entity.get("domain");
    }


    public <T extends IBillDO> T add(String busiObj, T vo) throws Exception {
        List<T> ret = this.batchAdd(busiObj, Arrays.asList(vo));
        return (T) ret.get(0);
    }

    public <T extends IBillDO> List<T> batchAdd(String busiObj, List<T> billDOs) throws Exception {
        billDOs.parallelStream().forEach((item) -> {
            ((BaseEntity) item).set_status(2);
        });
        return this.billSaveProcessor.commonSaveBill(billDOs, busiObj);
    }

}
