package com.yonyou.ucf.mdf.iris.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessBillType {
	PAY_ORDER(10, "earap.payment.PaymentHeader", "freeChId", "payment", "付款单"),
	GENERAL_EXPENSE_ACCOUNT(2, "znbzbx.commonexpensebill.CommonExpenseBillVO", "expensebillDcs", "znbzbx_expensebill",
			"通用报销单"),
	TRAVEL_EXPENSE_REIMBURSEMENT_FORM(11, "znbzbx.travelexpensebill.TravelExpenseBillVO", "expensebillDcs",
			"znbzbx_travelexpbill", "差旅费报销单"),
	CORPORATE_PREPAYMENT_FORM(12, "znbzbx.pubprepayloanbill.PubPrePayLoanBillVO", "loanBillDcs", "znbzbx_pubprepay",
			"对公预付单");

	private Integer businessBillType;
	private String billUri;
	private String customFiled;
	private String billType;
	private String description;

	public static List<Integer> getAllBusinessBillType() {
		return Arrays.stream(BusinessBillType.values()).map(BusinessBillType::getBusinessBillType)
				.collect(Collectors.toList());
	}

	public static BusinessBillType getBusinessBillType(Integer businessBillType) {
		BusinessBillType[] values = BusinessBillType.values();
		for (BusinessBillType billType : values) {
			if (Objects.equals(billType.getBusinessBillType(), businessBillType)) {
				return billType;
			}
		}
		return null;
	}
}
