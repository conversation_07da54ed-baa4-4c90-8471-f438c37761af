package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.rbsm.model.FieldRef;
import com.yonyou.ucf.mdf.rbsm.service.itf.IFieldRefService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 *         2025年3月10日
 */
@Service
public class FieldRefServiceImpl implements IFieldRefService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, FieldRef> getFieldRef() {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("*");
		QueryConditionGroup cond = new QueryConditionGroup();
		cond.addCondition(new QueryCondition<String>("dr", ConditionOperator.eq, "0"));
		schema.addCondition(cond);
		List<FieldRef> result = (List<FieldRef>) billQryRepository.queryBySchema("FIELDREF001.FIELDREF001.FieldRef",
				schema);
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyMap();
		}
		return result.stream().collect(Collectors.toMap(FieldRef::getRefField, v -> v, (v1, v2) -> v2));
	}

	@SuppressWarnings("unchecked")
	@Override
	public FieldRef getFieldRefByKkField(String kkField) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("*");
		QueryConditionGroup cond = new QueryConditionGroup();
		cond.addCondition(new QueryCondition<String>("dr", ConditionOperator.eq, "0"));
		cond.addCondition(new QueryCondition<String>("kkField", ConditionOperator.eq, kkField));
		schema.addCondition(cond);
		List<FieldRef> result = (List<FieldRef>) billQryRepository.queryBySchema("FIELDREF001.FIELDREF001.FieldRef",
				schema);
		if (CollectionUtils.isNotEmpty(result)) {
			return result.get(0);
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<FieldRef> getFieldRefByKkFields(List<String> kkFields) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("*");
		QueryConditionGroup cond = new QueryConditionGroup();
		cond.addCondition(new QueryCondition<String>("dr", ConditionOperator.eq, "0"));
		cond.addCondition(new QueryCondition<String>("kkField", ConditionOperator.in, kkFields));
		schema.addCondition(cond);
		List<FieldRef> result = (List<FieldRef>) billQryRepository.queryBySchema("FIELDREF001.FIELDREF001.FieldRef",
				schema);
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyList();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, String> getFieldRefKkKey() {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("kkField,refField");
		QueryConditionGroup cond = new QueryConditionGroup();
		cond.addCondition(new QueryCondition<String>("dr", ConditionOperator.eq, "0"));
		schema.addCondition(cond);
		List<FieldRef> result = (List<FieldRef>) billQryRepository.queryBySchema("FIELDREF001.FIELDREF001.FieldRef",
				schema);
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyMap();
		}
		return result.stream().collect(Collectors.toMap(FieldRef::getKkField, FieldRef::getRefField, (v1, v2) -> v2));
	}

}
