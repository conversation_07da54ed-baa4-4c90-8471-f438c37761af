package com.yonyou.ucf.mdf.rbsm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Expapportion {
    private String id;//否	费用分摊id(_status为Update更新时必填)
    private String vfinacedeptid;//否	费用承担部门(支持id和code)    示例：****************
    private String cfinaceorg;//否	费用承担组织(支持id和code)    示例：****************
    private String caccountorg;//否	会计主体(支持id和code)    示例：****************
    private String pk_busimemo;//否	费用项目id(id与编码必填一项)    示例：****************
    private String pk_busimemo_code;//否	费用项目编码(id与编码必填一项)
    private String vcurrency = "CNY";//否	报销币种(支持id和code)    示例：****************
    private String vnatcurrency = "CNY";//否	组织本币(支持id和code)    示例：****************
    private String vnatexchratetype = "01";//否	组织本币汇率类型(支持id和code)    示例：py7y8nze
    private String dnatexchratedate;//否	组织本币汇率日期(格式：yyyy-MM-dd)    示例：2021-09-26
    private String nnatbaseexchrate = "1";//否	组织本币企业汇率    示例：1
    private String nnatexchrate = "1";//否	组织本币汇率    示例：1
    private String napportmny;//否	含税金额    示例：133.74
    private String nnatapportmny;//否	含税金额-本币    示例：133.74
    private String napportrate = "100";//否	比例    示例：100
    private String napportnotaxmny;//否	不含税金额    示例：129.87
    private String nnatapportnotaxmny;//否	不含税金额-本币    示例：129.87
    private String _status = "Insert";//否	操作标识, Insert:新增、Update:更新 示例:Insert    示例：Insert
}
