package com.yonyou.ucf.mdf.rbsm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 薪资发放单新增项目
 * 
 * <AUTHOR>
 *
 *         2025年3月10日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WaItem {

	private String pkPayfile; // 发放单id
	private String dr; // 删除标注(0:没删除 1:删除)
	private String itemCode; // 项目编码
	private String itemName; // 项目名字(简体中文)
	private String itemName2; // 项目名字(英文)
	private String itemName3; // 项目名字(繁体中文)
	private String showOrder; // 显示的排序位置
	private String isItem; // 是否薪资项目(1:是 0:不是)
	private String fromFlag; // 项目来源(0:公式（包括考勤/绩效/社保/外部数据源等）；1:输入/导入；薪酬体系)
	private String approveFlag; // 审批标志(0：不审批 1：审批)
	private String dateType; // 数据类型(0:整形 1:数值型 2：字符 3：布尔 4：日期)
	private String pkWaItem; // 项目主键
	private String isShow; // 是否显示(1:显示 0：不显示)
	private String schemeItemId; // 方案项目id
	private String fldDecimal; // 小数位数
	private String pkScheme; // 发薪方案Id
	private String fldWidth; // 数据长度
	private String roundType; // 小数舍入方式(0进位/1:舍位/4：四舍五入)
	private String clearFlag; // 下月清零（0：否，1：是）
	private String isModify; // 是否可以修改(0:不可以 1：可以)
	private String segmentAccount; // 是否是分段项目(0:不分段 1：分段)
	private String isFixed; // 是否固定列(0否，1是)
	private String fromFlagName; // 数据来源描述
	private String mullangName; // 多语名字
	private String payFileDisplay; // 默认是否显示(0:不显示,1:显示)
	private String formula; // 公式
	private String pkWaSchemeItem; // 发薪方案项目ID
	private String es; // 实体状态(0:正常,1:不正常)
}
