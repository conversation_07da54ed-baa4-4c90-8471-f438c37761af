package com.yonyou.ucf.mdf.aipo.service;

import java.util.List;

import org.imeta.orm.schema.QuerySchema;

import com.yonyou.ucf.mdf.aipo.model.AIPOVoucherEventLog;

/**
 * 凭证事件日志服务接口
 */
public interface AIPOVoucherEventLogService {
    /**
	 * 保存凭证事件日志
	 * 
	 * @param eventLog 凭证事件日志对象
	 * @return 保存的凭证事件日志
	 */
	AIPOVoucherEventLog save(AIPOVoucherEventLog eventLog);

    /**
	 * 根据事件ID查询事件日志
	 * 
	 * @param eventid 事件id,事件id唯一
	 * @return 事件日志
	 */
	AIPOVoucherEventLog findByEventId(String eventid);

	/**
	 * 根据凭证ID查询事件日志
	 * 
	 * @param billId 凭证ID
	 * @return 事件日志列表
	 */
    List<AIPOVoucherEventLog> findByBillId(String billId);

	/**
	 * 根据查询方案查询事件日志
	 * 
	 * @param schema 查询方案
	 * @return 事件日志列表
	 */
	List<AIPOVoucherEventLog> findBySchema(QuerySchema schema);

	/**
	 * 查询所有处理失败且处理状态不是处理中的事件日志
	 * 
	 * @return 事件日志列表
	 */
	List<AIPOVoucherEventLog> findAllFail();

}
