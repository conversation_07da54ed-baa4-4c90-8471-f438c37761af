package com.yonyou.ucf.mdf.aipo.utils;

import com.yonyou.ypd.bill.utils.YpdAppContextUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MsgForwardUrlConfig {
    private String nccUrl = YpdAppContextUtil.getEnvSerivce().getProperty("nc.api.domain", "http://*************:8089");
 

    @Value("/uapws/rest/voucherDetailService/voucherDetailAddAndUpdate")
    private String pz_add;
    @Value("/uapws/rest/voucherDetailService/voucherOperateAction")
    private String pz_action;


    @Value("/uapws/rest/voucherDetailService/voucherDetailAddAndUpdate")
    private String pz_update;

    @Value("/uapws/rest/voucherDetailService/voucherDetailDel")
    private String pz_del;

    @Value("/uapws/rest/voucherDetailService/voucherDetailOneDel")
    private String pz_onedel;
    @Value("/uapws/rest/voucherDetailService/voucherCheck")
    private String pz_check;

    @Value("/uapws/rest/voucherDetailService/voucherQueryByPeriod")
    private String pz_query;

    public MsgForwardUrlConfig() {
    }

    public Map getUrlMap() {
        Map<String, String> urlMap = new HashMap();
        urlMap.put("pz_add", this.nccUrl + this.pz_add);
        urlMap.put("pz_action", this.nccUrl + this.pz_action);
        urlMap.put("pz_update", this.nccUrl + this.pz_update);
        urlMap.put("pz_del", this.nccUrl + this.pz_del);
        urlMap.put("pz_query", this.nccUrl + this.pz_query);
        urlMap.put("pz_onedel", this.nccUrl + this.pz_onedel);
        urlMap.put("pz_check", this.nccUrl + this.pz_check);
        return urlMap;
    }
}
