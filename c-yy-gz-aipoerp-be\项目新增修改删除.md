# 1新增修改项目档案
### 1.1请求类型 
POST
### 1.2请求地址
 http://IP:port/nccloud/api/uapbd/projectmanage/project/addEx

### 1.3请求参数说明

#### 1.3.1参数说明
  ```
{
    "ufinterface": {
        "billtype": "project"（固定值）,
        "sender": "default"（固定值）,
        "replace": "Y"（固定值）,
        "isexchange": "Y"（固定值）,
        "account": "NCCDB2311JCYS"（帐套编码）,
        "groupcode": "A"（集团编码）,
        "bill": {
        "id": "项目id"（旗舰版项目id）,
            "billhead": {
                "pk_group": "集团"(必输，code),
                "pk_org": "管理组织"(必输，旗舰版id),
                "pk_org_v": "管理组织版本",
                "project_code": "项目编码"(必输),
                "project_name": "项目名称"(必输),
                "pk_eps": "项目基本分类"(必输),
                "pk_parentpro": "父项目"(旗舰版父项目id),
                "project_sh_name":"项目简称",
                 "project_ot_name":"曾用名",
                "plan_start_date": "计划开始日期",
                "plan_finish_date": "计划完成日期",
                "actu_start_date": "实际开始日期",
                "actu_finish_date": "实际完成日期",
                "planduration": "计划工期",
                "status_date": "状态日期",
                "begin_flag": "期初",
                "pk_duty_dept": "责任部门",
                "pk_duty_org_v": "责任组织多版本",
                "pk_duty_org": "责任组织",
                "pk_duty_dept_v": "责任组织版本",
                "order_finish_date": "排程完成日期",
                "req_start_date": "需求开始日期",
                "req_finish_date": "需求完成日期",
                "start_work_date": "开工日期",
                "finish_work_date": "完工日期",
                "memo": "备注",
                "bill_type": "单据类型",
                "pk_parenttask": "父项目任务",
                "orderduration": "排程工期",
                "estimate_mny": "估算金额",
                "estimate_group": "估算金额(集团)",
                "general_mny": "概算金额",
                "general_global": "概算金额(全局)",
                "general_group": "概算金额(集团)",
                "pk_wbstemplate": "WBS模板",
                "actuduration": "实际工期",
                "pk_yearplan_b": "年度计划明细",
                "src_pk_transitype": "来源交易类型主键",
                "src_pk_bill": "来源单据主键",
                "pk_projectclass": "项目类型",
                "creationstate": "创建时项目状态",
                "reqduration": "需求工期",
                "pk_dutier": "责任人",
                "src_bill_code": "来源单据编码",
                "order_start_date": "排程开始日期",
                "pk_transitype": "交易类型主键",
                "check_date": "验收日期",
                "upload_flag": "N",
                "pk_busitype": "业务流程",
                "pk_workcalendar": "项目日历",
                "plan_auth": "编制人权限",
                "src_bill_type": "来源单据类型",
                "src_transi_type": "来源交易类型",
                "pk_planmaker": "计划编制人",
                "tax_flag": "物资含税",
                "transi_type": "交易类型",
                "def1~def50":"(自定义1~50)",
                "hdef51~hdef100":"(自定义51~100)",
                "bodyvos"（参与组织）: {
                    "item": [
                        {
                            "pk_parti_org": "参与组织旗舰版id",
                            "memo": "备注",
                            "def1~def10":"(自定义1~自定义10)"
                        }
                    ]
                }
            }
        }
    }
}

  ```
#### 1.3.2请求示例
  ```
{
    "ufinterface": {
        "billtype": "project",
        "sender": "default",
        "replace": "Y",
        "isexchange": "Y",
        "account": "YSGZ",
        "groupcode": "A",
        "bill": {
        "id": "xmbiptestid0002",
            "billhead": {
                "pk_group": "A",
                "pk_org": "biptestid0",
                "project_code": "xmtestcode001-1",
                "project_name": "xmtestname001-1",
                "pk_eps": "99",
                "pk_projectclass": "99",
                "pk_parentpro": "xmbiptestid0001",
                "project_sh_name":"xmtestnamejc001",
                 "project_ot_name":"曾用名",
                "plan_start_date": "2024-10-10",
                "plan_finish_date": "2024-11-11",
                "actu_start_date": "2024-10-10",
                "actu_finish_date": "2024-11-11",
                "planduration": "32",
                "pk_duty_dept": "testid1212-1",
                "pk_duty_org": "biptestid0",
                "memo": "备注",
                "upload_flag": "N",
                "bodyvos": {
                    "item": [
                        {
                            "pk_parti_org": "biptestid1",
                            "memo": "备注"
                      
                       }
                    ]
                }
            }
        }
    }
}
  ```

### 1.4返回示例
#### 1.4.1成功
```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "project_1734005441810.xml",
            "billtype": "project",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "xmbiptestid0001",
                    "filename": "project_1734005441810.xml",
                    "resultdescription": "单据  xmbiptestid0001  开始处理...\r\n单据  xmbiptestid0001  处理完毕!\r\n",
                    "resultcode": "1",
                    "content": "1001ZZ100000000EA2YF"
                }
            ],
            "successful": "Y"
        }
    },
    "code": "1000000000",
    "message": null,
    "errorStack": null
}
```
#### 1.4.2失败
```

{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "project_1734005425366.xml",
            "billtype": "project",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "xmbiptestid0001",
                    "filename": "project_1734005425366.xml",
                    "resultdescription": "单据  xmbiptestid0001  开始处理...\r\n单据  xmbiptestid0001  处理错误:业务插件处理错误：插件类=nc.bs.bd.pfxx.plugin.ProjectDocPfxxPlugin,异常信息:未找到父项目null对应项目\r\n",
                    "resultcode": "-32000",
                    "content": null
                }
            ],
            "successful": "N"
        }
    },
    "code": "1000000000",
    "message": null,
    "errorStack": null
}
```


# 2删除项目 

### 2.1请求类型 
POST
### 2.2请求地址
 http://IP:port/nccloud/api/uapbd/projectmanage/project/deleteProjectByBipId
### 2.3请求参数说明

#### 2.3.1参数说明
  ```
{
  "bipid": "旗舰版项目id",
  "pk_org": "旗舰版组织id"
}
  ```
#### 2.3.2请求示例
  ```
{
  "bipid": "xmbiptestid0002",
  "pk_org": "biptestid0"
}
  ```

### 2.4返回示例
#### 2.4.1成功
```
{
    "success": true,
    "data": "success",
    "code": "1000000000",
    "message": null,
    "errorStack": null
}
```
#### 2.4.2失败
```
{
    "success": false,
    "data": null,
    "code": "-1",
    "message": "根据主键无法查询到对应的项目类型",
    "errorStack": null
}
```
