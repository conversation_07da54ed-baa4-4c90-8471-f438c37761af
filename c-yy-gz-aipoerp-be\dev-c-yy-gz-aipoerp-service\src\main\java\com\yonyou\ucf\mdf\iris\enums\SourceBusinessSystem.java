package com.yonyou.ucf.mdf.iris.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SourceBusinessSystem {
    PAY_ORDER("10","znbzbx","/yonbip/znbz/rbsm/api/bill/common/getFileManageTree","费用管理"),
    GENERAL_EXPENSE_ACCOUNT("14","yonbip-fi-earapbill","/yonbip/EFI/payment/download/url","应付管理");
    private String source;
    private String domain;
    private String openApiUrl;
    private String description;

    public static SourceBusinessSystem getSourceBusinessSystem(String source) {
        for (SourceBusinessSystem value : SourceBusinessSystem.values()) {
            if (value.getSource().equals(source)) {
                return value;
            }
        }
        return null;
    }
}
