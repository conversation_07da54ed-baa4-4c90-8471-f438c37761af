package com.yonyou.aipierp.dto.ncapi.enterBankAcc;

import com.alibaba.fastjson.JSONObject;

public class EnterBankCurrencyAcc extends JSONObject {
    /**
     * "CNY"（币种 两边系统编码需要维护一致 CNY人民币,EUR欧元,GBP英镑,HKD港币,JPY日元,USD美元,MOP	澳门币,TWD	新台币）,必输
     */
    public String PK_CURRTYPE = "pk_currtype";
    /**
     * "11111-01"（编码）,必输
     */
    public String CODE = "code";
    /**
     * "人民币户"（名称）,必输
     */
    public String NAME = "name";
    /**
     * "2"账号类型（0=活期,1=定期,2=通知,4=保证金户）,必输
     */
    public String ACCTYPE = "acctype";
    /**
     * "N"（协定）,
     */
    public String ISCONCERTED = "isconcerted";
    /**
     * "********.88"（协定金额）
     */
    public String CONCERTEDMNY = "concertedmny";
    /**
     * "0"（冻结状态 0=正常,1=冻结,2=部分冻结,3=销户）,必输
     */
    public String FRONZENSTATE = "fronzenstate";
    /**
     * 1000000.00"（冻结金额）,
     */
    public String FRONZENMNY = "fronzenmny";
    /**
     * "2011-01-01 14:18:11"（冻结日期）,
     */
    public String FROZENDATE = "frozendate";
    /**
     * "2011-02-02 14:18:11"（解冻日期）,
     */
    public String DEFROZENDATE = "defrozendate";
    /**
     * "1500000.00"（透支额度）,
     */
    public String OVERDRAFTMNY = "overdraftmny";
    /**
     * "1"（透支控制方式 0=控制,1=提示,2=不控制）,必输
     */
    public String OVERDRAFTTYPE = "overdrafttype";
    /**
     * （付款范围 0=不限制,1=全局内,2=集团内）,必输
     */
    public String PAYAREA = "payarea";
    /**
     * （交易账户 ）
     */
    public String ISTRADE = "istrade";
    /**
     * "isdefault":"Y"(默认)
     */
    public String ISDEFAULT = "isdefault";


    public void setPk_currtype(String pk_currtype) {
        this.put(PK_CURRTYPE, pk_currtype);
    }

    public void setCode(String code) {
        this.put(CODE, code);
    }

    public void setName(String name) {
        this.put(NAME, name);
    }

    public void setAcctype(String acctype) {
        this.put(ACCTYPE, acctype);
    }

    public void setIsconcerted(String isconcerted) {
        this.put(ISCONCERTED, isconcerted);
    }

    public void setConcertedmny(String concertedmny) {
        this.put(CONCERTEDMNY, concertedmny);
    }

    public void setFronzenstate(String fronzenstate) {
        this.put(FRONZENSTATE, fronzenstate);
    }

    public void setFronzenmny(String fronzenmny) {
        this.put(FRONZENMNY, fronzenmny);
    }

    public void setFrozendate(String frozendate) {
        this.put(FROZENDATE, frozendate);
    }

    public void setDefrozendate(String defrozendate) {
        this.put(DEFROZENDATE, defrozendate);
    }

    public void setOverdraftmny(String overdraftmny) {
        this.put(OVERDRAFTMNY, overdraftmny);
    }

    public void setOverdrafttype(String overdrafttype) {
        this.put(OVERDRAFTTYPE, overdrafttype);
    }

    public void setPayarea(String payarea) {
        this.put(PAYAREA, payarea);
    }

    public void setIstrade(String istrade) {
        this.put(ISTRADE, istrade);
    }

    public void setIsdefault(String isdefault) {
        this.put(ISDEFAULT, isdefault);
    }
}
