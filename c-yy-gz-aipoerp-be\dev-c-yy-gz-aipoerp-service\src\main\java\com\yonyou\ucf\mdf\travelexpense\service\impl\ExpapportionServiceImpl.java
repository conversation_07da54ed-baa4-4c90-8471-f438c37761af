package com.yonyou.ucf.mdf.travelexpense.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.base.EntityStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ucf.mdf.travelexpense.service.IExpapportionService;
import com.yonyou.ucf.mdf.travelexpense.service.IStaffQryService;

/**
 * <AUTHOR>
 *
 *         2025年3月24日
 */
@Service
public class ExpapportionServiceImpl implements IExpapportionService {

	@Autowired
	private IStaffQryService staffQryService;

	/**
	 * 先按人员多少，分摊部门所占比例。最后再统一按部门合计，重新计算分摊比例
	 */
	@Override
	public List<BizObject> generateExpapportions(List<BizObject> expinvoicedetails) {
		if (CollectionUtils.isEmpty(expinvoicedetails)) {
			return Collections.emptyList();
		}

		BizObject expensebillb = expinvoicedetails.remove(expinvoicedetails.size() - 1); // 取出最后一条报销明细行

		BizObject allData = new BizObject();// 直接构造虚拟数据
//      allData.set("dept_id", expensebillb.get("vhandledeptid"));
//      allData.set("dept_id_name", expensebillb.get("vhandledeptid_name"));
//      allData.set("org_id", expensebillb.get("chandleorg"));
//      allData.set("org_id_name", expensebillb.get("chandleorg_name"));
		allData.set("dept_id", expensebillb.get("vfinacedeptid"));
		allData.set("dept_id_name", expensebillb.get("vfinacedeptid_name"));
		allData.set("org_id", expensebillb.get("cfinaceorg"));
		allData.set("org_id_name", expensebillb.get("cfinaceorg_name"));
		BizObject bizObj = new BizObject();
		bizObj.set("allData", allData);

		List<BizObject> result = Lists.newArrayList();
		for (BizObject bizObject : expinvoicedetails) {
			List<BizObject> extend3List = bizObject.getBizObjects("TravelExpInvoiceDetailVO_extend3List",
					BizObject.class); // 账单明细多选人员

			if (CollectionUtils.isEmpty(extend3List)) {
				extend3List = Lists.newArrayList();
				extend3List.add(bizObj);
			} else {
				BizObject has = extend3List.stream()
						.filter(v -> v.getString("extend3").equals(expensebillb.getString("pk_handlepsn"))).findFirst()
						.orElse(null);
				if (has == null) {
					extend3List.add(bizObj);
				}
			}

			List<String> staffIds = extend3List.stream().filter(v -> {
				BizObject data = v.getBizObject("allData", BizObject.class);
				if (data == null) { // 保存后修改的数据不存在allData
					return true;
				}
				return false;
			}).map(v -> {
				return v.getString("extend3");
			}).collect(Collectors.toList());

			Map<String, Map<String, Object>> staffMap = staffQryService.queryStaffByIds(staffIds);

			Map<String, List<BizObject>> extend3Group = extend3List.stream().map(v -> {
				if (v.get("allData") == null) {
					setFields(v, staffMap);
					return v;
				}
				return v.getBizObject("allData", BizObject.class);
			}).collect(Collectors.groupingBy(v -> {
				if (v.getString("dept_id") == null) {
					return "";
				}
				return v.getString("dept_id");
			})); // 按部门人员分组
			BigDecimal total = new BigDecimal(extend3List.size()); // 总人数
			Iterator<String> it = extend3Group.keySet().iterator();
			BigDecimal napportmnyTotal = BigDecimal.ZERO; // 合计含税金额（用于处理尾差）
			BigDecimal napporttaxmnyTotal = BigDecimal.ZERO; // 合计含税额（用于处理尾差）
			BigDecimal rateNumTotal = BigDecimal.ZERO; // 分摊比例（用于处理尾差）
			while (it.hasNext()) {
				String key = it.next();
				// 每个部门生成一条分摊数据
				List<BizObject> values = extend3Group.get(key);
				BigDecimal num = new BigDecimal(values.size()); // 该部门人数
				BigDecimal rateNum = null; // 分摊比例
				if (!it.hasNext()) {
					rateNum = new BigDecimal(1).subtract(rateNumTotal);
				} else {
					rateNum = num.divide(total, 4, RoundingMode.HALF_UP); // 分摊比例
					rateNumTotal = rateNumTotal.add(rateNum);
				}
				BizObject value = values.get(0);

				BizObject expapportion = new BizObject();
				expapportion.setEntityStatus(EntityStatus.Insert);
				expapportion.set("hasDefaultInit", true);
				expapportion.set("vfinacedeptid", key);
				expapportion.set("vfinacedeptid_name", value.get("dept_id_name"));
				expapportion.set("cfinaceorg", value.get("org_id"));
				expapportion.set("cfinaceorg_name", value.get("org_id_name"));
				expapportion.set("caccountorg", value.get("org_id"));
				expapportion.set("caccountorg_name", value.get("org_id_name"));
				expapportion.set("pk_busimemo", bizObject.get("pk_busimemo"));
				expapportion.set("pk_busimemo_name", bizObject.get("pk_busimemo_name"));
				expapportion.set("vcurrency", bizObject.get("vinvoicecurrency"));
				expapportion.set("vcurrency_name", bizObject.get("vinvoicecurrency_name"));
				expapportion.set("vcurrency_moneyDigit", bizObject.get("vinvoicecurrency_moneyDigit"));
				expapportion.set("vcurrency_currTypeSign", bizObject.get("vinvoicecurrency_currTypeSign"));
				expapportion.set("vnatcurrency", bizObject.get("vnatcurrency"));
				expapportion.set("vnatcurrency_name", bizObject.get("vnatcurrency_name"));
				expapportion.set("vnatcurrency_moneyDigit", bizObject.get("vnatcurrency_moneyDigit"));
				expapportion.set("vnatcurrency_currTypeSign", bizObject.get("vnatcurrency_currTypeSign"));
				expapportion.set("vnatexchratetype", bizObject.get("vnatexchratetype"));
				expapportion.set("vnatexchratetype_digit", bizObject.get("vnatexchratetype_digit"));
				expapportion.set("dnatexchratedate", bizObject.get("dnatexchratedate"));
				expapportion.set("nnatbaseexchrate", bizObject.get("nnatbaseexchrate"));
				expapportion.set("nnatexchrate", bizObject.get("nnatexchrate"));

				/** 计算分摊金额 ***********************/
				// 含税金额
				BigDecimal napportmny = null;
				BigDecimal nexpmny = bizObject.getBigDecimal("nexpmny"); // 可报销金额
				if (nexpmny != null) {
					if (!it.hasNext()) { // 判断当前是否是最后一条，如果是最后一条，则处理尾差问题
						napportmny = nexpmny.subtract(napportmnyTotal);
					} else {
						napportmny = nexpmny.multiply(rateNum).setScale(2, RoundingMode.HALF_UP);
						napportmnyTotal = napportmnyTotal.add(napportmny);
					}
				}

				// 税额
				BigDecimal napporttaxmny = null;
				BigDecimal ndeducttaxmny = bizObject.getBigDecimal("ndeducttaxmny"); // 可抵扣税额
				if (ndeducttaxmny != null) {
					if (!it.hasNext()) {
						napporttaxmny = ndeducttaxmny.subtract(napporttaxmnyTotal);
					} else {
						napporttaxmny = ndeducttaxmny.multiply(rateNum).setScale(2, RoundingMode.HALF_UP);
						napporttaxmnyTotal = napporttaxmnyTotal.add(napporttaxmny);
					}
				}

				// 不含税金额
				BigDecimal napportnotaxmny = null;
				if (napportmny != null && ndeducttaxmny != null) { // 如果可抵税金额不为空，则取不含税金额。否则取含税金额
					napportnotaxmny = napportmny.subtract(napporttaxmny); // 含税金额-税额
				} else {
					napportnotaxmny = napportmny;
				}
				// 代扣税税额
				BigDecimal nwhtax_amount = BigDecimal.ZERO;
				// 预提核销额
				BigDecimal nwithholdingcavmny = BigDecimal.ZERO;
				// 待摊含税金额
				BigDecimal ndeferredmny = napportmny;
				// 待摊不含税金额
				BigDecimal ndeferreduntaxmny = napportnotaxmny;

				// 含税金额-本币
				BigDecimal nnatapportmny = napportmny;
				// 税额-本币
				BigDecimal nnatapporttaxmny = napporttaxmny;
				// 不含税金额-本币
				BigDecimal nnatapportnotaxmny = napportnotaxmny;
				// 代扣税税额-本币
				BigDecimal nnatwhtax_amount = nwhtax_amount;
				// 预提核销额-本币
				BigDecimal nnatwithholdingcavmny = nwithholdingcavmny;
				// 待摊金额-本币
				BigDecimal nnatdeferredmny = ndeferredmny;
				// 待摊不含税金额-本币
				BigDecimal nnatdeferreduntaxmny = ndeferreduntaxmny;

				// 分摊比例
				BigDecimal napportrate = rateNum.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP); // 乘以100，换算成百分比

				expapportion.set("napportmny", napportmny); // 含税金额
				expapportion.set("napporttaxmny", napporttaxmny); // 税额（可抵扣税额）
				expapportion.set("napportnotaxmny", napportnotaxmny); // 不含税金额
				expapportion.set("nwhtax_amount", nwhtax_amount); // 代扣税税额
				expapportion.set("nwithholdingcavmny", nwithholdingcavmny); // 预提核销额
				expapportion.set("ndeferredmny", ndeferredmny); // 待摊含税金额
				expapportion.set("ndeferreduntaxmny", ndeferreduntaxmny); // 待摊不含税金额

				expapportion.set("nnatapportmny", nnatapportmny); // 含税金额-本币
				expapportion.set("nnatapporttaxmny", nnatapporttaxmny); // 税额-本币
				expapportion.set("nnatapportnotaxmny", nnatapportnotaxmny); // 不含税金额-本币
				expapportion.set("nnatwhtax_amount", nnatwhtax_amount); // 代扣税税额-本币
				expapportion.set("nnatwithholdingcavmny", nnatwithholdingcavmny); // 预提核销额-本币
				expapportion.set("nnatdeferredmny", nnatdeferredmny); // 待摊金额-本币
				expapportion.set("nnatdeferreduntaxmny", nnatdeferreduntaxmny); // 待摊不含税金额-本币

				expapportion.set("napportrate", napportrate); // 分摊比例

				result.add(expapportion);
			}

		}

		result = expapportionByDept(result);

		return result;
	}

	/**
	 * 设置必要字段
	 * 
	 * @param v
	 * @param staffMap
	 */
	private void setFields(BizObject v, Map<String, Map<String, Object>> staffMap) {
		if (MapUtils.isEmpty(staffMap)) {
			return;
		}
		Map<String, Object> staff = staffMap.get(v.getString("extend3"));
		if (MapUtils.isEmpty(staff)) {
			return;
		}
		Object staffJob = staff.get("staffJob");
		if (staffJob == null) {
			return;
		}
		JSONArray jobs = JSONArray.parseArray(JSONUtil.toJson(staffJob));
		for (Iterator<Object> iterator = jobs.iterator(); iterator.hasNext();) {
			JSONObject job = (JSONObject) iterator.next();
			if (job.getBooleanValue("isMainJob")) {
				v.set("dept_id", job.getString("deptId"));
				v.set("dept_id_name", job.getString("deptName"));
				v.set("org_id", job.getString("orgId"));
				v.set("org_id_name", job.getString("orgName"));
				return;
			}
		}
	}

	/**
	 * 再按部门分组，重新计算分摊比例
	 *
	 * @param result
	 * @return
	 */
	private List<BizObject> expapportionByDept(List<BizObject> result) {
		if (CollectionUtils.isEmpty(result)) {
			return result;
		}

		// 按部门和费用项目重新合计分摊
		Map<String, List<BizObject>> resultGroup = result.stream().collect(Collectors.groupingBy(b -> {
			return b.getString("vfinacedeptid") + b.getString("pk_busimemo");
		}));

		List<BizObject> finalResult = Lists.newArrayList();

		BigDecimal napportmnyTotal = BigDecimal.ZERO; // 合计含税金额
		Iterator<String> it = resultGroup.keySet().iterator();
		while (it.hasNext()) {
			String key = it.next();
			List<BizObject> values = resultGroup.get(key);
			// 合计含税金额
			BigDecimal napportmnySum = BigDecimal.ZERO;
			// 合计税额
			BigDecimal napporttaxmnySum = BigDecimal.ZERO;
			// 合计不含税金额
			BigDecimal napportnotaxmnySum = BigDecimal.ZERO;
			for (BizObject bizObject : values) {
				BigDecimal napportmny = bizObject.getBigDecimal("napportmny"); // 含税金额
				if (napportmny != null) {
					napportmnySum = napportmnySum.add(napportmny);
				}
				BigDecimal napporttaxmny = bizObject.getBigDecimal("napporttaxmny"); // 税额
				if (napporttaxmny != null) {
					napporttaxmnySum = napporttaxmnySum.add(napporttaxmny);
				}
			}
			napportnotaxmnySum = napportmnySum.subtract(napporttaxmnySum);

			napportmnyTotal = napportmnyTotal.add(napportmnySum);

			// 代扣税税额
			BigDecimal nwhtax_amount = BigDecimal.ZERO;
			// 预提核销额
			BigDecimal nwithholdingcavmny = BigDecimal.ZERO;
			// 待摊含税金额
			BigDecimal ndeferredmny = napportmnySum;
			// 待摊不含税金额
			BigDecimal ndeferreduntaxmny = napportnotaxmnySum;

			// 含税金额-本币
			BigDecimal nnatapportmny = napportmnySum;
			// 税额-本币
			BigDecimal nnatapporttaxmny = napporttaxmnySum;
			// 不含税金额-本币
			BigDecimal nnatapportnotaxmny = napportnotaxmnySum;
			// 代扣税税额-本币
			BigDecimal nnatwhtax_amount = nwhtax_amount;
			// 预提核销额-本币
			BigDecimal nnatwithholdingcavmny = nwithholdingcavmny;
			// 待摊金额-本币
			BigDecimal nnatdeferredmny = ndeferredmny;
			// 待摊不含税金额-本币
			BigDecimal nnatdeferreduntaxmny = ndeferreduntaxmny;

//			BizObject expapportion = new BizObject();
			String biz = JSONUtil.toJson(values.get(0));
			BizObject expapportion = JSONUtil.toBean(biz, BizObject.class);
			BeanUtils.copyProperties(values.get(0), expapportion);

			expapportion.set("napportmny", napportmnySum); // 含税金额
			expapportion.set("napporttaxmny", napporttaxmnySum); // 税额（可抵扣税额）
			expapportion.set("napportnotaxmny", napportnotaxmnySum); // 不含税金额
			expapportion.set("nwhtax_amount", nwhtax_amount); // 代扣税税额
			expapportion.set("nwithholdingcavmny", nwithholdingcavmny); // 预提核销额
			expapportion.set("ndeferredmny", ndeferredmny); // 待摊含税金额
			expapportion.set("ndeferreduntaxmny", ndeferreduntaxmny); // 待摊不含税金额

			expapportion.set("nnatapportmny", nnatapportmny); // 含税金额-本币
			expapportion.set("nnatapporttaxmny", nnatapporttaxmny); // 税额-本币
			expapportion.set("nnatapportnotaxmny", nnatapportnotaxmny); // 不含税金额-本币
			expapportion.set("nnatwhtax_amount", nnatwhtax_amount); // 代扣税税额-本币
			expapportion.set("nnatwithholdingcavmny", nnatwithholdingcavmny); // 预提核销额-本币
			expapportion.set("nnatdeferredmny", nnatdeferredmny); // 待摊金额-本币
			expapportion.set("nnatdeferreduntaxmny", nnatdeferreduntaxmny); // 待摊不含税金额-本币

//			expapportion.set("napportrate", napportrate); // 分摊比例

			finalResult.add(expapportion);

		}

		// 重新计算分摊比例
		BigDecimal rateNumTotal = BigDecimal.ZERO; // 分摊比例（用于处理尾差）
		for (int i = 0; i < finalResult.size(); i++) {
			BizObject biz = finalResult.get(i);
			if (i == finalResult.size() - 1) {
				BigDecimal rateNum = new BigDecimal(1).subtract(rateNumTotal);
				BigDecimal napportrate = rateNum.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP); // 乘以100，换算成百分比
				biz.set("napportrate", napportrate);
			} else {
				BigDecimal napportmny = biz.getBigDecimal("napportmny");
				if (napportmny != null && napportmnyTotal.compareTo(BigDecimal.ZERO) > 0) {
					BigDecimal rateNum = napportmny.divide(napportmnyTotal, 4, RoundingMode.HALF_UP); // 分摊比例
					rateNumTotal = rateNumTotal.add(rateNum);
					BigDecimal napportrate = rateNum.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP); // 乘以100，换算成百分比
					biz.set("napportrate", napportrate);
				}
			}
		}

		// 按费用项目排序
		finalResult = finalResult.stream().sorted((v1, v2) -> {
			if (v1.getString("pk_busimemo") == null) {
				return 1;
			} else if (v2.getString("pk_busimemo") == null) {
				return -1;
			}
			return v1.getString("pk_busimemo").compareTo(v2.getString("pk_busimemo"));
		}).collect(Collectors.toList());

		return finalResult;
	}

}
