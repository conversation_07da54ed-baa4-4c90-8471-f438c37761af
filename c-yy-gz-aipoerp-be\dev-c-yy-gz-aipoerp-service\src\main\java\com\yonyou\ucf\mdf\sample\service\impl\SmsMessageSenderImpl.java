package com.yonyou.ucf.mdf.sample.service.impl;

import java.util.concurrent.CompletableFuture;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.google.gson.JsonObject;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.sample.service.ISmsMessageSender;
import com.yonyou.ypd.bill.utils.YpdAppContextUtil;

import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;

/**
 * AccessKeyId:LTAI5tJP2aYaFwC6F7jzGtJU
 * AccessKeySecret:****************************** 模板类型 验证码 模板名称
 * ALI_YONYOU_MOBILECODE 模板CODE SMS_230662335 模板内容
 * 验证码为:${code},您正在进行身份验证，该验证码5分钟内有效，请勿泄漏于他人。 地址http://dysmsapi.aliyuncs.com
 */
@Service
@Slf4j
public class SmsMessageSenderImpl implements ISmsMessageSender {

	@Override
	public boolean send(String mobile, String msg, String[] codes, String templateCode) throws Exception {
		if (StringUtils.isBlank(mobile)) {
			throw new BusinessException("手机号不能为空");
		}
		if (StringUtils.isEmpty(codes[0])) {
			throw new BusinessException("验证码不能为空");
		}
		String signName = YpdAppContextUtil.getEnvSerivce().getProperty("ALIBABA_CLOUD_SIGNNAME", "云上艾珀");
		templateCode = YpdAppContextUtil.getEnvSerivce().getProperty("ALIBABA_CLOUD_TEMPLATECODE", "SMS_230662335");
		log.error("发送短信signName：{}", signName);
		log.error("发送短信templateCode：{}", templateCode);
		return alibabaCloudSmsSend(mobile, signName, codes, templateCode);
	}

	private boolean alibabaCloudSmsSend(String mobile, String signName, String[] codes, String templateCode)
			throws Exception {
		// HttpClient Configuration
		/*
		 * HttpClient httpClient = new ApacheAsyncHttpClientBuilder()
		 * .connectionTimeout(Duration.ofSeconds(10)) // Set the connection timeout
		 * time, the default is 10 seconds .responseTimeout(Duration.ofSeconds(10)) //
		 * Set the response timeout time, the default is 20 seconds .maxConnections(128)
		 * // Set the connection pool size .maxIdleTimeOut(Duration.ofSeconds(50)) //
		 * Set the connection pool timeout, the default is 30 seconds // Configure the
		 * proxy .proxy(new ProxyOptions(ProxyOptions.Type.HTTP, new
		 * InetSocketAddress("<your-proxy-hostname>", 9001))
		 * .setCredentials("<your-proxy-username>", "<your-proxy-password>")) // If it
		 * is an https connection, you need to configure the certificate, or ignore the
		 * certificate(.ignoreSSL(true)) .x509TrustManagers(new X509TrustManager[]{})
		 * .keyManagers(new KeyManager[]{}) .ignoreSSL(false) .build();
		 */
		String accessKey = YpdAppContextUtil.getEnvSerivce().getProperty("ALIBABA_CLOUD_ACCESS_KEY_ID",
				"LTAI5tJP2aYaFwC6F7jzGtJU");
		String accessKeySecret = YpdAppContextUtil.getEnvSerivce().getProperty("ALIBABA_CLOUD_ACCESS_KEY_SECRET",
				"******************************");
		log.error("发送短信AK：{}", accessKey);
		log.error("发送短信KS：{}", accessKeySecret);
		// Configure Credentials authentication information, including ak, secret, token
		StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
				// Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and
				// ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
				.accessKeyId(accessKey).accessKeySecret(accessKeySecret)
				// .securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS
				// token
				.build());

		// Configure the Client
		AsyncClient client = AsyncClient.builder().region("cn-hangzhou") // Region ID
				// .httpClient(httpClient) // Use the configured HttpClient, otherwise use the
				// default HttpClient (Apache HttpClient)
				.credentialsProvider(provider)
				// .serviceConfiguration(Configuration.create()) // Service-level configuration
				// Client-level configuration rewrite, can set Endpoint, Http request
				// parameters, etc.
				.overrideConfiguration(ClientOverrideConfiguration.create()
						// Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
						.setEndpointOverride("dysmsapi.aliyuncs.com")
				// .setConnectTimeout(Duration.ofSeconds(30))
				).build();

		// Parameter settings for API request
		// 生成随机的4位验证码
		// String code = ValidateCodeUtils.generateValidateCode(4).toString();
		JsonObject json = new JsonObject();
		json.addProperty("code", codes[0]);
		SendSmsRequest sendSmsRequest = SendSmsRequest.builder().signName(signName).templateCode(templateCode)
				.phoneNumbers(mobile).templateParam(json.toString())
				// Request-level configuration rewrite, can set Http request parameters, etc.
				// .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new
				// HttpHeaders()))
				.build();
		log.error("发送短信code：{}", codes[0]);
		// Asynchronously get the return value of the API request
		CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
		// Synchronously get the return value of the API request
		SendSmsResponse resp = response.get();
		log.error("发送短信resp：{}", JSONObject.toJSONString(resp));
		if (resp.getStatusCode().intValue() != 200) {
			log.error("阿里云短信认证服务网络异常，statusCode：" + resp.getStatusCode().toString());
			return false;
		}
		String restCode = resp.getBody().getCode();
		if (!restCode.equals("OK")) {
			log.error("阿里云短信认证服务出错，错误码：" + restCode + "，错误原因：" + resp.getBody().getMessage());
			return false;
		}
		// Asynchronous processing of return values
		/*
		 * response.thenAccept(resp -> { System.out.println(new Gson().toJson(resp));
		 * }).exceptionally(throwable -> { // Handling exceptions
		 * System.out.println(throwable.getMessage()); return null; });
		 */
		// Finally, close the client
		client.close();
		return true;
	}
}
