package com.yonyou.ucf.mdf.aipo.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class JSON {

    private static final Logger logger = LoggerFactory.getLogger(JSON.class);

    private static final ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    public static <T> T readValue(String jsonString, Class<T> type) {
        T t = null;
        try {
            t = mapper.readValue(jsonString, type);
        } catch (JsonProcessingException e) {
            e.getOriginalMessage();
        }
        return t;
    }

    public static <T> T readValue(String jsonString, TypeReference<T> typeReference) {
        T t = null;
        try {
            t = mapper.readValue(jsonString, typeReference);
        } catch (JsonProcessingException e) {
            logger.error("json字串转换对象失败", e);
        }
        return t;
    }

    /**
     * 对换序列化成 json String
     *
     * @param object 对象
     * @return 序列化后的 json String
     */
    public static String writeValueAsString(Object object) {
        String str = null;
        try {
            str = mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error("对象序列化json串失败", e);
        }
        return str;
    }

    /**
     * 对换序列化成 json String
     *
     * @param object 对象
     * @return 序列化后的 json String
     */
    public static String writeValueAsStringWithPrettyWriter(Object object) {
        String str = null;
        try {
            str = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error("对象序列化json串失败", e);
        }
        return str;
    }

    /**
     * @param object
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> T readValue(Object object, TypeReference<T> typeReference) {
        T t = null;
        try {
            t = mapper.convertValue(object, typeReference);
        } catch (Exception e) {
            logger.error("对象转换失败", e);
        }
        return t;
    }

    /**
     * 列表对象转换
     *
     * @param objects
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> List<T> readListValue(List<Object> objects, TypeReference<List<T>> typeReference) {
        List<T> ts = null;
        try {
            ts = mapper.convertValue(objects, typeReference);
        } catch (Exception e) {
            logger.error("列表对象转换失败", e);
        }
        return ts;
    }

    public static Map<String, Object> read2Map(String jsonStr) {
        return readValue(jsonStr, new TypeReference<Map<String, Object>>() {
        });
    }

    public static List<Map<String, Object>> read2ListMap(String jsonStr) {
        List<Map<String, Object>> maps = readValue(jsonStr, new TypeReference<List<Map<String, Object>>>() {
        });
        return maps;
    }
}
