# 新增（修改）部门 
根据ufinterface.bill.id 来判断，已经存在走修改，不存在走新增
### 1.1请求类型 
POST
### 1.2请求地址
 http://IP:port/nccloud/api/riaorg/org/dept/addDeptEx

### 1.3请求参数说明
  参数说明
```
{
    "ufinterface": {
        "account": "账套编码（sm_busicenter的值）", ？？？
        "billtype": "dept（固定值）",
        "groupcode": "0001（集团编码）",  ？？？
        "isexchange": "Y（一般默认Y）",
        "replace": "Y（一般默认Y）",
        "sender": "default（一般默认default）",
        "bill": {
            "billhead": {
                "code": "dept1552"（部门编码）,
                "name": "dept1552"（部门名称）,
                "shortname": "dept1552"（简称）,
                "mnecode": "dept1552"（助记码）,         ？？？
                "pk_group": "0001"（集团编码）,           ？？？
                "pk_fatherorg":"testid1209"(上级部门id（旗舰版），为空就是没上级),
                "pk_org": "0000102"（旗舰版业务单元id）,
                "depttype": "0"（0=普通部门,1=虚拟部门）,
                "enablestate": "2"（1=未启用,2=已启用,3=已停用）,
                "createdate": "2011-04-28 11:40:51"（创建时间）,
                "hrcanceled": "N"（HR撤销标志）,
                "displayorder": "1"（显示顺序）,
                "address": ""（地址）,
                "orgtype13": "Y"（报表）,
                "orgtype17": "N"（预算）,
                "tel": "********"（联系电话）,
                "memo":"备注"
            },
             "id":"testid1209-1"(bip旗舰版的部门id)
        }
    }
}
```
 > 请求参数JSON示例
```
{
    "ufinterface": {
        "account": "0001",
        "billtype": "dept",
        "groupcode": "0001",
        "isexchange": "Y",
        "replace": "Y",
        "sender": "default",
        "bill": {
            "billhead": {
                "code": "dept1552",
                "name": "dept1552",
                "shortname": "pfxx_dept1",
                "mnecode": "pfxx_dept1",
                "pk_group": "0001",
                "pk_org": "0000102",
                "depttype": "0",
                "enablestate": "2",
                "createdate": "2011-04-28 11:40:51",
                "hrcanceled": "N",
                "displayorder": "1",
                "address": "",
                "orgtype13": "Y",
                "orgtype17": "N",
                "tel": "********",
                "memo":"备注"
            },
             "id":"testid1209-1"
        }
    }
}
```

### 1.4返回示例
#### 1.4.1成功
```
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "dept_1733730694617.xml",
            "billtype": "dept",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "testid1209",
                    "filename": "dept_1733730694617.xml",
                    "resultdescription": "单据  testid1209  开始处理...\r\n单据  testid1209  处理完毕!\r\n",
                    "resultcode": "1",
                    "content": "1001ZZ1000000003GYGY"
                }
            ],
            "successful": "Y"
        }
    },
    "code": "1000000000",
    "message": null,
    "errorStack": null
}
```

#### 1.4.2失败

```
{
    "success": false,
    "data": null,
    "code": "1000000001",
    "message": "未找到code110000102对应组织",
    "errorStack": "堆栈"
}
 -- 或 data.ufinterface.sendresult.resultcode不是1
{
    "success": true,
    "data": {
        "ufinterface": {
            "filename": "dept_1733731039131.xml",
            "billtype": "dept",
            "sender": "default",
            "replace": "Y",
            "roottag": "sendresult",
            "isexchange": "Y",
            "sendresult": [
                {
                    "bdocid": "testid1209000",
                    "filename": "dept_1733731039131.xml",
                    "resultdescription": "单据  testid1209000  开始处理...\r\n单据  testid1209000  处理错误:业务插件处理错误：插件类=nc.bs.org.DeptPfxxPlugin,异常信息:pk_org0001 没有找到该参数\r\n",
                    "resultcode": "-32000",
                    "content": null
                }
            ],
            "successful": "N"
        }
    },
    "code": "1000000000",
    "message": null,
    "errorStack": null
}
```


# 删除部门 
### 1.1请求类型 
POST
### 1.2请求地址
 http://IP:port/nccloud/api/riaorg/org/dept/deleteByBipId

### 1.3请求参数说明
  参数说明
  ```
  {
  "id": "testid1209-5"(旗舰版id),
  "version": "1"（固定值）
 }

  ```

### 1.4返回示例
#### 1.4.1成功
```
{
  "success": true,
  "data": true,
  "code": "1000000000",
  "message": null,
  "errorStack": null,
  "pageInfo": null
}
```
#### 1.4.2失败
```
{
    "success": false,
    "data": null,
    "code": "1000000001",
    "message": "dept15575组织的业务职能已经被选择，不可删除.",
    "errorStack": "堆栈"
}
```