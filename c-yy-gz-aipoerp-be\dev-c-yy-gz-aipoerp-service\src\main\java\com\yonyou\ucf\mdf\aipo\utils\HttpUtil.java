package com.yonyou.ucf.mdf.aipo.utils;

import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import com.alibaba.fastjson.JSON;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * http发送工具
 *
 * <AUTHOR>
 * @since 2022/10/27 11:17
 */
public class HttpUtil {

    /**
     * 发动get请求
     *
     * @param url
     * @return
     */
    public static String get(String url) throws Exception {
        return execute(url, HttpMethod.GET, null, null);
    }

    /**
     * 发动get请求
     *
     * @param url
     * @return
     */
    public static <T> T get(String url, Class<T> resType) throws Exception {
        return JSON.parseObject(get(url), resType);
    }

    /**
     * 发送post请求
     *
     * @param url
     * @param data
     * @return
     */
    public static String post(String url, String data) throws Exception {
        return execute(url, HttpMethod.POST, null, data);
    }

    /**
     * 发送post请求
     *
     * @param url
     * @param data
     * @return
     */
    public static <T> T post(String url, String data, Class<T> resType) throws Exception {
        return JSON.parseObject(post(url, data), resType);
    }

    /**
     * 发送put请求
     *
     * @param url
     * @param data
     * @return
     */
    public static String put(String url, String data) throws Exception {
        return execute(url, HttpMethod.PUT, null, data);
    }

    /**
     * 发送put请求
     *
     * @param url
     * @param data
     * @return
     */
    public static <T> T put(String url, String data, Class<T> resType) throws Exception {
        return JSON.parseObject(put(url, data), resType);
    }

    /**
     * 响应是否成功
     *
     * @return
     */
    public static String execute(String reqUrl, HttpMethod method, Map<String, String> headMap, String reqJson) throws Exception {
        //if(reqUrl!=null && reqUrl.startsWith("https://yonyoubip-test.aipocloud.com")){
        //    reqUrl = reqUrl.replaceAll("https","http");
        //}
        // 定义 URL对象
        URL url = new URL(reqUrl);
        // 获取 URL 链接
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        // 设置连接超时时间, 值必须大于0，设置为0表示不超时 单位为“毫秒”
        conn.setConnectTimeout(30000);
        // 设置读超时时间, 值必须大于0，设置为0表示不超时 单位毫秒
        conn.setReadTimeout(300000);
        // 设置请求类型
        conn.setRequestMethod(method.name());
        // 设置请求类型为 application/json
        conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        // 设置可接受的数据类型
        conn.setRequestProperty("Accept", "*/*");
        // 设置保持长链接
        conn.setRequestProperty("Connection", "Keep-Alive");
        // 模拟浏览器发送
        conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
       // conn.setRequestProperty("Cookie", "wpsqing_autoLoginV1=1; csrf=2KRTM7JGrSdij6iRYzzNjSiP5KwCT8EG; yht_default_country=86; yht_default_login_type=normal; _WorkbenchCross_=Ultraman; org.springframework.web.servlet.i18n.CookieLocaleResolver.LOCALE=zh-CN; _yht_code_uuid=d0d7e232-820d-4106-863c-90666bce7ea6; at=73fcd9ed-d749-43a8-acbe-d558b30d73b4; JSESSIONID=AFD0322D609F4E5C429625D9778CF246; yht_username_diwork=ST-909-GYMAoBdEd9HE6BoCB4sw-online__db497f4d-02b1-4606-9412-6c74113466b8; yht_usertoken_diwork=fKKwsxZe%2F33nXpAI1cAl%2FI7HB3DYLDNCLZe%2BBn1%2Bz%2F7bmzXGY0ThnhRaEZUMewfLqJMVpVlnXql1ja%2B%2FSMf50w%3D%3D; yht_access_token=bttamVuTlVpYWx4QW5NL2FabGpwWWd5ZEVZV3QxM3JwU3ROVGdXajg3aHhuTUtUS0wvRXRPaG5pOGVXQTFybVFsY19fYzNwb2MueW9ueW91LmNvbQ..__9284bc6b77e9a6ef2f5c3d3af2f64f9b_1715868953832TGTGdccore0iuap-apcom-workbench1bfc29a4YT; multilingualFlag=false; timezone=UTC+08:00; language=001; locale=zh_CN; orgId=; defaultOrg=; tenantid=iuajyldr; theme=; languages=001001; newArch=true; sysid=diwork; a00=6PlsRakEXOnH6Pg-H_rmyMi0hV-_zb3ktr8J3VqWCxhpdWFqeWxkcmAzMzYxNDUzMTgzMTkzNDI0YGl1YWp5bGRyYGRiNDk3ZjRkLTAyYjEtNDYwNi05NDEyLTZjNzQxMTM0NjZiOGAxYGBlNmI4YjhlNTg2OWJlNjk2ODdgYGAxNzY3NTc1MDA0NzE5MzQ5NzYyYGZhbHNlYGAxNzE1ODY4OTUzODM1YHltc3NlczpkZTY4N2U2MjIwNThiYWVmOGUyNjM3OGIxN2RkYzZmNmBkaXdvcmtg; a10=MDE4NTMzODM4NDYwNDQ5NTM4MzU; n_f_f=true; wb_at=LMjvmvj9RF3n4c7cvA7s4n54qrvjnmkhmd; UBA_LAST_EID=pxdhlw9c462d; jDiowrkTokenMock=bttamVuTlVpYWx4QW5NL2FabGpwWWd5ZEVZV3QxM3JwU3ROVGdXajg3aHhuTUtUS0wvRXRPaG5pOGVXQTFybVFsY19fYzNwb2MueW9ueW91LmNvbQ..__9284bc6b77e9a6ef2f5c3d3af2f64f9b_1715868953832TGTGdccore0iuap-apcom-workbench1bfc29a4YT; a10=MTMzNTEyOTUxNDYwNDU1NTY4NzE; XSRF-TOKEN=AX_4FWS3PRCLS1AFFTKJQPR8JFSG!222720");

        if(headMap != null) {
            for(Map.Entry<String, String> entry : headMap.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
        }

        if("https".equals(url.getProtocol())) {
            SSLContext sc = SSLContext.getInstance("TLSv1.2");
            sc.init(null, null, null);
            ((HttpsURLConnection) conn).setSSLSocketFactory(sc.getSocketFactory());
        }
        if(StringUtils.isNotEmpty(reqJson)) {
            conn.setDoOutput(true);
            try(OutputStream os = conn.getOutputStream()) {
                // 往输出流中写数据
                os.write(reqJson.getBytes(StandardCharsets.UTF_8));
                os.flush();
            }
        }

        int respCode = conn.getResponseCode();
        try(InputStream in = respCode < 200 || respCode >= 300 ? conn.getErrorStream() : conn.getInputStream(); ByteArrayOutputStream byteOut = new ByteArrayOutputStream()) {
            // 开始读取数据
            byte[] buffer = new byte[256];
            int len;
            while((len = in.read(buffer)) > 0) {
                byteOut.write(buffer, 0, len);
            }
            return new String(byteOut.toByteArray(), StandardCharsets.UTF_8);
        }
    }

    public static String post(String url, String reqJson, Map<String, String> headerMap) throws Exception {
        return execute(url, HttpMethod.POST, headerMap, reqJson);
    }


    /**
     * 请求方法枚举类
     */
    public enum HttpMethod {
        GET, HEAD, POST, PUT, DELETE, CONNECT, OPTIONS, TRACE, PATCH
    }
}