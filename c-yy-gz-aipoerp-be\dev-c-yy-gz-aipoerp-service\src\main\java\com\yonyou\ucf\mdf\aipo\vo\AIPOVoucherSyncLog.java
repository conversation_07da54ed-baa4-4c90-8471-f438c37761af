package com.yonyou.ucf.mdf.aipo.vo;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.base.ILogicDelete;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 艾珀凭证同步日志
 * @Date 2025-05-21 14:42:18
 * @since 2023/11/28
 **/
@YMSEntity(name = "AIPOERPCREATE.AIPOERPCREATE.AIPOVoucherSyncLog", domain = "c-yy-gz-aipoerp")
public class AIPOVoucherSyncLog extends SuperDO implements ILogicDelete {
	public static final String ENTITY_NAME = "AIPOERPCREATE.AIPOERPCREATE.AIPOVoucherSyncLog";
	public static final String EVENTID = "eventid";
	public static final String BILLID = "billid";
	public static final String SYNCDATA = "syncdata";
	public static final String ACCCODE = "acccode";
	public static final String ACCNAME = "accname";
	public static final String VOUCHERPERIOD = "voucherperiod";
	public static final String BILLCODE = "billcode";
	public static final String VOUCHERTYPECODE = "vouchertypecode";
	public static final String SYNCTYPE = "synctype";
	public static final String ISSUCCESS = "issuccess";
	public static final String ERRORMSG = "errormsg";
	public static final String SENDMSG = "sendmsg";
	public static final String RETMSG = "retmsg";
	public static final String ENTERTIME = "entertime";
	public static final String DEF1 = "def1";
	public static final String DEF2 = "def2";
	public static final String DEF3 = "def3";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String DR = "dr";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 事件id */
	private String eventid;
	/* 凭证id */
	private String billid;
	/* 同步类型 */
	private String syncdata;
	/* 账簿编码 */
	private String acccode;
	/* 账簿名称 */
	private String accname;
	/* 凭证期间 */
	private String voucherperiod;
	/* 凭证号 */
	private String billcode;
	/* 凭证类别 */
	private String vouchertypecode;
	/* 同步名称 */
	private String synctype;
	/* 是否成功 */
	private String issuccess;
	/* 错误原因 */
	private String errormsg;
	/* 发送数据 */
	private String sendmsg;
	/* 返回数据 */
	private String retmsg;
	/* 进入时间 */
	private String entertime;
	/* 自定义项1 */
	private String def1;
	/* 自定义项2 */
	private String def2;
	/* 自定义项3 */
	private String def3;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* 逻辑删除 */
	private Short dr;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setEventid(String eventid) {
		this.eventid = eventid;
	}

	public void setBillid(String billid) {
		this.billid = billid;
	}

	public void setSyncdata(String syncdata) {
		this.syncdata = syncdata;
	}

	public void setAcccode(String acccode) {
		this.acccode = acccode;
	}

	public void setAccname(String accname) {
		this.accname = accname;
	}

	public void setVoucherperiod(String voucherperiod) {
		this.voucherperiod = voucherperiod;
	}

	public void setBillcode(String billcode) {
		this.billcode = billcode;
	}

	public void setVouchertypecode(String vouchertypecode) {
		this.vouchertypecode = vouchertypecode;
	}

	public void setSynctype(String synctype) {
		this.synctype = synctype;
	}

	public void setIssuccess(String issuccess) {
		this.issuccess = issuccess;
	}

	public void setErrormsg(String errormsg) {
		this.errormsg = errormsg;
	}

	public void setSendmsg(String sendmsg) {
		this.sendmsg = sendmsg;
	}

	public void setRetmsg(String retmsg) {
		this.retmsg = retmsg;
	}

	public void setEntertime(String entertime) {
		this.entertime = entertime;
	}

	public void setDef1(String def1) {
		this.def1 = def1;
	}

	public void setDef2(String def2) {
		this.def2 = def2;
	}

	public void setDef3(String def3) {
		this.def3 = def3;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setDr(Short dr) {
		this.dr = dr;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getEventid() {
		return eventid;
	}

	public String getBillid() {
		return billid;
	}

	public String getSyncdata() {
		return syncdata;
	}

	public String getAcccode() {
		return acccode;
	}

	public String getAccname() {
		return accname;
	}

	public String getVoucherperiod() {
		return voucherperiod;
	}

	public String getBillcode() {
		return billcode;
	}

	public String getVouchertypecode() {
		return vouchertypecode;
	}

	public String getSynctype() {
		return synctype;
	}

	public String getIssuccess() {
		return issuccess;
	}

	public String getErrormsg() {
		return errormsg;
	}

	public String getSendmsg() {
		return sendmsg;
	}

	public String getRetmsg() {
		return retmsg;
	}

	public String getEntertime() {
		return entertime;
	}

	public String getDef1() {
		return def1;
	}

	public String getDef2() {
		return def2;
	}

	public String getDef3() {
		return def3;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public Short getDr() {
		return dr;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
