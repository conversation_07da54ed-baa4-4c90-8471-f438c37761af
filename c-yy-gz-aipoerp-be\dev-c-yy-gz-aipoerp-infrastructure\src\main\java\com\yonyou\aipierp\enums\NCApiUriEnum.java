package com.yonyou.aipierp.enums;

public enum NCApiUriEnum {
    /**
     * 获取token
     */
    TOKEN_GET("/nccloud/opm/accesstoken"),
    /**
     * 部门保存
     */
    DEPT_SAVE("/nccloud/api/riaorg/org/dept/addDeptEx"),
    /**
     * 部门删除
     */
    DEPT_DELETE("/nccloud/api/riaorg/org/dept/deleteByBipId"),
    /**
     * 员工保存
     */
    PSN_SAVE("/nccloud/api/uapbd/psndocmanage/psndoc/add"),
    /**
     * 员工删除
     */
    PSN_DELETE("/nccloud/api/uapbd/psndocmanage/psndoc/deletePsndocbyBipId"),
    /**
     * 银行账户档案保存
     */
    BANK_SAVE("/nccloud/api/uapbd/bankmanage/bankaccbas/addEx"),
    /**
     * 删除银行账户档案
     */
    BANK_DELETE("/nccloud/api/uapbd/bankmanage/bankaccbas/deleteBankaccbasByBipId"),
    /**
     * 项目保存
     */
    PROJECT_SAVE("/nccloud/api/uapbd/projectmanage/project/addEx"),
    /**
     * 项目删除
     */
    PROJECT_DELETE("/nccloud/api/uapbd/projectmanage/project/deleteProjectByBipId"),
    /**
     * 自定义档案 保存
     */
    DEFARCHIVE_SAVE("/nccloud/api/riamm/defdocmanage/defdoc/add"),
    /**
     * 自定义档案 删除
     */
    DEFARCHIVE_DELETE("/nccloud/api/riamm/defdocmanage/defdoc/deleteDefdocbyBipId"),
    /**
     * 自定义档案 保存
     */
    CUSTOMER_SAVE("/nccloud/api/uapbd/customermanage/customer/addCustomerPf"),
    /**
     * 自定义档案 删除
     */
    CUSTOMER_DELETE("/nccloud/api/uapbd/customermanage/customer/deleteCustbyBipId");

    private String uri;

    NCApiUriEnum(String uri) {
        this.uri = uri;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }
}
