package com.yonyou.ucf.mdf.equip.bill.plugin;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.imeta.biz.base.BizContext;
import org.imeta.core.model.Entity;
import org.imeta.core.model.Property;
import org.imeta.core.selector.node.BaseNode;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.imeta.spring.support.cache.RedisManager;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.equip.model.EquipCardDataContrastDetailVo;
import com.yonyou.ucf.mdf.equip.model.EquipCardDataVo;
import com.yonyou.ucf.mdf.equip.model.UserDefines;
import com.yonyou.ucf.mdf.utils.FileUtil;
import com.yonyou.ypd.bill.annotation.BillPlugin;
import com.yonyou.ypd.bill.context.YpdBillContext;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.plugin.AbstractBillPlugin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @date 2025/2/19 11:23
 * @DESCRIPTION 资产卡片数据批量导入贵州插件保存前
 */
@BillPlugin(busiObj = "EquipCardDataVo")
public class EquipDataImportSaveBeforePlugin extends AbstractBillPlugin {
    private static final String CACHE_KEY ="EquipCardDataVoChangeFiledKey";
    @Autowired
    private IBillQueryRepository repository;

    @Autowired
    private RedisManager redisManager;

    @Override
    public void beforeSave(YpdBillContext billContext) throws Exception {
        String origCode = billContext.getBaseBillContext().getActionInfo().getOrigCode();
        if ("import".equals(origCode)) {
            EquipCardDataVo equipCardDataVo = (EquipCardDataVo)billContext.getBillDO();
            List<Map<String, Object>> originDataList = getEquipOriginData(equipCardDataVo.getEquip_code());
            if (CollUtil.isEmpty(originDataList)) {
                //在原数据中未找到 盘亏
                equipCardDataVo.setComparison_result("SHORTAGE");
                return;
            }
            Map<String, Map<String, Object>> equipCodeMap = originDataList.stream()
                    .collect(Collectors.toMap(k -> k.get("equip_code").toString(), Function.identity()));
            if (CollUtil.isEmpty(equipCodeMap)) {
                return;
            }
            Map<String, JSONObject> changeProjectMap = getChangeProjectMap();
            String equipCode = equipCardDataVo.getEquip_code();
            // 获取资产卡片中原有的数据
            Map<String, Object> originObj = equipCodeMap.get(equipCode);
            List<EquipCardDataContrastDetailVo> detailVos = new ArrayList<>();
            changeProjectMap.forEach((chk, chv) -> {
                //原始值
                Object originValue;
                //新值
                Object newValue;
                //原始值名称
                Object originValueName;
                //新值名称
                Object newValueName;
                Boolean isCharacter = chv.getBoolean("isCharacter");
                //是否为特征字段
                boolean character = isCharacter != null && isCharacter;
                Boolean isDateTime = chv.getBoolean("isDateTime");
                if (character) {
                    Object mapObj = originObj.get("userDefines");
                    JSONObject originJson = JSONObject.parseObject(JSONObject.toJSONString(mapObj));
                    //自定义项原始值
                    originValue = originJson.get(chk);
                    if (isDateTime !=null  && isDateTime && originValue != null) {
                        originValue = DateUtil.format(new Date(Long.parseLong(originValue.toString())), "yyyy-MM-dd");
                    }
                    //自定义项新值
                    UserDefines userDefines = equipCardDataVo.getUserDefines();
                    newValue = userDefines.get(chk);
                    String fullName = chv.getString("fullName");
                    String domain = chv.getString("domain");
                    //如果是参照字段
                    if (StringUtils.isNotBlank(fullName) && StringUtils.isNotBlank(domain)) {
                        String nameKey = chk + "_name";
                        //原始值名称默认等于原始值
                        originValueName = originJson.get(nameKey);
                        //新值名称默认等于新值
                        newValueName = userDefines.get(nameKey);
                    } else {
                        //原始值名称默认等于原始值
                        originValueName = originValue;
                        //新值名称默认等于新值
                        newValueName = newValue;
                    }
                } else {
                    //原始值
                    originValue = originObj.get(chk);
                    //新值
                    newValue = equipCardDataVo.getAttrValue(chk);
                    //原始值名称默认等于原始值
                    originValueName = originValue;
                    //新值名称默认等于新值
                    newValueName = newValue;
                }
                if (newValue != null && !newValue.equals(originValue)) {
                    EquipCardDataContrastDetailVo detailVo = new EquipCardDataContrastDetailVo();
                    detailVo.setEquip_code(equipCode);
                    detailVo.setChange_field(chk);
                    detailVo.setChange_field_name(chv.getString("name"));
                    String fullName = chv.getString("fullName");
                    String domain = chv.getString("domain");
                    //如果是参照字段
                    if (StringUtils.isNotBlank(fullName) && StringUtils.isNotBlank(domain)) {
                        if (originValueName == null || originValueName.equals(originValue)) {
                            originValueName = getReferName(fullName, domain, originValue);
                        }
                        if (newValueName == null || newValueName.equals(newValue)) {
                            newValueName = getReferName(fullName, domain, newValue);
                        }
                    }
                    //改变前
                    detailVo.setChange_before_value(objToStr(originValue));
                    detailVo.setChange_before_value_name(objToStr(originValueName));
                    //改变后
                    detailVo.setChange_after_value(objToStr(newValue));
                    detailVo.setChange_after_value_name(objToStr(newValueName));
                    detailVos.add(detailVo);
                }
            });
            if (CollUtil.isNotEmpty(detailVos)) {
                //与原数据存在差异即为不符
                equipCardDataVo.setComparison_result("MISMATCH");
                equipCardDataVo.setIs_exit_diff("Y");
                equipCardDataVo.setEquipCardDataContrastDetailVoList(detailVos);
            }
        }
    }

    private String objToStr(Object obj) {
        if (obj == null) return null;
        String name = obj.toString();
        if(JSONUtil.isTypeJSON(name)) {
			String jsonStr = name.replaceAll("(\\w+)=([^,}]+)", "\"$1\":\"$2\"").replace(":\"null\"", ":null");
			return JSONObject.parseObject(jsonStr).getString("zh_CN");
        }
        return name;
    }

    //TODO 根据编码查询
    private List<Map<String, Object>> getEquipOriginData(String equipCode) {
        QuerySchema querySchema = QuerySchema.create().addSelect("*").addCondition(QueryConditionGroup.and(
                QueryCondition.name("equip_code").eq(equipCode), QueryCondition.name("dr").eq(0)));
        return repository.queryMapBySchema("aim.equip.EquipHeadVO", querySchema, "ucf-amc-ambd");
    }

    @SneakyThrows
    protected Map<String, JSONObject> getChangeProjectMap() {
        Map<String, JSONObject> jsonObjectMap = redisManager.getObject(CACHE_KEY);
        if (CollUtil.isNotEmpty(jsonObjectMap)) {
            return jsonObjectMap;
        }
        String changeStr = FileUtil.readText(getClass().getResourceAsStream("/res/changeproject.json"), "utf-8");
        JSONArray jsonArray = JSONObject.parseArray(changeStr);
        Entity entity = BizContext.getMetaRepository().entity("aim.equip.EquipHeadVODefines");
        List<BaseNode> characterNodes = entity.selectAttribute("/attributes[@isCharacter=true]");
        if (CollUtil.isNotEmpty(characterNodes)) {
            characterNodes.forEach(baseNode -> {
                Property property = ((Property) baseNode.getNodeObject());
                JSONObject jsonObject = new JSONObject();
                int index = jsonArray.size() + 1;
                jsonObject.put("isCharacter", true);
                jsonObject.put("code", "ZCBD0" + index);
                jsonObject.put("name", property.title());
                jsonObject.put("fullName", property.getTypeUri());
                if (StringUtils.isNotBlank((CharSequence) property.get("refType"))) {
                    String refType = property.get("refType");
                    String domain = refType.split("\\.")[0];
                    jsonObject.put("domain", domain);
                }
                jsonObject.put("index", index);
                jsonObject.put("id", property.name());
                jsonObject.put("isDateTime", property.getType().isDateTime());
                jsonArray.add(jsonObject);
            });
        }
        Map<String, JSONObject> objectMap=null;
        if (CollUtil.isNotEmpty(jsonArray)) {
            objectMap = jsonArray.stream().collect(Collectors.toMap(k -> JSONObject.parseObject(JSONObject.toJSONString(k)).getString("id"),
                    v -> JSONObject.parseObject(JSONObject.toJSONString(v))));
            redisManager.setObject(CACHE_KEY, objectMap, 10 * 60);
        }
        return objectMap;
    }

    //TODO 获取参照name
    private Object getReferName(String fullName, String domain, Object id) {
        QuerySchema querySchema = QuerySchema.create().addSelect("*")
                .addCondition(QueryConditionGroup.and(QueryCondition.name("id").eq(id)));
        List<Map<String, Object>> maps = repository.queryMapBySchema(fullName, querySchema, domain);
        if (CollUtil.isNotEmpty(maps)) {
            return maps.stream().map(m -> {
                String name = m.keySet().stream().filter(f -> "name".equals(f) || f.endsWith("_name")).findFirst().orElse("");
                if (StringUtils.isBlank(name)) {
                    return "";
                }
                return m.get(name);
            }).findFirst().orElse("");
        }
        return null;
    }
}