package com.yonyou.ucf.mdf.equip.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.ucf.mdf.equip.model.AssetNameLibraryVo;
import com.yonyou.ucf.mdf.equip.service.AssetNameLibraryService;
import com.yonyou.ucf.mdf.product.util.ObjectUtil;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.basic.entity.convert.EntityConverter;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/28 11:13
 * @DESCRIPTION 资产名称库接口实现类
 */
@Slf4j
@Service
public class AssetNameLibraryServiceImpl implements AssetNameLibraryService {
    @Autowired
    private IBillRepository repository;

    @Autowired
    private IBillCommonRepository commonRepository;

    @Autowired
    private IBillQueryRepository billQueryRepository;

    @Autowired
    private EntityConverter entityConverter;

    @SneakyThrows
    @Override
    public Integer pullFromMaterial() {
        String querySql = "select ma.`name`,ma.`code`,ma.orgId,ma.manageClass materialClassify,'MATERIAL' source," +
                "now() enablets, 1 enable, ma.id productId" +
                " from iuap_apdoc_coredoc.product ma join iuap_apdoc_coredoc.productdetail de on" +
                " ma.id=de.productId where (ma.realProductAttributeType=4 or de.iValueManageType=1) and de.stopstatus=0" +
                " and ma.ytenant_id='"+InvocationInfoProxy.getTenantid()+"'";
        List<AssetNameLibraryVo> libraryVos = repository.queryForDTOList(querySql, null, AssetNameLibraryVo.class);
        if (CollUtil.isEmpty(libraryVos)) return null;
        Object[] productIds = libraryVos.stream().map(AssetNameLibraryVo::getProductId).toArray();
        List<Map<String, Object>> queryResultList = getAssentNameListByProductId(productIds);
        if (CollUtil.isNotEmpty(queryResultList)) {
            List<Object> resProductIds = queryResultList.stream().map(map -> map.get("productId")).collect(Collectors.toList());
            libraryVos = libraryVos.stream().filter(f -> !resProductIds.contains(f.getProductId())).collect(Collectors.toList());
        }
        executeInsert(libraryVos);
        return libraryVos.size();
    }

    @SneakyThrows
    private void executeInsert(List<AssetNameLibraryVo> libraryVos) {
        if (CollUtil.isNotEmpty(libraryVos)) {
            List<IBillDO> iBillDOS = libraryVos.stream().map(ma -> {
                ma.set_status(ActionEnum.INSERT.getValueInt());
                ((IBillDO)ma).setAttrValue("hasBillCode", ma.getCode());
                return (IBillDO)ma;
            }).collect(Collectors.toList());
            commonRepository.commonSaveBill(iBillDOS, "AssetNameLibraryVo");
        }
    }

    @Override
    public List<Map<String, Object>> getAssentNameListByProductId(Object[] productIds) {
        QuerySchema schema = QuerySchema.create()
                .addSelect("id,name,code,materialClassify,productId")
                .addCondition(QueryConditionGroup.and(QueryCondition.name("productId").in(productIds)));
        return billQueryRepository.queryMapBySchema("ZCMCK.ZCMCK.AssetNameLibraryVo", schema);
    }

    @Async
    @SneakyThrows
    @Override
    public void updateAssentName(Map<String, Object> assentNameMap, BizObject bizObject) {
        Object productCode = bizObject.get("code");
        Object assentNameCode = assentNameMap.get("code");
        if (!productCode.equals(assentNameCode)) {
            //物料编码
            assentNameMap.put("code", productCode);
        }
        Object proManageClass = bizObject.get("manageClass");
        Object assentNameClass = assentNameMap.get("materialClassify");
        if (!proManageClass.equals(assentNameClass)) {
            //物料分类
            assentNameMap.put("materialClassify", proManageClass);
        }
        Object assentNameName = assentNameMap.get("name");
        BizObject productNameObj = bizObject.getBizObject("name", BizObject.class);
        Object productName = productNameObj.get("zh_CN");
        if (productName != null && !productName.equals(assentNameName)) {
            //物料名称
            assentNameMap.put("name", productName);
        }
        boolean updateBoolean = !(productName!=null && productName.equals(assentNameName))
                || !proManageClass.equals(assentNameClass) || !productCode.equals(assentNameCode);

        if (updateBoolean) {
            assentNameMap.put("_status", ActionEnum.UPDATE.getValueInt());
            List<Map<String, Object>> updateList = new ArrayList<>();
            updateList.add(assentNameMap);
            List<IBillDO> iBillDOS = entityConverter.convertObjToDO("ZCMCK.ZCMCK.AssetNameLibraryVo", updateList);
            commonRepository.commonSaveBill(iBillDOS, "AssetNameLibraryVo");
        }
    }

    @Override
    public void enable(List<String> ids) {
        stopOrEnable(ids, "1");
    }

    @Override
    public void stop(List<String> ids) {
        stopOrEnable(ids, "0");
    }

    @Async
    @Override
    public void addAssentName(List<Map<String, Object>> productList) {
        if (CollUtil.isEmpty(productList)) return;
        log.error("资产名称库新增来源物料档案=={}", productList);
        List<AssetNameLibraryVo> libraryVos = productList.stream().map(map -> {
            AssetNameLibraryVo assetNameLibraryVo = new AssetNameLibraryVo();
            assetNameLibraryVo.setProductId(ObjectUtil.toStr(map.get("id")));
            assetNameLibraryVo.setCode(ObjectUtil.toStr(map.get("code")));
            assetNameLibraryVo.setName(ObjectUtil.toStr(map.get("name")));
            assetNameLibraryVo.setMaterialClassify(ObjectUtil.toLon(map.get("manageClass")));
            assetNameLibraryVo.setOrgId(ObjectUtil.toStr(map.get("orgId")));
            assetNameLibraryVo.setSource("MATERIAL");
            assetNameLibraryVo.setEnable("1");
            assetNameLibraryVo.setEnablets(new Date());
            return assetNameLibraryVo;
        }).collect(Collectors.toList());
        executeInsert(libraryVos);
    }

    @SneakyThrows
    private void stopOrEnable(List<String> ids, String enable) {
        List<Map<String, Object>> libraryVos = ids.stream().map(id -> {
            Map<String, Object> libraryVo = new HashMap<>();
            libraryVo.put("_status",ActionEnum.UPDATE.getValueInt());
            libraryVo.put("id",id);
            libraryVo.put("enable",enable);
            if ("0".equals(enable)) {
                libraryVo.put("enablets",new Date());
            }
            if ("1".equals(enable)) {
                libraryVo.put("disablets",new Date());
            }
            return libraryVo;
        }).collect(Collectors.toList());
        List<IBillDO> iBillDOS = entityConverter.convertObjToDO("ZCMCK.ZCMCK.AssetNameLibraryVo", libraryVos);
        commonRepository.commonSaveBill(iBillDOS, "AssetNameLibraryVo");
    }
}
