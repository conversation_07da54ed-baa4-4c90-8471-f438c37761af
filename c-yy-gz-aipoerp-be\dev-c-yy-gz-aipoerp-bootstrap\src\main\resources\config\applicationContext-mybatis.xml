<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.3.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.3.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.3.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.3.xsd"
       default-autowire="byName">

    <!-- 配置sqlSessionFactory,mybatis-spring.jar -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean" primary="true">
        <!--数据库连接池 -->
        <property name="dataSource" ref="mainDataSource"/>
        <property name="configLocation" value="classpath:mybatis/mdd-mybatis-config.xml"/>
        <!--加载mybatis的全局配置文件 -->
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/**/*Mapper.xml</value>
            </list>
        </property>
        <property name="transactionFactory">
            <bean class="org.mybatis.spring.transaction.SpringManagedTransactionFactory" />
        </property>
    </bean>
    <bean id="sqlSession" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg index="0" ref="sqlSessionFactory"/>
    </bean>
    <bean id="mainDataSource" class="com.zaxxer.hikari.HikariDataSource"></bean>
</beans>
