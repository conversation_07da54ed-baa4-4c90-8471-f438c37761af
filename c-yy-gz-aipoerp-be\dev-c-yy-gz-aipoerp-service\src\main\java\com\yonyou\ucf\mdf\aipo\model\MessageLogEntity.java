package com.yonyou.ucf.mdf.aipo.model;

public class MessageLogEntity {

    private String sendSystem;

    private String userName;

    private String userCode;

    private String userMobile;

    private String taskId;

    private String title;

    private String businessKey;

    private String murl;

    private String weburl;

    private String oaJson;

    private String oaResult;

    private String logError;

    private String messageId;

    private String messageType;

    private String approveSource;

    private String eventId;

    private String tenantId;

    private String contentMsg;

    private String isSuccess;

    private String pushSystem;

    private String pushUrl;

    private String msgTsLong;
    private String registerCode;


    private String TaskType;

    public MessageLogEntity() {
    }

    public MessageLogEntity(String sendSystem, String userName, String userCode, String userMobile, String taskId, String title, String businessKey, String murl, String weburl, String oaJson, String oaResult, String logError, String messageId, String messageType, String approveSource, String eventId, String tenantId) {
        this.sendSystem = sendSystem;
        this.userName = userName;
        this.userCode = userCode;
        this.userMobile = userMobile;
        this.taskId = taskId;
        this.title = title;
        this.businessKey = businessKey;
        this.murl = murl;
        this.weburl = weburl;
        this.oaJson = oaJson;
        this.oaResult = oaResult;
        this.logError = logError;
        this.messageId = messageId;
        this.messageType = messageType;
    }

    public String getSendSystem() {
        return sendSystem;
    }

    public void setSendSystem(String sendSystem) {
        this.sendSystem = sendSystem;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getMurl() {
        return murl;
    }

    public void setMurl(String murl) {
        this.murl = murl;
    }

    public String getWeburl() {
        return weburl;
    }

    public void setWeburl(String weburl) {
        this.weburl = weburl;
    }

    public String getOaJson() {
        return oaJson;
    }

    public void setOaJson(String oaJson) {
        this.oaJson = oaJson;
    }

    public String getOaResult() {
        return oaResult;
    }

    public void setOaResult(String oaResult) {
        this.oaResult = oaResult;
    }

    public String getLogError() {
        return logError;
    }

    public void setLogError(String logError) {
        this.logError = logError;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getApproveSource() {
        return approveSource;
    }

    public void setApproveSource(String approveSource) {
        this.approveSource = approveSource;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getContentMsg() {
        return contentMsg;
    }

    public void setContentMsg(String contentMsg) {
        this.contentMsg = contentMsg;
    }

    public String getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(String isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getPushSystem() {
        return pushSystem;
    }

    public void setPushSystem(String pushSystem) {
        this.pushSystem = pushSystem;
    }

    public String getPushUrl() {
        return pushUrl;
    }

    public void setPushUrl(String pushUrl) {
        this.pushUrl = pushUrl;
    }

    public String getMsgTsLong() {
        return msgTsLong;
    }

    public void setMsgTsLong(String msgTsLong) {
        this.msgTsLong = msgTsLong;
    }

    public String getRegisterCode() {
        return registerCode;
    }

    public void setRegisterCode(String registerCode) {
        this.registerCode = registerCode;
    }

    public String getTaskType() {
        return TaskType;
    }

    public void setTaskType(String taskType) {
        TaskType = taskType;
    }

    @Override
    public String toString() {
        return "MessageLogEntity{" +
                "sendSystem='" + sendSystem + '\'' +
                ", userName='" + userName + '\'' +
                ", userCode='" + userCode + '\'' +
                ", userMobile='" + userMobile + '\'' +
                ", taskId='" + taskId + '\'' +
                ", title='" + title + '\'' +
                ", businessKey='" + businessKey + '\'' +
                ", murl='" + murl + '\'' +
                ", weburl='" + weburl + '\'' +
                ", oaJson='" + oaJson + '\'' +
                ", oaResult='" + oaResult + '\'' +
                ", logError='" + logError + '\'' +
                ", messageId='" + messageId + '\'' +
                ", messageType='" + messageType + '\'' +
                '}';
    }
}
