package com.yonyou.ucf.mdf.aipo.model;

/**
 * 推送事件定义
 */
public enum EventType {

    /**
     * 检查回调地址有效性
     */
    CHECK_URL,

    /**
     * UNKNOWN
     **/
    UNKNOWN,

    /**
     * 添加待办消息
     */
    TODO_CENTER_ADD_TODO,

    /**
     * 更新待办消息
     */
    TODO_CENTER_UPDATE_TODO,
    FIEPUB_ACCOUNT_BOOK_ADD_AFTER,
    FIEPUB_ACCOUNT_BOOK_UPDATE_AFTER,
    FIEPUB_ACCOUNT_BOOK_DELETE_AFTER,
    /**
	 * 凭证保存
	 */
    GL_VOUCHER_EVENT_ADD_AFTER,

    /**
     * 凭证审核
     */
    GL_VOUCHER_EVENT_AUDIT_AFTER,
    /**
     * 凭证取消审核
     */
    GL_VOUCHER_EVENT_UNAUDIT_AFTER,
    /**
     * 凭证记账
     */
    GL_VOUCHER_EVENT_TALLY_AFTER,
    /**
     * 凭证取消记账
     */
    GL_VOUCHER_EVENT_UNTALLY_AFLTER,
    /**
     * 凭证删除
     */
    GL_VOUCHER_EVENT_DELETE_AFTER,
    /**
     * 凭证修改
     */
    GL_VOUCHER_EVENT_UPDATE_AFTER,
    /**
     * 辅助新增
     */
    FIEPUB_EVENT_DIM_REL_ADD_AFTER,
    /**
     * 辅助更新
     */
    FIEPUB_EVENT_DIM_REL_UPDATE_AFTER,
    /**
     * 辅助删除
     */
    FIEPUB_EVENT_DIM_REL_DELETE_AFTER,

    /**
     * 会计科目新增
     */
    FIEPUB_ACCSUBJECT_ADD_AFTER,

    /**
     * 非末级会计科目新增
     */
    FIEPUB_ACCSUBJECT_NOTLEAF_ADD_AFTER,
    /**
     * 会计科目删除
     */
    FIEPUB_ACCSUBJECT_DELETE_AFTER,
    /**
     * 会计科目更新
     */
    FIEPUB_ACCSUBJECT_EDIT_AFTER,
    /**
     * 科目表新增
     */
    FIEPUB_ACCCHART_ADD_AFTER,
    /**
     * 科目表编辑
     */
    FIEPUB_ACCCHART_EDIT_AFTER,

    /**
     * 科目表删除
     */
    FIEPUB_ACCCHART_DEL_AFTER,

    //=================基础档案事件=====================
    /**
     * 客户档案新增事件
     */
    YXYBASEDOC_AA_MERCHANT_INSERT,
    /**
     * 客户档案修改事件
     */
    YXYBASEDOC_AA_MERCHANT_UPDATE,
    /**
     * 客户档案删除事件
     */
    YXYBASEDOC_AA_MERCHANT_DELETE,
    /**
     * 项目新增事件
     */
    PROJECT_ADD,
    /**
     * 项目修改事件
     */
    PROJECT_UPDATE,
    /**
     * 项目删除事件
     */
    PROJECT_DELETE,
    /**
     * 物料新增事件
     */
    YXYBASEDOC_PC_PRODUCT_INSERT,
    /**
     * 物料修改事件
     */
    YXYBASEDOC_PC_PRODUCT_UPDATE,
    /**
     * 物料删除事件
     */
    YXYBASEDOC_PC_PRODUCT_DELETE,
    /**
     * 自定义档案维护新增事件
     */
    UCFBASEDOC_ADDCUSTDOC_AFTER,
    /**
     * 自定义档案维护删除事件
     */
    UCFBASEDOC_DELETECUSTDOC_AFTER,
    /**
     * 自定义档案维护修改事件
     */
    UCFBASEDOC_UPDATECUSTDOC_AFTER,
    /**
     * 企业银行新增后事件
     */
    UCFBASEDOC_ORGFINBANKACCTADD_AFTER,
    /**
     * 企业银行删除后事件
     */
    UCFBASEDOC_ORGFINBANKACCTDELETE_AFTER,
    /**
     * 企业银行更新后事件
     */
    UCFBASEDOC_ORGFINBANKACCTUPDATE_AFTER,
    /**
     * 供应商新增
     */
    BASEDOC_VENDOR_ADD_AFTER,
    /**
     * 供应商修改
     */
    BASEDOC_VENDOR_UPDATE_AFTER,
    /**
     * 供应商删除
     */
    BASEDOC_VENDOR_DELETE_AFTER,

    //=================基础档案事件=====================

    /**
     * 科目表清YMS缓存
     */
    ACCSUBJECT_CHART_CLEAN_CACHE;

}
