package com.yonyou.ucf.mdf.rbsm.utils;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;

/**
 * <AUTHOR>
 *
 *         2025年3月19日
 */
public class JSONUtil {

	private static final JsonMapper JSON_MAPPER = JsonMapper.alwaysMapper();

	private JSONUtil() {
		throw new IllegalStateException("Utility  class");
	}

	/**
	 * json字符串转java对象
	 * 
	 * @param <T>
	 * @param json
	 * @param clazz
	 * @return
	 */
	public static <T> T toBean(String json, Class<T> clazz) {
		return JSON_MAPPER.fromJson(json, clazz);
	}

	/**
	 * json字符串转java对象
	 * 
	 * @param <T>
	 * @param json
	 * @param typeReference
	 * @return
	 */
	public static <T> T toBean(String json, TypeReference<T> typeReference) {
		return JSON_MAPPER.fromJson(json, typeReference);
	}

	/**
	 * json对象转java对象
	 * 
	 * @param <T>
	 * @param obj
	 * @param clazz
	 * @return
	 */
	public static <T> T toBean(Object obj, Class<T> clazz) {
		String json = JSONObject.toJSONString(obj);
		return JSON_MAPPER.fromJson(json, clazz);
	}

	/**
	 * json对象转java对象
	 * 
	 * @param <T>
	 * @param obj
	 * @param typeReference
	 * @return
	 */
	public static <T> T toBean(Object obj, TypeReference<T> typeReference) {
		String json = JSONObject.toJSONString(obj);
		return JSON_MAPPER.fromJson(json, typeReference);
	}

	/**
	 * java对象转json字符串
	 * 
	 * @param object
	 * @return
	 */
	public static String toJson(Object object) {
		return JSON_MAPPER.toJson(object);
	}

}
