.golden-gate-container {
  /* 全局样式 */
  margin: 0;
  padding: 0;
  height: 100vh;
  font-family: Arial, sans-serif;
  overflow: hidden;
  position: relative;
  background: #ffffff;

  /* 描述文字样式 */
  p {
    font-size: 1.5rem;
    color: #333;
    margin: 0 0 40px 0;
    text-align: center;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    font-weight: 300;
  }

  /* 按钮容器样式 */
  .button-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 80px; /* 新增：下移一个按钮的高度 */
  }

  /* 主按钮样式 - 更新为纯色 */
  .button {
    display: inline-block;
    width: 260px; /* 减小宽度 */
    padding: 10px 20px; /* 减小内边距 */
    background: #4a8bf5; /* 纯色代替渐变 */
    color: white;
    text-decoration: none;
    font-size: 1.7rem; /* 减小字体大小 */
    border-radius: 12px; /* 减小圆角 */
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    font-weight: 600;
    letter-spacing: 1px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    /* 按钮悬停效果 */
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      background: #3a66d6; /* 悬停颜色 */
    }
  }

  /* 页面内容居中 */
  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
    transform: translateY(60px); /* 新增：整体下移一个按钮高度 */
  }

  /* 背景装饰元素 */
  .bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 100, 255, 0.05);
    box-shadow: 0 0 30px rgba(0, 100, 255, 0.1);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    animation: float 15s infinite linear;
    z-index: 0;
  }

  @keyframes float {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    100% {
      transform: translateY(-100vh) rotate(360deg);
    }
  }

  /* 添加光晕效果 */
  .glow {
    position: fixed;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 100, 255, 0.1) 0%, transparent 70%);
    filter: blur(30px);
    z-index: 0;
    animation: glowMove 20s infinite alternate;
  }

  @keyframes glowMove {
    0% {
      transform: translate(20vw, 10vh);
    }
    50% {
      transform: translate(60vw, 50vh);
    }
    100% {
      transform: translate(30vw, 70vh);
    }
  }

  /* Logo背景样式 - 调整位置和大小 */
  .logo-background {
    position: absolute;
    top: calc(30% + 60px);
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150vw;
    height: 150vh;
    max-width: 1000px;
    max-height: 1000px;
    z-index: 0;
    opacity: 0.5;
    pointer-events: none;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: 
        drop-shadow(0 0 20px rgba(0, 100, 255, 0.3))
        brightness(1.2)
        contrast(1.1);
      animation: logo-glow 3s infinite ease-in-out;
    }
  }

  @keyframes logo-glow {
    0% { filter: brightness(1.1) contrast(1.0); }
    50% { filter: brightness(1.3) contrast(1.1); }
    100% { filter: brightness(1.1) contrast(1.0); }
  }
  
  /* 设置按钮样式 */
  .settings-container {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
  }
  
  .settings-icon {
    padding-top: 12px;
    width: 40px;
    height: 40px;
    /*background: #f5f5f5;*/
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    /*box-shadow: 0 2px 10px rgba(0,0,0,0.1);*/
    transition: all 0.3s ease;

    &:hover {
      background: #e0e0e0;
    }

    &::before {
      content: "⋯";
      font-size: 28px; /* 增大三点大小 */
      color: #666;
      line-height: 1;
      transform: translateY(-4px); /* 调整垂直居中 */
      font-weight: bold;
    }
  }
  
  .hidden-button {
    display: none;
    position: absolute;
    top: 50px;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    z-index: 10;

    a {
      display: block;
      padding: 12px 20px;
      color: #333;
      text-decoration: none;
      white-space: nowrap;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f9ff;
      }
    }
  }
  
  .show {
    display: block;
    animation: fadeIn 0.3s ease;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* 左上角logo样式 */
  .top-logo {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    z-index: 10;

    img {
      height: 30px;
      margin-right: 10px;
    }
  }

  .logo-text {
    font-size: 16px;
    color: #333;
    font-weight: 500;
  }
}