package com.yonyou.aipierp.enums;

/**
 * 员工员工身份类型枚举
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public enum StaffIdTypeEnum {

    /**
     * 身份证
     */
    ID_CARD("0000000001", "CN01"),
    /**
     * 护照
     */
    PASSPORT("0000000003", "CN02"),
    /**
     * 回乡许可证
     */
    HOME_RETURN_PERMIT("0000000021", "CN03"),
    /**
     * 外国人永久居留许可
     */
    Foreigner_Permanent_Permit("0000000020", "CN04"),
    /**
     * 香港居民身份证
     */
    HONG_KONG_CARD("0000000018", "HK01"),
    /**
     * 澳门居民身份证
     */
    MACAU_CARD("0000000019", "MO01"),
    /**
     * 台湾身份证
     */
    TAIWAN_CARD("0000000023", "TW01"),
    /**
     * 台胞证
     */
    TAIWAN_RESIDENTS("0000000022", "TW02");

    private String bipIdType;
    private String ncIdType;

    StaffIdTypeEnum(String bipIdType, String ncIdType) {
        this.bipIdType = bipIdType;
        this.ncIdType = ncIdType;
    }

    /**
     * 根据bip身份类型获取nc身份类型
     *
     * @param bipIdType BIP ID 类型
     * @return {@link String }
     */
    public static String getNcIdTypeByBipIdType(String bipIdType) {
        for (StaffIdTypeEnum idTypeEnum : values()) {
            if (idTypeEnum.getBipIdType().equals(bipIdType)) {
                return idTypeEnum.getNcIdType();
            }
        }
        return null;
    }

    public String getBipIdType() {
        return bipIdType;
    }

    public String getNcIdType() {
        return ncIdType;
    }
}
