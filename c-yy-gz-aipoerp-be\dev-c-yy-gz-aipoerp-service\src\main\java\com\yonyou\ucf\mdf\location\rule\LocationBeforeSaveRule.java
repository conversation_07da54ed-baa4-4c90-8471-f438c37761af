package com.yonyou.ucf.mdf.location.rule;

import cn.hutool.core.collection.CollUtil;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/18 10:53
 * @DESCRIPTION 位置保存前
 */
@Slf4j
@Component("locationBeforeSaveRule")
public class LocationBeforeSaveRule implements IYpdCommonRul {
    @Autowired
    private IBillQueryRepository billQueryRepository;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        try {
            List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
            BizObject bizObject = bills.get(0);
            String parent = bizObject.get("parent");
            if (StringUtils.isBlank(parent)) return null;
            QuerySchema schema = QuerySchema.create().addSelect("id,location_global,location_name")
                    .addCondition(QueryConditionGroup.and(QueryCondition.name("id").eq(parent)));
            List<Map<String, Object>> maps = billQueryRepository.queryMapBySchema("ampub.equipbase.LocationVO", schema, "ucf-amc-ambd");
            if (CollUtil.isEmpty(maps)) {
                return null;
            }
            Map<String, Object> map = maps.get(0);
            Object locationGlobalObj = map.get("location_global");
            String locationGlobal = locationGlobalObj == null ? null : locationGlobalObj.toString();
            if (StringUtils.isNotBlank(locationGlobal)) {
                locationGlobal = locationGlobal.replaceAll("/",".");
            }
            Map<String, String> nameMap = bizObject.get("location_name");
            locationGlobal = locationGlobal +"." + nameMap.get("zh_CN");
            log.error("位置信息======={}", locationGlobal);
            bizObject.set("location_global", locationGlobal);
            return new RuleExecuteResult(bizObject);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }
}
