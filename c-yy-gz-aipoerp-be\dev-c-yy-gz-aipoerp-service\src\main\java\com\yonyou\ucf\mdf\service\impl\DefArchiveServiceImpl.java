package com.yonyou.ucf.mdf.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.defArchive.DefArchiveBill;
import com.yonyou.aipierp.dto.ncapi.defArchive.DefArchiveBillHead;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IDefArchiveService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DefArchiveServiceImpl implements IDefArchiveService {

    NCOpenApiConfig ncOpenApiConfig;
    NCOpenApiService ncOpenApiService;
    AIPORepository aipoRepository;


    public DefArchiveServiceImpl(NCOpenApiConfig ncOpenApiConfig,
                               NCOpenApiService ncOpenApiService,
                               AIPORepository aipoRepository) {
        this.ncOpenApiConfig = ncOpenApiConfig;
        this.ncOpenApiService = ncOpenApiService;
        this.aipoRepository = aipoRepository;
    }


    @Override
    public JSONObject pushDefArchiveToNC(JSONObject defArchiveInfo, String archiveTypeCode) {
        UFinterface uFinterface = convertToDefArchUFinterface(defArchiveInfo,archiveTypeCode);
        JSONObject resp = ncOpenApiService.saveDefArchive(uFinterface);
        if (!resp.getBooleanValue("success") ||
                !"1".equals(resp.getJSONObject("data").getJSONObject("ufinterface").getJSONArray("sendresult").getJSONObject(0).getString("resultcode"))) {
            log.error("同步自定义档案信息到高级版失败 saveBody-->{},resp-->{}", uFinterface, resp);
            throw new BusinessException("同步自定义档案信息到高级版失败，错误原因："+ resp);
        }
        return resp;
    }

    @Override
    public JSONObject deleteDefArchiveFromNC(String id) {
        JSONObject deleteBody = new JSONObject();
        deleteBody.put("bipid",id);
        deleteBody.put("version","1");
        JSONObject resp = ncOpenApiService.deleteDefArchive(deleteBody);
        if (!resp.getBooleanValue("success")) {
            log.error("从高级版删除自定义档案信息失败 req-->{},resp-->{}", deleteBody, resp);
            throw new BusinessException("从高级版删除自定义档案信息失败，错误原因："+ resp);
        }
        return resp;
    }

    private UFinterface convertToDefArchUFinterface(JSONObject bipDefArchive, String archiveTypeCode){
        UFinterface uFinterface = UFinterface.init(ncOpenApiConfig.getBiz_center(),"defdoc",ncOpenApiConfig.getGroupcode());

        // 构建billhead
        DefArchiveBillHead defArchiveBillHead = new DefArchiveBillHead();
        defArchiveBillHead.setPk_group(ncOpenApiConfig.getGroupcode());
        defArchiveBillHead.setCode(bipDefArchive.getString("code"));
        defArchiveBillHead.setName(bipDefArchive.getString("name"));
        if (StringUtils.isNotEmpty(bipDefArchive.getString("parent"))){
            defArchiveBillHead.setPid(bipDefArchive.getString("parent"));
        }
        defArchiveBillHead.setPk_defdoclist(archiveTypeCode);
        if ("666666".equals(bipDefArchive.getString("orgid"))){
            defArchiveBillHead.setPk_org(ncOpenApiConfig.getGroupcode());
        }else {
            defArchiveBillHead.setPk_org(bipDefArchive.getString("orgid"));
        }
        defArchiveBillHead.setDef1(bipDefArchive.getString("id"));

        // 构建bill
        DefArchiveBill defArchiveBill = new DefArchiveBill();
        defArchiveBill.setBillhead(defArchiveBillHead);
        defArchiveBill.setId(bipDefArchive.getString("id"));

        uFinterface.setBill(defArchiveBill);

        return uFinterface;
    }
}
