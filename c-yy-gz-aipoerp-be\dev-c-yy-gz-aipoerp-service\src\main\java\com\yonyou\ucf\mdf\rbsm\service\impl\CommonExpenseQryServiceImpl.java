package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.rbsm.service.itf.ICommonExpenseQryService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 *         2025年3月30日
 */
@Service
public class CommonExpenseQryServiceImpl implements ICommonExpenseQryService {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@Override
	public List<Map<String, Object>> queryCommonExpense(QuerySchema schema) {
		List<Map<String, Object>> bills = billQryRepository
				.queryMapBySchema("znbzbx.commonexpensebill.CommonExpenseBillVO", schema, "znbzbx");
		if (CollectionUtils.isEmpty(bills)) {
			return Collections.emptyList();
		}
		return bills;
	}

}
