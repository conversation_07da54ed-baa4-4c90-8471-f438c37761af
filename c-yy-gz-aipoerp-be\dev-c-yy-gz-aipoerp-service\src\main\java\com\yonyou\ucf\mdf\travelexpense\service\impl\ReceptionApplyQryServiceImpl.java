package com.yonyou.ucf.mdf.travelexpense.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QueryJoin;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.travelexpense.service.IReceptionApplyQryService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * <AUTHOR>
 *
 * 2025年3月26日
 */
@Service
public class ReceptionApplyQryServiceImpl implements IReceptionApplyQryService {

    @Autowired
    private IBillQueryRepository billQryRepository;

    @SuppressWarnings("unchecked")
    @Override
    public List<Map<String, Object>> queryByDcostdateAndHandlepsn(Map<String, Set<String>> handlepsnMap) {
        if (MapUtils.isEmpty(handlepsnMap)) {
            return Collections.emptyList();
        }
        QuerySchema schema = QuerySchema.create();
        schema.distinct();
        // 设置主表字段
        schema.addSelect("id,code,vapplyname,creator");
        schema.addSelect(new QueryField("bustype", "bustype", null, "bd.bill.TransType/id"));
        // 关联子表（因为要用的子表字段做条件）
        schema.addJoin(new QueryJoin("MemoapplyBVO", null, "left"));
        // 关联孙表（因为要用的孙表字段做条件）
        schema.addJoin(new QueryJoin("MemoapplyBVO.CommonMemoApplyBVO_extend3List",
                ".CommonMemoApplyBVO = MemoapplyBVO.id", "left,alone"));
        // 设置查询条件
        List<QueryConditionGroup> conditions = Lists.newArrayList();
        for (String key : handlepsnMap.keySet()) {
            Set<String> values = handlepsnMap.get(key);
            QueryConditionGroup condGroup = QueryConditionGroup.and(
                    QueryCondition.name("MemoapplyBVO.extend4").in(Arrays.asList("1", "2", "3")), // 只查询餐次【早餐、中餐、晚餐】有值的子表
                    QueryCondition.name("MemoapplyBVO.memoapplyBDcs.BX18").eq(key), // 子表接待日期
                    QueryCondition.name("MemoapplyBVO.CommonMemoApplyBVO_extend3List.extend3").in(values)); // 子表陪同人员
            conditions.add(condGroup);
        }
        schema.addCondition(QueryConditionGroup.and(QueryCondition.name("bustype.code").eq("SQD02"),
                QueryConditionGroup.or(conditions.toArray(new QueryConditionGroup[0]))));

        // 指定子表
        QuerySchema sonSchema = QuerySchema.create().name("MemoapplyBVO").addSelect("*"); // 这里如果指定了具体字段，则孙表查不出来，具体不知道啥原因
        // 指定孙表（如果不指定孙表，也不会查出孙表）
        QuerySchema grandsonSchema = QuerySchema.create().name("CommonMemoApplyBVO_extend3List")
                .addSelect(new QueryField("extend3", "extend3", null, "bd.staff.StaffNew/id"))
                .addSelect("extend3.code,extend3.name").addSelect("*");
        sonSchema.addCompositionSchema(grandsonSchema);

        // 设置子表
        schema.addCompositionSchema(sonSchema);

        // 执行查询
        List<Map<String, Object>> result = billQryRepository
                .queryMapBySchema("znbzbx.commonmemoapply.CommonMemoApplyVO", schema, "znbzbx");

        if (CollectionUtils.isEmpty(result)) {
            // 说明没有查询到
            return Collections.emptyList();
        }
        return result;
    }

}
