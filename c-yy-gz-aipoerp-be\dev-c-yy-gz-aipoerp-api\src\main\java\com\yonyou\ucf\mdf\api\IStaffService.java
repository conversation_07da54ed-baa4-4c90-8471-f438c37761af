package com.yonyou.ucf.mdf.api;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.entity.AIPONCStaffSyncTaskLogDetail;

import java.util.Date;
import java.util.List;

public interface IStaffService {

    public void asyncHandleAccBalanceUpdateTask(String logId, Date beginTime, Date endTime);

    /**
     * 推送员工信息到高级版
     *
     * @return {@link JSONObject }
     */
    public JSONObject pushStaffInfoToNC(JSONObject bipStaffInfo, AIPONCStaffSyncTaskLogDetail taskLogDetail);

    /**
     * 根据员工id从高级版删除员工
     *
     * @param staffId 员工 ID
     * @return {@link JSONObject }
     */
    public JSONObject deleteStaffFromNC(String staffId, String orgId);

    /**
     * 按 pubts 范围查询 员工信息
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link List }<{@link JSONObject }>
     */
    public List<JSONObject> queryStaffByPubtsRange(Date beginTime, Date endTime);

}
