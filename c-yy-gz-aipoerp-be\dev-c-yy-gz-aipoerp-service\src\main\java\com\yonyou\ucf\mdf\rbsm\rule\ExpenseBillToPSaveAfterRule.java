package com.yonyou.ucf.mdf.rbsm.rule;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ypd.bill.basic.bean.CharacteristicsEntity;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.basic.entity.convert.EntityConverter;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

@Component("expenseBillToPSaveAfterRule")
@Slf4j
public class ExpenseBillToPSaveAfterRule implements IYpdCommonRul {

    @Autowired
    private IBillRepository billRepository;

    @Autowired
    private IBillQueryRepository billQueryRepository;

    @Autowired
    private EntityConverter entityConverter;

	@Autowired
	private BipOpenApiRequest apiRequest;

	private String contractSaveUrl = "/yonbip/cpu/contract/saveStandardVersion";

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        List<BizObject> bills = null;
        try {
            bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
            if (CollectionUtils.isEmpty(bills)) {
                return null;
            }
            log.error("进入通用报销单保存后规则----lifeid-------------单据：{}",JSONUtil.toJson(bills));
            //获取保存数据
            for (BizObject b : bills) {
                //获取单据主键
                String id = b.getString("id");
                //采购合同主键
                BizObject expensebillDcs1 = b.getBizObject("expensebillDcs", BizObject.class);
                log.error("expensebillDcs1----lifeid-------------单据：{}");
                String cght = "";
                if (expensebillDcs1 != null) {
                    if(expensebillDcs1.getString("CGHT") != null){
                        cght = expensebillDcs1.getString("CGHT");
                    }

                }
                //价税总额
                Double nsummny = b.getDouble("nsummny");
                if(nsummny == null){
                    nsummny = 0.0;
                }

                //记录修改前数据
                log.error("记录修改前数据----lifeid-------------单据：{}");
                String ocght = "";     //采购合同主键
                if(StringUtils.isNotEmpty(id)){
                    IBillDO billDO = billQueryRepository.findById("znbzbx.commonexpensebill.CommonExpenseBillVO", id);
                    if (billDO != null) {
                        CharacteristicsEntity expensebillDcs = (CharacteristicsEntity) billDO.getAttrValue("expensebillDcs");
                        if (expensebillDcs != null) {
                            Object cght1 = expensebillDcs.getAttribute("CGHT");
                            if(cght1 != null ){
                                if(cght1 instanceof  String){
                                    ocght =(String) cght1;
                                }else{
                                    ocght = String.valueOf((Long)cght1);
                                }
                            }
                        }
                    }
                }

                //处理新采购合同回写
                log.error("处理新采购合同回写----lifeid-------------单据：{}");
                if(StringUtils.isNotEmpty(cght)){
                    BigDecimal sumNsummny = getHistoryNsummny(id, cght);
                    sumNsummny = sumNsummny.add(new BigDecimal(nsummny));
                    updateCGHT(cght, sumNsummny);
                }

                //判断采购合同是否更改
                log.error("判断采购合同是否更改----lifeid-------------单据：{}");
                if (!ocght.equals("") && !cght.equals(ocght)) {//处理原采购合同数据回写
                    BigDecimal sumNsummny = getHistoryNsummny(id, ocght);
                    updateCGHT(ocght, sumNsummny);
                }


            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 获取历史数据的价税总额
     * @param id
     * @param ocght
     * @return
     */
    private BigDecimal getHistoryNsummny(String id, String ocght) {
        List<Map<String, Object>> maps = queryCommonExpenseBillVOByCGHT(id, ocght);
        BigDecimal sumNsummny = new BigDecimal(0.0);
        if (CollectionUtils.isNotEmpty(maps)) {
            for (Map<String, Object> map : maps) {
                Object obj = map.get("nsummny");
                if(obj != null && obj instanceof BigDecimal){
                    sumNsummny = sumNsummny.add( (BigDecimal) obj );
                }else if(obj != null && obj instanceof BigInteger){
                    BigInteger bigInteger = (BigInteger) obj;
                    sumNsummny = sumNsummny.add( new BigDecimal(bigInteger) );
                }

            }
        }
        return sumNsummny;
    }

    /**
     * 根据采购合同主键获取报销单数据
     *
     * @param id
     * @param cght
     * @return
     */
    public List<Map<String, Object>> queryCommonExpenseBillVOByCGHT(String id, String cght) {
        QuerySchema querySchema = new QuerySchema();
        querySchema.addSelect("expensebillDcs.CGHT,sum(nsummny) as nsummny ");
        querySchema.addGroupBy("expensebillDcs.CGHT");
        querySchema.appendQueryCondition(
                QueryCondition.name("expensebillDcs.CGHT").eq(cght),
                QueryCondition.name("id").not_eq(id)
        );

        List<Map<String, Object>> maps = billQueryRepository.queryMapBySchema("znbzbx.commonexpensebill.CommonExpenseBillVO", querySchema);
        if (CollectionUtils.isNotEmpty(maps)) {
            return maps;
        }
        return null;
    }

    /**
     * 更新采购合同数据
     * @param cght
     * @param sumNsummn
     * @return
     */
    public boolean updateCGHT( String cght, BigDecimal sumNsummn) {
        //获取采购合同
        IBillDO billDO = billQueryRepository.findById("cpu-contract.contract.ContractVO", cght);
        if(billDO == null){
            throw new RuntimeException("获取采购合同失败！！！");
        }
        //采购合同含税金额
		BigDecimal taxMoney = null;
        Object obj = billDO.getAttrValue("taxMoney");
        if(obj != null && obj instanceof BigDecimal){
            taxMoney = (BigDecimal) obj ;
        }else if(obj != null && obj instanceof BigInteger){
            BigInteger bigInteger = (BigInteger) obj;
            taxMoney =new BigDecimal(bigInteger) ;
        }

		CharacteristicsEntity contractVODefineCharacter = (CharacteristicsEntity) billDO
				.getAttrValue("ContractVODefineCharacter");
		BigDecimal initbalance = null; // 期初已报销金额
		if (contractVODefineCharacter != null) {
			Object tzInitbalance = contractVODefineCharacter.getAttribute("tz_initbalance");
			if (tzInitbalance != null) {
				initbalance = new BigDecimal(tzInitbalance.toString());
			}
		}
		if (initbalance != null) {
			sumNsummn = sumNsummn.add(initbalance);
		}
        //校验金额
		if (taxMoney != null && sumNsummn.doubleValue() > taxMoney.doubleValue()) {
            throw new RuntimeException("费控累计报销金额大于采购合同含税金额,不允许保存！！！");
        }

        if(contractVODefineCharacter == null ){
            contractVODefineCharacter = new CharacteristicsEntity();
            contractVODefineCharacter.set_status(2);
        }
        contractVODefineCharacter.setAttribute("yykCS004", sumNsummn);
		contractVODefineCharacter.set_status(1);
        billDO.setAttrValue("ContractVODefineCharacter",contractVODefineCharacter);
        billDO.setAttrValue("_status",1);

		JSONObject param = JSONObject.parseObject(JSONUtil.toJson(billDO));
		JSONObject defineCharacter = param.getJSONObject("ContractVODefineCharacter");
		if (defineCharacter.getInteger("_status") != null) {
			if (defineCharacter.getInteger("_status") == 1) {
				defineCharacter.put("_status", "Update");
			} else if (defineCharacter.getInteger("_status") == 2) {
				defineCharacter.put("_status", "Insert");
			}
		}

        JSONArray contractMaterialList = param.getJSONArray("contractMaterialList");
        for (int i = 0; i < contractMaterialList.size(); i++) {
            JSONObject jsonObject = contractMaterialList.getJSONObject(i);
            jsonObject.put("_status", "Unchanged");
        }

		param.put("_status", "Update");
		param.put("supplierDocId", param.get("supplierId"));

        //遍历异常JSONArray
        JSONObject temp = JSON.parseObject(param.toJSONString());
        for (String key : temp.keySet()) {
            Object value = temp.get(key);
            if (value instanceof JSONArray) {
                if(!key.equals("contractMaterialList")){
                    param.remove(key);
                }
            }
        }

        log.error("billDO----lifeid-------------单据：{}",JSONUtil.toJson(billDO));
        log.error("contractSaveUrl----lifeid-------------单据：{}",JSONUtil.toJson(param));
		String result = apiRequest.doPost(contractSaveUrl, param);
        log.error("result----lifeid-------------单据：{}",result);
        if (result == null || result.equals("")) {
            throw new RuntimeException("回写采购合同出错！！！");
        }else{
            JSONObject jsonObject = JSON.parseObject(result);
            if(jsonObject.getInteger("code") != 200){
                throw new RuntimeException("回写采购合同失败！！！");
            }
        }
		return true;
    }

}
