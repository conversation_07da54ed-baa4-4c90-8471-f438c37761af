package com.yonyou.ucf.mdf.task;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.task.service.IPrintTaskService;
import com.yonyou.ypd.mdf.adapter.controller.BaseController;

/**
 * @className: BillPrint2PDFTask
 * @author: wjc
 * @date: 2025/4/8 14:26
 * @Version: 1.0
 * @description:
 */
@RequestMapping("/api/bill/task")
@RestController
public class BillPrint2PDFTask extends BaseController {

	@Autowired
	private IPrintTaskService printTaskService;

	/**
	 * 单据审批通过后拉取打印预览生成PDF上传附件中心和影像系统
	 * 
	 * @param request
	 * @param paramMap
	 * @return
	 */
	@RequestMapping(value = "/billPrint2PDF", method = RequestMethod.POST)
	@ResponseBody
	public Object billPrint2PDF(HttpServletRequest request,
			@RequestBody(required = false) Map<String, Object> paramMap) {
		// Service
		return printTaskService.billPrint2PDF();
	}

}
