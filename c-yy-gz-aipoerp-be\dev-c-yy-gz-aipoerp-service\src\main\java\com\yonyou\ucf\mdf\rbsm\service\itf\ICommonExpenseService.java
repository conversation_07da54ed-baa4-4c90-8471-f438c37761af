package com.yonyou.ucf.mdf.rbsm.service.itf;

import java.util.List;

import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseBillVO;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseSaveParam;
import com.yonyou.ypd.bill.basic.entity.IBillDO;

/**
 * 通用报销单接口
 * 
 * <AUTHOR>
 *
 *         2025年3月19日
 */
public interface ICommonExpenseService {

	CommonExpenseBillVO saveCommonExpense(CommonExpenseSaveParam saveParam);

	/**
	 * 根据通用报销单id查询报销单
	 * 
	 * @param ids
	 */
	List<? extends IBillDO> queryCommonExpenseByIds(List<Object> ids);

}
