package com.yonyou.ucf.mdf.aipo.service.impl;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.ucf.mdf.aipo.dto.MsgForwardDto;
import com.yonyou.ucf.mdf.aipo.service.IMsgForwardService;
import com.yonyou.ucf.mdf.aipo.utils.MsgForwardUrlConfig;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


@Service
@Slf4j
public class MsgForwardServiceImpl implements IMsgForwardService {
    @Autowired
    private MsgForwardUrlConfig msgForwardUrlConfig;

    public MsgForwardServiceImpl() {
    }

    public String msgForward(MsgForwardDto msgForwardDto) {
        log.error("执行方法开始，{}", JSONUtil.toJsonStr(msgForwardDto));
        Map<String, String> urlMap = this.msgForwardUrlConfig.getUrlMap();
        if (!urlMap.containsKey(msgForwardDto.getUrl())) {
            return "接口不存在";
        } else {
            String req;
            if ("post".equals(msgForwardDto.getReqType())) {
                log.error("执行接口地址为：{}", urlMap.get(msgForwardDto.getUrl()));
                req = this.post((String) urlMap.get(msgForwardDto.getUrl()), msgForwardDto.getData());
                return req;
            } else if ("get".equals(msgForwardDto.getReqType())) {
                req = this.get((String) urlMap.get(msgForwardDto.getUrl()), msgForwardDto.getData().toString());
                if (req == "success") {
                    return "success";
                } else {
                    log.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>业务数据：{}", req);
                    throw new RuntimeException("NCC数据同步失败" + req);
                }
            } else {
                return "请求类型不存在";
            }
        }
    }

    private String post(String url, Object data) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(new MediaType("application", "json", Charset.forName("UTF-8")));
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonData = null;

        try {
            jsonData = objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException var8) {
            throw new RuntimeException(var8);
        }

        HttpEntity<String> requestEntity = new HttpEntity(jsonData, headers);
        return (String) restTemplate.postForObject(url, requestEntity, String.class, new Object[0]);
    }

    private String get(String url, String data) {
        RestTemplate restTemplate = new RestTemplate();
        return (String) restTemplate.exchange(url + "?" + data, HttpMethod.GET, (HttpEntity) null, String.class, new Object[0]).getBody();
    }
}
