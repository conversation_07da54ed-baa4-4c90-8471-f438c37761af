package com.yonyou.ucf.mdf.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.constant.RedisKeys;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.staff.StaffBill;
import com.yonyou.aipierp.dto.ncapi.staff.StaffBillHead;
import com.yonyou.aipierp.dto.ncapi.staff.StaffJob;
import com.yonyou.aipierp.entity.AIPONCStaffSyncTaskLog;
import com.yonyou.aipierp.entity.AIPONCStaffSyncTaskLogDetail;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.aipierp.enums.StaffIdTypeEnum;
import com.yonyou.aipierp.enums.TaskTypeEnum;
import com.yonyou.cloud.middleware.embed.proteus.client.utils.PropertyUtil;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.iuap.dispatch.sdk.dto.TaskSendMsgDTO;
import com.yonyou.iuap.iuap.dispatch.sdk.util.DispatchYmsHttpClient;
import com.yonyou.iuap.yms.lock.YmsLock;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IStaffService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import com.yonyou.ucf.mdf.utils.AIPODaoHelper;
import com.yonyou.ucf.mdf.utils.RedisLockHelper;
import com.yonyou.ypd.bill.basic.entity.WeakTypingDO;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class StaffServiceImpl implements IStaffService {

	AIPORepository aipoRepository;
	NCOpenApiService ncOpenApiService;
	NCOpenApiConfig ncOpenApiConfig;
	SimpleDateFormat staffDateFormat;
	RedisLockHelper redisLockHelper;
	String dispatchCallbackUrl;
	AIPODaoHelper aipoDaoHelper;

	public StaffServiceImpl(AIPORepository aipoRepository, NCOpenApiService ncOpenApiService,
			NCOpenApiConfig ncOpenApiConfig, RedisLockHelper redisLockHelper, AIPODaoHelper aipoDaoHelper) {
		this.aipoRepository = aipoRepository;
		this.ncOpenApiService = ncOpenApiService;
		this.ncOpenApiConfig = ncOpenApiConfig;
		this.staffDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		this.redisLockHelper = redisLockHelper;
		this.dispatchCallbackUrl = PropertyUtil.getPropertyByKey("domain.iuap-apcom-coderule")
				+ "/warning/warning/async/updateTaskLog";
		this.aipoDaoHelper = aipoDaoHelper;
	}

	/**
	 * 同步人力数据到nc系统调度任务
	 *
	 * @param logId     日志 ID
	 * @param beginTime 开始时间
	 * @param endTime   结束时间
	 */
	@Async
	public void asyncHandleAccBalanceUpdateTask(String logId, Date beginTime, Date endTime) {
		String taskExecMessage = null;
		boolean taskSuccess = true;
		YmsLock taskLock = redisLockHelper.getLock(RedisKeys.LOCK_STAFFSYNCTONC_TASK,
				InvocationInfoProxy.getTenantid());
		boolean getLock = taskLock.tryLock();
		if (getLock) {
			try {
				// 查询员工数据同步至nc系统
				queryAndPushStaffInfoToNC(beginTime, endTime);
			} catch (Exception e) {
				log.error("查询员工数据同步至nc系统报错", e);
				taskExecMessage = e.getMessage();
				taskSuccess = false;
			} finally {
				taskLock.unLock();
			}
		} else {
			taskExecMessage = "数据同步任务正在执行中，请等待正在执行的任务执行完毕后重试";
		}
		// 回调调度任务，发送调度任务执行结果
		String jsonBody = JSONObject
				.toJSONString(buildSyncStaffToNcTaskSendMsgDTO(logId, taskSuccess, taskExecMessage));
		try {
			DispatchYmsHttpClient.reportPost(dispatchCallbackUrl, null, jsonBody);
		} catch (Exception e) {
			log.error("回调平台，更改任务状态异常 " + e.getMessage());
		}
	}

	/**
	 * 查询并推送员工信息到nc系统
	 *
	 * @param beginTime 员工pubts开始时间
	 * @param endTime   员工pubts结束时间
	 */
	public void queryAndPushStaffInfoToNC(Date beginTime, Date endTime) {
		// 首先初始化日志
		AIPONCStaffSyncTaskLog taskLog = new AIPONCStaffSyncTaskLog();
		List<AIPONCStaffSyncTaskLogDetail> taskLogDetails = new ArrayList<>();
		if (beginTime == null || endTime == null) {
			// 1-自动执行 2-手动设置日期范围
			taskLog.setTaskType(TaskTypeEnum.AUTO_SYNC.getValue());
			// 历史执行任务的最大的同步数据时间范围结束时间就是这次的开始时间
			beginTime = aipoRepository.getCurrentStaffTaskBeginTime();
			if (beginTime == null) {
				beginTime = new Date();
			}
			endTime = new Date();
		} else {
			taskLog.setTaskType(TaskTypeEnum.SPECIFY_TIME.getValue());
		}
		taskLog.setDataSyncBeginTime(beginTime);
		taskLog.setDataSyncEndTime(endTime);
		taskLog.set_status(ActionEnum.INSERT.getValueInt());
		// 查询所有员工
		List<JSONObject> bipStaffInfoList = queryStaffByPubtsRange(beginTime, endTime);
		// 先构建一个存全量员工的taskDetail
		AIPONCStaffSyncTaskLogDetail fullOriStaffsTaskLogDetail = new AIPONCStaffSyncTaskLogDetail();
		fullOriStaffsTaskLogDetail.setDataType("1"); // 1表示本次查到的全量员工
		fullOriStaffsTaskLogDetail.setBipOriStaffInfo(JSON.toJSONString(bipStaffInfoList));
		fullOriStaffsTaskLogDetail.set_status(ActionEnum.INSERT.getValueInt());
		taskLogDetails.add(fullOriStaffsTaskLogDetail);
		// 每一个员工都推送nc
		for (JSONObject bipStaffInfo : bipStaffInfoList) {
			AIPONCStaffSyncTaskLogDetail taskLogDetail = new AIPONCStaffSyncTaskLogDetail();
			taskLogDetail.setDataType("2"); // 2 表示单条数据的任务日志
			taskLogDetail.set_status(ActionEnum.INSERT.getValueInt());
			// 单个员工推送nc系统
			pushStaffInfoToNC(bipStaffInfo, taskLogDetail);

			taskLogDetails.add(taskLogDetail);
		}
		// 记录执行日志
		taskLog.setAIPONCStaffSyncTaskLogDetailList(taskLogDetails);
		try {
			aipoDaoHelper.add(AIPONCStaffSyncTaskLog.ENTITY_NAME, taskLog);
		} catch (Exception e) {
			log.error("员工数据同步任务日志记录失败", e);
			throw new RuntimeException(e);
		}
	}

	@Override
	public JSONObject pushStaffInfoToNC(JSONObject bipStaffInfo, AIPONCStaffSyncTaskLogDetail taskLogDetail) {
		UFinterface uFinterface = convertToStaffSaveUFinterface(bipStaffInfo);
		JSONObject resp = ncOpenApiService.savePsn(uFinterface);
		boolean success = true;
		if (!resp.getBooleanValue("success") || !"1".equals(resp.getJSONObject("data").getJSONObject("ufinterface")
				.getJSONArray("sendresult").getJSONObject(0).getString("resultcode"))) {
			log.error("同步员工信息至高级版系统失败 uFinterface-->{},resp-->{}", uFinterface, resp);
			success = false;
		}
		// 处理下日志明细
		if (taskLogDetail != null) {
			taskLogDetail.setSyncResult(success ? "1" : "0");
			taskLogDetail.setBipOriStaffInfo(bipStaffInfo.toJSONString());
			taskLogDetail.setDataSyncReq(uFinterface.toJSONString());
			taskLogDetail.setDataSyncResp(resp.toJSONString());
			taskLogDetail.setStaffName(bipStaffInfo.getString("name"));
			taskLogDetail.setStaffCode(bipStaffInfo.getString("code"));
			taskLogDetail.setStaffOrg(bipStaffInfo.getString("unitId"));
			taskLogDetail.setStaffDept(bipStaffInfo.getString("orgId"));
			taskLogDetail.setStaffPhone(bipStaffInfo.getString("mobile"));
		}
		return resp;
	}

	@Override
	public JSONObject deleteStaffFromNC(String staffId, String orgId) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("bipid", staffId);
		jsonObject.put("pk_org", orgId);
		JSONObject resp = ncOpenApiService.deletePsnByBipId(jsonObject);
		if (!resp.getBooleanValue("success")) {
			log.error("同步员工信息从高级版删除失败 req-->{},resp-->{}", jsonObject, resp);
			throw new BusinessException("同步员工信息从高级版删除失败");
		}
		return resp;
	}

	@Override
	public List<JSONObject> queryStaffByPubtsRange(Date beginTime, Date endTime) {
		List<WeakTypingDO> staffs = aipoRepository.queryStaffsByPubts(beginTime, endTime);
		if (CollectionUtils.isNotEmpty(staffs)) {
			return staffs.stream().map(weakTypingDO -> (JSONObject) JSON.toJSON(weakTypingDO.toMap()))
					.collect(Collectors.toList());
		}
		return new ArrayList<>();
	}

	private UFinterface convertToStaffSaveUFinterface(JSONObject bipStaffInfo) {
		UFinterface staffUFinterface = UFinterface.init(ncOpenApiConfig.getBiz_center(), "psndoc",
				ncOpenApiConfig.getGroupcode());
		StaffBillHead staffBillHead = new StaffBillHead();
		staffBillHead.setPk_group(ncOpenApiConfig.getGroupcode());
		// bip员工中 deptId、orgId 都是部门id，unitId是业务单元id
		staffBillHead.setPk_org(bipStaffInfo.getString("unitId"));
		staffBillHead.setCode(bipStaffInfo.getString("code"));
		staffBillHead.setName(bipStaffInfo.getString("name"));
		staffBillHead.setUsedname(bipStaffInfo.getString("formerName"));
		if (bipStaffInfo.getDate("birthDate") != null) {
			staffBillHead.setBirthdate(staffDateFormat.format(bipStaffInfo.getDate("birthDate")));
		}
		if (bipStaffInfo.getString("sex") != null && !bipStaffInfo.getString("sex").equals("0")) {
			// 性别不是未知就同步，0-性别未知
			staffBillHead.setSex(bipStaffInfo.getString("sex"));
		}
		staffBillHead.setIdtype(StaffIdTypeEnum.getNcIdTypeByBipIdType(bipStaffInfo.getString("certType_code")));
		staffBillHead.setId(bipStaffInfo.getString("certNo"));
		if (bipStaffInfo.getDate("joinWorkDate") != null) {
			staffBillHead.setJoinworkdate(staffDateFormat.format(bipStaffInfo.getDate("joinWorkDate")));
		}
		staffBillHead.setOfficephone(bipStaffInfo.getString("officeTel"));
		staffBillHead.setMobile(bipStaffInfo.getString("mobile"));
		staffBillHead.setEmail(bipStaffInfo.getString("email"));
		// 现在处理任职子表
		if (bipStaffInfo.containsKey("staffJob")) {
			List<StaffJob> staffJobs = new ArrayList<>();
			List<JSONObject> bipStaffJobs = JSON.parseArray(bipStaffInfo.getString("staffJob"), JSONObject.class);

			for (JSONObject bipStaffJob : bipStaffJobs) {
				StaffJob staffJob = new StaffJob();
				staffJob.setPk_group(ncOpenApiConfig.getGroupcode());
				// 任职信息中的 orgId：组织id deptId：部门id
				staffJob.setPk_org(bipStaffJob.getString("orgId"));
				staffJob.setPsncode(bipStaffInfo.getString("code"));
				staffJob.setPk_psncl(bipStaffJob.getString("psnclId_code")); // 人员类别编码
				staffJob.setPk_dept(bipStaffJob.getString("deptId"));
				if (bipStaffJob.getBooleanValue("isMainJob")) { // 是否主职
					staffJob.setIsmainjob("Y");
				} else {
					staffJob.setIsmainjob("N");
				}
				if (bipStaffJob.containsKey("beginDate")) {
					staffJob.setIndutydate(staffDateFormat.format(bipStaffJob.getDate("beginDate"))); // 任职开始日期
				}
				if (bipStaffJob.containsKey("endDate")) {
					staffJob.setEnddutydate(staffDateFormat.format(bipStaffJob.getDate("endDate"))); // 任职结束日期
				}
				staffJob.setPk_job(bipStaffJob.getString("jobId_code")); // 职务
				staffJob.setPk_post(bipStaffInfo.getString("postId_code")); // 岗位

				staffJobs.add(staffJob);
			}
			JSONObject item = new JSONObject();
			item.put("item", staffJobs);
			staffBillHead.setPsnjobs(item);
		}

		// 构建StaffBill
		StaffBill staffBill = new StaffBill();
		staffBill.setId(bipStaffInfo.getString("id"));
		staffBill.setBillhead(staffBillHead);
		staffUFinterface.setBill(staffBill);
		return staffUFinterface;
	}

	private TaskSendMsgDTO buildSyncStaffToNcTaskSendMsgDTO(String logId, boolean isSuccess, String message) {
		TaskSendMsgDTO taskSendMsgDTO = new TaskSendMsgDTO();
		taskSendMsgDTO.setId(logId);
		taskSendMsgDTO.setStatus(isSuccess ? 1 : 0);
		taskSendMsgDTO.setContent(message);
		taskSendMsgDTO.setTitle("同步员工数据至高级版调度任务");
		return taskSendMsgDTO;
	}
}
