<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<service>
    <!--微服务编码，专业版要替换成对应的微服务编码-->
    <serviceName>c-yy-gz-aipoerp</serviceName>
    <dbScripts>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>c-yy-gz-aipoerp_mainDataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPAIPOERPCREATE/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPAIPOERPCREATE/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPAIPOERPCREATE/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>c-yy-gz-aipoerp_mainDataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPZCEDI/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPZCEDI/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPZCEDI/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>c-yy-gz-aipoerp_mainDataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPZCMCK/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPZCMCK/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPZCMCK/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>c-yy-gz-aipoerp_mainDataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPSMR001/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPSMR001/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPSMR001/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>c-yy-gz-aipoerp_mainDataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPFIELDREF001/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPFIELDREF001/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPFIELDREF001/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>c-yy-gz-aipoerp_mainDataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPSMR002/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPSMR002/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPSMR002/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>c-yy-gz-aipoerp_mainDataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPPAY_ORG_CFG/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPPAY_ORG_CFG/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/c-yy-gz-aipoerpYONBIPPAY_ORG_CFG/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>yonbip-fi-expsrbsm_main_dataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/znbzbx/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/znbzbx/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/znbzbx/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>yonbip-am-aim_main_dataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/ucf-amc-aim/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/ucf-amc-aim/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/ucf-amc-aim/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>yonbip-am-aum_main_dataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/ucf-amc-aum/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/ucf-amc-aum/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/ucf-amc-aum/0020_self/FIN</fin>
        </dbScript>
        <dbScript>
            <dbType>rdb</dbType>
            <type>patch</type>
            <!--逻辑数据源编码，专业版要替换成对应的逻辑数据源编码-->
            <logicDataSource>yonbip-am-ambd_main_dataSource</logicDataSource>
            <ddl>patch/rdb/V3_R6_2407/ucf-amc-ambd/0020_self/DDL</ddl>
            <dml>patch/rdb/V3_R6_2407/ucf-amc-ambd/0020_self/DML</dml>
            <fin>patch/rdb/V3_R6_2407/ucf-amc-ambd/0020_self/FIN</fin>
        </dbScript>
    </dbScripts>
    <!--AuditInfo:AIPOERPCREATE,ZCEDI,ZCMCK,SMR001,FIELDREF001,SMR002,PAY_ORG_CFG,RBSM,RBSM,RBSM,RBSM,RBSM,AIM,AUM,AIM-->
</service>
