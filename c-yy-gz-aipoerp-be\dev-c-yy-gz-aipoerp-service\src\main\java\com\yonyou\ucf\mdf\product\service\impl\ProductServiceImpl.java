package com.yonyou.ucf.mdf.product.service.impl;

import com.yonyou.ucf.mdf.product.service.ProductService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/5 15:29
 * @DESCRIPTION 物料接口实现类
 */
@Service
public class ProductServiceImpl implements ProductService {
    @Autowired
    private IBillQueryRepository billQueryRepository;

    @Override
    public List<Map<String, Object>> getProductListById(Object id) {
        QuerySchema querySchema = QuerySchema.create()
                .addSelect("id,code,name,manageClass,orgId,realProductAttributeType,detail.valueManageType")
                .addCondition(QueryConditionGroup.and(
                        QueryConditionGroup.or(
                                QueryCondition.name("realProductAttributeType").eq(4),
                                QueryCondition.name("detail.valueManageType").eq(1)),
                        QueryCondition.name("id").eq(id)));
       return billQueryRepository.queryMapBySchema("pc.product.Product", querySchema, "productcenter");
    }

    @Override
    public List<Map<String, Object>> getEnableProductList() {
        QuerySchema querySchema = QuerySchema.create()
                .addSelect("id,code,name,manageClass,realProductAttributeType,detail.valueManageType")
                .addCondition(QueryConditionGroup.and(
                        QueryConditionGroup.or(
                                QueryCondition.name("realProductAttributeType").eq(4),
                                QueryCondition.name("detail.valueManageType").eq(1)),
                        QueryCondition.name("detail.stopstatus").eq(false)));
        return billQueryRepository.queryMapBySchema("pc.product.Product", querySchema, "productcenter");
    }
}
