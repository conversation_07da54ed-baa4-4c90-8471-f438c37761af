package com.yonyou.ucf.mdf.service.impl;


import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
public class NCOpenApiConfig {

    // 高级版openApi域名
    private String ncDomain;
    // 账套编码
    private String biz_center;
    // 第三方应用id
    private String client_id;
    // ncc用户编码，在客户端模式下，非必传，不传默认使用第三方应用在系统设置的用户编码
    private String usercode;
    // 调用方式，目前支持两种方式：client_credentials客户端模式；password密码模式
    private String grant_type;
    // 用于在getClientSecret获取client_secret
    private String client_secret;
    // 用于在getClientSecret获取client_secret
    private String pub_key;
    // 请求的加密级别，用于业务请求加密请求体
    private String level;
    // 集团编码
    private String groupcode;

    // todo + yms配置 以下配置均为测试配置，仅为保证容器正常启动
    public NCOpenApiConfig(@Value("${nc.api.domain:http://103.81.6.148:8089}") String ncDomain,
                           @Value("${nc.api.biz_center:YSGZ}") String biz_center,
                           @Value("${nc.api.client_id:test}") String client_id,
                           @Value("${nc.api.usercode:wangy}") String usercode,
                           @Value("${nc.api.grant_type:client_credentials}") String grant_type,
                           @Value("${nc.api.client_secret:0d4847387a2c4f9ca6e7}") String client_secret,
                           @Value("${nc.api.pub_key:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjBT1OtdwGJwVZIi9u5U71h+PLBAdivQy+ZddYvcVyYZrYl9ZJ16cFRNVKBWK0mSwksb7qRtk0QccWirDMj7Lrg7EZ/chW9M5wzlZNsUjZce5EoYUHRvaER0UqTsVSL+M8ajwL4Dbt90Ri63SUVndxzhTsVLlawwGuhE5uWQKH7gfu7IElPP552jrp80QPiQ96KZYymytc6lkWScNHrZUVfxLSVLnYQxbtMh2SxSxCkAayCK7uEBhuQFCzu1bna3k1/12YScBSm0Wg2DDUuLUE9AwpSN1NqzlBf2ppFEChxEhtp6mZMuZ8bOCBfygFyy0GFhi5U//U/EOLP+vx1eF1QIDAQAB}") String pub_key,
                           @Value("${nc.api.level:level0}") String level,
                           @Value("${nc.api.groupcode:A}") String groupcode) {
        this.biz_center = biz_center;
        this.client_id = client_id;
        this.usercode = usercode;
        this.ncDomain = ncDomain;
        this.grant_type = grant_type;
        this.client_secret = client_secret;
        this.pub_key = pub_key;
        this.level = level;
        this.groupcode = groupcode;
    }

}
