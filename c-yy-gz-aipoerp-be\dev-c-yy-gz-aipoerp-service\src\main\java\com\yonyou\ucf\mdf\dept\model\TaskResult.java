package com.yonyou.ucf.mdf.dept.model;

import lombok.Data;

/**
 * 定时任务返回结果实体类
 */
@Data
public class TaskResult {

    /**
     * 是否为异步接口
     */
    private Boolean asynchronized;

    /**
     * 任务执行结果 1: 成功 0: 失败
     */
    private Integer status;

    /**
     * 发送信息标题
     */
    private String title;

    /**
     * 发送信息内容
     */
    private String content;

    /**
     * 失败信息
     */
    private String msg;

    /**
     * 创建成功结果
     */
    public static TaskResult success(String title, String content) {
        TaskResult result = new TaskResult();
        result.setAsynchronized(false);
        result.setStatus(1);
        result.setTitle(title);
        result.setContent(content);
        return result;
    }

    /**
     * 创建失败结果
     */
    public static TaskResult failure(String title, String content, String msg) {
        TaskResult result = new TaskResult();
        result.setAsynchronized(false);
        result.setStatus(0);
        result.setTitle(title);
        result.setContent(content);
        result.setMsg(msg);
        return result;
    }

    /**
     * 创建异步结果
     */
    public static TaskResult async(String title, String content) {
        TaskResult result = new TaskResult();
        result.setAsynchronized(true);
        result.setStatus(1);
        result.setTitle(title);
        result.setContent(content);
        return result;
    }
}
