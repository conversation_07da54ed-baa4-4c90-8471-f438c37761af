package com.yonyou.aipoerp.controller;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.IDefArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/defArchive")
@Slf4j
public class AIPODefArchiveController {

    /**
     * 使用权资产自定义档案编码
     */
    public static String USE_FUND_DEFARCH_CODE = "ysgz12";
    /**
     * 软件名称自定义档案编码
     */
    public static String SOFT_NAME_DEFARCH_CODE = "rjmc";

    IDefArchiveService defArchiveService;

    public AIPODefArchiveController(IDefArchiveService defArchiveService) {
        this.defArchiveService = defArchiveService;
    }


    @RequestMapping("/nc/push/useFund")
    public JSONObject pushUseFundToNc(@RequestBody JSONObject body){
        JSONObject resp = new JSONObject();
        log.error("成功进入了推送自定义档案到nc的接口 body--->{}",body);
        try {
            defArchiveService.pushDefArchiveToNC(body,USE_FUND_DEFARCH_CODE);
        }catch (Exception e){
            resp.put("succeed",false);
            resp.put("message",e.getMessage());
            return resp;
        }
        resp.put("succeed",true);
        return resp;
    }

    @RequestMapping("/nc/delete/useFund")
    public JSONObject deleteUseFundFromNc(@RequestBody JSONObject body){
        JSONObject resp = new JSONObject();
        log.error("成功进入了推送自定义档案到nc的接口 body--->{}",body);
        try {
            defArchiveService.deleteDefArchiveFromNC(body.getString("id"));
        }catch (Exception e){
            resp.put("succeed",false);
            resp.put("message",e.getMessage());
            return resp;
        }
        resp.put("succeed",true);
        return resp;
    }

    @RequestMapping("/nc/push/softName")
    public JSONObject pushSoftNameToNc(@RequestBody JSONObject body){
        JSONObject resp = new JSONObject();
        log.error("成功进入了推送自定义档案到nc的接口 body--->{}",body);
        try {
            defArchiveService.pushDefArchiveToNC(body,SOFT_NAME_DEFARCH_CODE);
        }catch (Exception e){
            resp.put("succeed",false);
            resp.put("message",e.getMessage());
            return resp;
        }
        resp.put("succeed",true);
        return resp;
    }

    @RequestMapping("/nc/delete/softName")
    public JSONObject deleteSoftNameFromNc(@RequestBody JSONObject body) {
        JSONObject resp = new JSONObject();
        log.error("成功进入了推送自定义档案到nc的接口 body--->{}", body);
        try {
            defArchiveService.deleteDefArchiveFromNC(body.getString("id"));
        } catch (Exception e) {
            resp.put("succeed", false);
            resp.put("message", e.getMessage());
            return resp;
        }
        resp.put("succeed", true);
        return resp;
    }

}
