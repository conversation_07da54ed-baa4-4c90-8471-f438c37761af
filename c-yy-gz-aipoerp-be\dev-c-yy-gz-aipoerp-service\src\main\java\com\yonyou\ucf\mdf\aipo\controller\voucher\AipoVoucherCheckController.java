package com.yonyou.ucf.mdf.aipo.controller.voucher;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.aipo.service.IAipoVoucherCheckService;
import com.yonyou.ucf.mdf.aipo.service.IAipoVoucherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/rest")
@Slf4j
public class AipoVoucherCheckController {

    @Autowired
    IAipoVoucherCheckService voucherCheckService;

    @RequestMapping("/aipo/voucherCheck")
    public Object VoucherCheck(@RequestBody JSONObject jsonObject, HttpServletRequest request) throws Exception {
        log.debug("begin VoucherCheck");
        JSONObject params = new JSONObject();
        params.put("code", "200");

        try {
            log.error("凭证检查入参:{}", jsonObject);
            voucherCheckService.beforeVoucherCheck(jsonObject);
        } catch (Exception e) {
            params.put("code", "500");
            params.put("message", e.getMessage());
        }

        return params;
    }

}
