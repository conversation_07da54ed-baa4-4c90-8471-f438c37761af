(self.webpackChunkc_yy_gz_aipoerp=self.webpackChunkc_yy_gz_aipoerp||[]).push([[398],{972:function(e,r,t){"use strict";t.r(r),t.d(r,{Demo:function(){return l}});var n=t(905),o=t.n(n);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function i(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}function u(e){var r=function(e,r){if("object"!=c(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==c(r)?r:r+""}function a(e,r,t){return r=y(r),function(e,r){if(r&&("object"==c(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,f()?Reflect.construct(r,t||[],y(e).constructor):r.apply(e,t))}function f(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(f=function(){return!!e})()}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}function p(e,r){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e},p(e,r)}var l=function(e){function r(){return function(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r),a(this,r,arguments)}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),r&&p(e,r)}(r,e),t=r,(n=[{key:"render",value:function(){return o().createElement("div",null,"这是一个PC组件Demo!")}}])&&i(t.prototype,n),c&&i(t,c),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,c}(n.Component)},694:function(){},361:function(){},313:function(e,r,t){"use strict";var n=t(972),o=t(694),c=t(361),i=t(23),u=t(595),a=t(327),f=t(478),y=t.n(f),p=t(285),l=(t(744),{basic:n,formatter:o,home:c,meta:i,modal:u,popover:a,portal:y(),toolbar:p});r.A=l},23:function(){},595:function(){},327:function(){},478:function(){},285:function(){},398:function(e,r,t){var n=t(635);cb.extend.registerScripts("c-yy-gz-aipoerp",n);var o=t(313).A;cb.extend.registerComponents("c-yy-gz-aipoerp",o);var c=t(826).A;cb.extend.registerReducers("c-yy-gz-aipoerp",c);var i=t(507).A;cb.extend.registerRoutes("c-yy-gz-aipoerp",i);var u=t(796).A;cb.extend.registerBizAction("c-yy-gz-aipoerp",u),cb.extend.registerEnv("c-yy-gz-aipoerp",{currentEnv:"daily"}),cb.lang.registerLang("c-yy-gz-aipoerp",t(626),"")},826:function(e,r,t){"use strict";var n=t(381);r.A={Demo:n.A}},507:function(e,r,t){"use strict";var n=t(972);r.A=[{exact:!0,path:"/demo",component:n.Demo}]},744:function(e){"use strict";var r=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var r=Object.prototype.toString.call(e);return"[object RegExp]"===r||"[object Date]"===r||function(e){return e.$$typeof===t}(e)}(e)};var t="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,r){return!1!==r.clone&&r.isMergeableObject(e)?a((t=e,Array.isArray(t)?[]:{}),e,r):e;var t}function o(e,r,t){return e.concat(r).map((function(e){return n(e,t)}))}function c(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(r){return Object.propertyIsEnumerable.call(e,r)})):[]}(e))}function i(e,r){try{return r in e}catch(e){return!1}}function u(e,r,t){var o={};return t.isMergeableObject(e)&&c(e).forEach((function(r){o[r]=n(e[r],t)})),c(r).forEach((function(c){(function(e,r){return i(e,r)&&!(Object.hasOwnProperty.call(e,r)&&Object.propertyIsEnumerable.call(e,r))})(e,c)||(i(e,c)&&t.isMergeableObject(r[c])?o[c]=function(e,r){if(!r.customMerge)return a;var t=r.customMerge(e);return"function"==typeof t?t:a}(c,t)(e[c],r[c],t):o[c]=n(r[c],t))})),o}function a(e,t,c){(c=c||{}).arrayMerge=c.arrayMerge||o,c.isMergeableObject=c.isMergeableObject||r,c.cloneUnlessOtherwiseSpecified=n;var i=Array.isArray(t);return i===Array.isArray(e)?i?c.arrayMerge(e,t,c):u(e,t,c):n(t,c)}a.all=function(e,r){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,t){return a(e,t,r)}),{})};var f=a;e.exports=f}}]);
//# sourceMappingURL=extend.398.c6710f16.min.js.map