package com.yonyou.ucf.mdf.equip.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.ucf.mdf.equip.model.BatchResult;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import com.yonyou.ucf.mdf.equip.service.EquipDataImportService;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.basic.entity.convert.EntityConverter;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.base.EntityStatus;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/18 9:37
 * @DESCRIPTION 资产卡片导入数据接口实现类
 */
@Slf4j
@Service
public class EquipDataImportServiceImpl implements EquipDataImportService {
    //资产变动
    private static final String equipChange = "/yonbip/am/aumaltercard/save";

    @Autowired
    private BipOpenApiRequest apiRequest;

    @Autowired
    private IBillCommonRepository billCommonRepository;

    @Autowired
    private IBillQueryRepository billQueryRepository;

    @Autowired
    private EntityConverter entityConverter;

    @Override
    public ResponseResult<BatchResult> pushDown(List<BizObject> bizObjects) {
        log.error("接收到推送至资产卡片的数据---->{}", bizObjects);
        if (CollUtil.isEmpty(bizObjects)) {
            throw new RuntimeException("推送数据为空！");
        }
        //过滤存只取存在差异，且未推送的数据
        List<BizObject> pushList = bizObjects.stream().filter(f -> "Y".equals(f.get("is_exit_diff")) && !"Y".equals(f.get("is_push"))).collect(Collectors.toList());
        log.error("推送至资产变动单的真实数据---->{}", pushList);
        if (CollUtil.isEmpty(pushList)) {
            throw new RuntimeException("不存在有效数据");
        }
        ResponseResult<BatchResult> result = pushToEquipChange(pushList);
        log.error("调用openAPI推送至资产变动单结果---->{}", result);

        if (result.isSuccess2()) {
            BatchResult data = result.getData();
            Integer failCount = data.getFailCount();
            if (failCount == null || failCount == 0 ) {
                updateEquipDataPushStatus(pushList);
            } else {
                List<Object> infos = data.getInfos();
                if (CollUtil.isNotEmpty(infos)) {
                    List<String> equipCodes = infos.stream().map(map -> {
                        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(map));
                        return jsonObject.getString("pk_equip__equip_code");
                    }).collect(Collectors.toList());
                    pushList = pushList.stream().filter(fil -> equipCodes.contains(fil.getString("pk_equip__equip_code"))).collect(Collectors.toList());
                    updateEquipDataPushStatus(pushList);
                }
            }
        }
        return result;
    }

    @SneakyThrows
    private void updateEquipDataPushStatus(List<BizObject> pushList) {
        List<Map<String, Object>> updateList = pushList.stream().map(m -> {
            Map<String, Object> updateDaMap = new HashMap<>();
            updateDaMap.put("_status", ActionEnum.UPDATE.getValueInt());
            updateDaMap.put("id", m.get("id"));
            updateDaMap.put("is_push", "Y");
            return updateDaMap;
        }).collect(Collectors.toList());
        List<IBillDO> iBillDOS = entityConverter.convertObjToDO("ZCEDI.ZCEDI.EquipCardDataVo", updateList);
        List<IBillDO> equipCardDataVo = billCommonRepository.commonSaveBill(iBillDOS, "EquipCardDataVo");
        log.error("更新资产卡片导入数据列表是否已推送状态结果---->{}", equipCardDataVo);
    }

    //推送至资产变动单
    private ResponseResult<BatchResult> pushToEquipChange(List<BizObject> bizObjects) {
        Map<String, List<BizObject>> orgMap = bizObjects.stream().collect(Collectors.groupingBy(k -> k.getString("pk_org")));
        List<BizObject> changeBills = new ArrayList<>();
        orgMap.forEach((key, values) -> {
            //勾选的数据列表
            //TODO 变动单信息构建
            BizObject changeBill = new BizObject();
            //资产组织 必填
            changeBill.set("pk_org", key);
            //变动日期 必填
            changeBill.set("bill_date", DateUtil.formatDateTime(new Date()));
            //变动单编码非必填
            // changeBill.set("bill_code","");
            //单据类型 必填
            changeBill.set("bill_type", "4A07");
            //交易类型编码 必填
            changeBill.set("transi_type", "4A07-07");
            //单据状态 必填
            changeBill.set("bill_status", 0L);
            //changeBill.set("pk_recorder", "");
            //changeBill.set("memo", "");
            //changeBill.set("pk_transitype", "");
            //changeBill.set("userDefines", "");
            changeBill.setEntityStatus(EntityStatus.Insert);
            StringBuffer alterInfoBuffer = new StringBuffer();
            //todo 变动单子表信息明细
            List<BizObject> changeBillDetailList = new ArrayList<>();
            for (BizObject bizObject : values) {
                String id = bizObject.getString("id");
                String equipCode = bizObject.getString("equip_code");
                List<Map<String, Object>> importDetailList = selectDetail(id);
                if (CollUtil.isEmpty(importDetailList)) break;
                //构建变动但子表信息
                BizObject changeBillDetail = buildChangeBillDetail(alterInfoBuffer, importDetailList);
                if (changeBillDetail == null) break;
                //资产编码 必填
                changeBillDetail.set("pk_equip__equip_code", equipCode);
                changeBillDetailList.add(changeBillDetail);
            }
            //变动项目 必填
            changeBill.set("alterinfo", alterInfoBuffer);
            if (CollUtil.isNotEmpty(changeBillDetailList)) {
                //资产变动子表 必填
                changeBill.set("bodyvos", changeBillDetailList);
                changeBills.add(changeBill);
            }
        });
        Map<String, Object> pushDataMap = new HashMap<>();
        pushDataMap.put("data", changeBills);
        return apiRequest.doPost(equipChange, pushDataMap, new TypeReference<ResponseResult<BatchResult>>() {
        });
    }

    @Nullable
    private BizObject buildChangeBillDetail(StringBuffer alterInfoBuffer, List<Map<String, Object>> importDetailList) {
        if (CollUtil.isEmpty(importDetailList)) return null;
        //todo 变动单子表信息
        BizObject changeBillDetail = new BizObject();
        changeBillDetail.setEntityStatus(EntityStatus.Insert);
        for (int i = 0; i < importDetailList.size(); i++) {
            Map<String, Object> importData = importDetailList.get(i);
            Object changeField = importData.get("change_field");
            String changeAfterKey = changeField + "_after";
            Object changeAfterValue = importData.get("change_after_value");
            if ("equip_name".equals(changeField)) {
                BizObject equipNameObj = new BizObject();
                equipNameObj.set("zh_CN", changeAfterValue);
                changeAfterValue = equipNameObj;
            }
            changeBillDetail.set(changeAfterKey, changeAfterValue);
            if (i == importDetailList.size() - 1) {
                alterInfoBuffer.append(changeField);
            } else {
                alterInfoBuffer.append(changeField).append(",");
            }
        }
        return changeBillDetail;
    }


    private List<Map<String, Object>> selectDetail(String foreignerKey) {
        QuerySchema querySchema = QuerySchema.create().addSelect("*").addCondition(QueryConditionGroup.and(
                QueryCondition.name("foreignerKey").in(foreignerKey)));
        return billQueryRepository.queryMapBySchema("ZCEDI.ZCEDI.EquipCardDataContrastDetailVo", querySchema, "c-yy-gz-aipoerp");
    }
}
