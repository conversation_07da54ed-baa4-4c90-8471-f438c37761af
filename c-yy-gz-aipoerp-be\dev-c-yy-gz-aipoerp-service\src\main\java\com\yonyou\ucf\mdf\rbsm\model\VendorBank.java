package com.yonyou.ucf.mdf.rbsm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 供应商银行信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class VendorBank {
	private String id;
	private String bank; // 银行类别
	private String accountType; // 账号类型, 0:对公账号、1:对私账号
	private String bank_name; // 银行类别名称
	private MultiLanguage accountname; // 账号名称
	private String stopstatus; // 停用状态
	private String correspondentcode; // 联行号
	private String account; // 银行账号
	private String openaccountbank; // 银行网点
	private String openaccountbank_name; // 银行名称
	private boolean defaultbank; // 默认银行
}
