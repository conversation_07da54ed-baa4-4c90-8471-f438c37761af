package com.yonyou.ucf.mdf.api;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;

/**
 * 高级版open API 调用服务
 */
public interface NCOpenApiService {

    /**
     * 获取token
     *
     * @return {@link JSONObject }
     */
    public JSONObject getToken();

    /**
     * 通用的高级版api测试方法
     *
     * @param reqStr req jsonString，用作请求体
     * @param uri    URI    请求的uri
     * @return {@link JSONObject }
     */
    public JSONObject doCommonApi(String reqStr, String uri);

    /**
     * 高级版新增（修改）部门 根据ufinterface.bill.id 来判断，已经存在走修改，不存在走新增
     *
     * @return {@link JSONObject }
     */
    public JSONObject addDeptEx(UFinterface ufinterface);

    /**
     * 根据bip部门id删除高级版部门
     *
     * @param deleteBody {"id": "testid1209-5"(旗舰版id),"version": "1"（固定值）}
     * @return {@link JSONObject }
     */
    public JSONObject deleteDeptByBipId(JSONObject deleteBody);


    /**
     * 高级版新增（修改）员工 根据ufinterface.bill.id 来判断，已经存在走修改，不存在走新增
     *
     * @return {@link JSONObject }
     */
    public JSONObject savePsn(UFinterface ufinterface);

    /**
     * 根据bip员工id删除高级版员工
     *
     * @param deleteBody {"id": "testid1209-5"(旗舰版id),"version": "1"（固定值）}
     * @return {@link JSONObject }
     */
    public JSONObject deletePsnByBipId(JSONObject deleteBody);

    /**
     * 高级版保存企业银行账户
     *
     * @param ufinterface ufinterface
     * @return {@link JSONObject }
     */
    public JSONObject saveEnterBank(UFinterface ufinterface);

    /**
     * 高级版删除企业银行账户
     *
     * @param deleteBody
     * @return {@link JSONObject }
     */
    public JSONObject deleteEnterBankBipId(JSONObject deleteBody);

    /**
     * 高级版保存项目信息
     *
     * @param ufinterface ufinterface
     * @return {@link JSONObject }
     */
    public JSONObject saveProject(UFinterface ufinterface);

    /**
     * 高级版删除项目
     *
     * @param deleteBody
     * @return {@link JSONObject }
     */
    public JSONObject deleteProject(JSONObject deleteBody);

    /**
     * 保存 自定义档案到高级版
     *
     * @param ufinterface ufinterface
     * @return {@link JSONObject }
     */
    public JSONObject saveDefArchive(UFinterface ufinterface);

    /**
     * 从高级版删除自定义档案
     *
     * @param deleteBody 删除正文
     * @return {@link JSONObject }
     */
    public JSONObject deleteDefArchive(JSONObject deleteBody);

    /**
     * 推送客户档案、供应商档案到高级版客户档案
     *
     * @param saveBody 保存正文
     * @return {@link JSONObject }
     */
    public JSONObject saveCustomer(JSONObject saveBody);

    /**
     * 从高级版删除客户档案、供应商档案
     *
     * @return {@link JSONObject }
     */
    public JSONObject deleteCustomer(JSONObject deleteBody);
}
