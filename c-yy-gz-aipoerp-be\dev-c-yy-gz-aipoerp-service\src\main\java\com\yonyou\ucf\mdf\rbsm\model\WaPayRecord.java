package com.yonyou.ucf.mdf.rbsm.model;

import java.util.Date;
import java.util.List;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 薪资发放推费控记录
 * @Date 2025-03-12 17:03:17
 * @since 2023/11/28
 **/
@YMSEntity(name = "SMR002.SMR002.WaPayRecord", domain = "c-yy-gz-aipoerp")
public class WaPayRecord extends SuperDO {
	public static final String ENTITY_NAME = "SMR002.SMR002.WaPayRecord";
	public static final String TAXORG = "taxOrg";
	public static final String TAXDEPT = "taxDept";
	public static final String SUMTYPE = "sumType";
	public static final String EXPENSEID = "expenseId";
	public static final String WAPAYRECORDDETAILLIST = "WaPayRecordDetailList";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 财务组织 */
	private String taxOrg;
	/* 财务部门 */
	private String taxDept;
	/* 推送类别 */
	private String sumType;
	/* 对应报销单id */
	private String expenseId;
	/* 薪资发放推送费控记录明细 */
	private List<WaPayRecordDetail> WaPayRecordDetailList;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setTaxOrg(String taxOrg) {
		this.taxOrg = taxOrg;
	}

	public void setTaxDept(String taxDept) {
		this.taxDept = taxDept;
	}

	public void setSumType(String sumType) {
		this.sumType = sumType;
	}

	public void setExpenseId(String expenseId) {
		this.expenseId = expenseId;
	}

	public void setWaPayRecordDetailList(List<WaPayRecordDetail> WaPayRecordDetailList) {
		this.WaPayRecordDetailList = WaPayRecordDetailList;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getTaxOrg() {
		return taxOrg;
	}

	public String getTaxDept() {
		return taxDept;
	}

	public String getSumType() {
		return sumType;
	}

	public String getExpenseId() {
		return expenseId;
	}

	public List<WaPayRecordDetail> getWaPayRecordDetailList() {
		return WaPayRecordDetailList;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
