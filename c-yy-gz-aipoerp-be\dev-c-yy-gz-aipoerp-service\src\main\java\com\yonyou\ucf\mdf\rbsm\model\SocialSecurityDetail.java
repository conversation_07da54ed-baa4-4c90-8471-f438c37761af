package com.yonyou.ucf.mdf.rbsm.model;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.base.*;

import java.math.BigDecimal;
import java.util.Date;

import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 社保缴交记录子表
 * @Date 2025-03-05 10:54:37
 * @since 2023/11/28
 **/
@YMSEntity(name = "SMR001.SMR001.SocialSecurityDetail", domain = "c-yy-gz-aipoerp")
public class SocialSecurityDetail extends SuperDO {
    public static final String ENTITY_NAME = "SMR001.SMR001.SocialSecurityDetail";
    public static final String STAFFID = "staffId";
    public static final String ORGID = "orgId";
    public static final String DEPTID = "deptId";
    public static final String ENDOWMENTINSURANCERADIX = "endowmentInsuranceRadix";
    public static final String ENDOWMENTINSURANCEOWNBASE = "endowmentInsuranceOwnBase";
    public static final String ENDOWMENTINSURANCEOWNPAY = "endowmentInsuranceOwnPay";
    public static final String ENDOWMENTINSURANCEORGBASE = "endowmentInsuranceOrgBase";
    public static final String ENDOWMENTINSURANCEORGPAY = "endowmentInsuranceOrgPay";
    public static final String FOREIGNERKEY = "foreignerKey";
    public static final String UNEMPLOYMENTRADIX = "unemploymentRadix";
    public static final String UNEMPLOYMENTOWNBASE = "unemploymentOwnBase";
    public static final String UNEMPLOYMENTOWNPAY = "unemploymentOwnPay";
    public static final String UNEMPLOYMENTORGBASE = "unemploymentOrgBase";
    public static final String UNEMPLOYMENTORGPAY = "unemploymentOrgPay";
    public static final String OCCUPATIONALINJURYRADIX = "occupationalInjuryRadix";
    public static final String OCCUPATIONALINJURYOWNBASE = "occupationalInjuryOwnBase";
    public static final String OCCUPATIONALINJURYOWNPAY = "occupationalInjuryOwnPay";
    public static final String OCCUPATIONALINJURYORGBASE = "occupationalInjuryOrgBase";
    public static final String OCCUPATIONALINJURYORGPAY = "occupationalInjuryOrgPay";
    public static final String MEDICALTREATMENTRADIX = "medicalTreatmentRadix";
    public static final String MEDICALTREATMENTOWNBASE = "medicalTreatmentOwnBase";
    public static final String MEDICALTREATMENTOWNPAY = "medicalTreatmentOwnPay";
    public static final String MEDICALTREATMENTOORGBASE = "medicalTreatmentOorgBase";
    public static final String MEDICALTREATMENTOORGPAY = "medicalTreatmentOorgPay";
    public static final String REPMEDICALTREATMENTRADIX = "repMedicalTreatmentRadix";
    public static final String REPMEDICALTREATMENTOWNBASE = "repMedicalTreatmentOwnBase";
    public static final String REPMEDICALTREATMENTOWNPAY = "repMedicalTreatmentOwnPay";
    public static final String REPMEDICALTREATMENTORGBASE = "repMedicalTreatmentOrgBase";
    public static final String REPMEDICALTREATMENTORGPAY = "repMedicalTreatmentOrgPay";
    public static final String BIRTHINSURANCERADIX = "birthInsuranceRadix";
    public static final String BIRTHINSURANCEOWNBASE = "birthInsuranceOwnBase";
    public static final String BIRTHINSURANCEOWNPAY = "birthInsuranceOwnPay";
    public static final String BIRTHINSURANCEORGBASE = "birthInsuranceOrgBase";
    public static final String BIRTHINSURANCEORGPAY = "birthInsuranceOrgPay";
    public static final String SERIOUSILLNESSRADIX = "seriousIllnessRadix";
    public static final String SERIOUSILLNESSOWNBASE = "seriousIllnessOwnBase";
    public static final String SERIOUSILLNESSOWNPAY = "seriousIllnessOwnPay";
    public static final String SERIOUSILLNESSORGBASE = "seriousIllnessOrgBase";
    public static final String SERIOUSILLNESSORGPAY = "seriousIllnessOrgPay";
    public static final String RESERVEDFUNDSRADIX = "reservedFundsRadix";
    public static final String RESERVEDFUNDSOWNBASE = "reservedFundsOwnBase";
    public static final String RESERVEDFUNDSOWNPAY = "reservedFundsOwnPay";
    public static final String RESERVEDFUNDSORGBASE = "reservedFundsOrgBase";
    public static final String RESERVEDFUNDSORGPAY = "reservedFundsOrgPay";
    public static final String REPRESERVEDFUNDSRADIX = "repReservedFundsRadix";
    public static final String REPRESERVEDFUNDSOWNBASE = "repReservedFundsOwnBase";
    public static final String REPRESERVEDFUNDSOWNPAY = "repReservedFundsOwnPay";
    public static final String REPRESERVEDFUNDSORGBASE = "repReservedFundsOrgBase";
    public static final String REPRESERVEDFUNDSORGPAY = "repReservedFundsOrgPay";
    public static final String INSURANCEID = "insuranceId";
    public static final String CREATETIME = "createTime";
    public static final String CREATOR = "creator";
    public static final String ID = "id";
    public static final String MODIFIER = "modifier";
    public static final String MODIFYTIME = "modifyTime";
    public static final String PUBTS = "pubts";
    public static final String YTENANTID = "ytenantId";

    /* 员工 */
    private String staffId;
    /* 任职组织 */
    private String orgId;
    /* 任职部门 */
    private String deptId;
    /* 养老保险缴纳基数 */
    private BigDecimal endowmentInsuranceRadix;
    /* 养老保险个人缴纳基数 */
    private BigDecimal endowmentInsuranceOwnBase;
    /* 养老保险个人缴纳额 */
    private BigDecimal endowmentInsuranceOwnPay;
    /* 养老保险单位缴纳基数 */
    private BigDecimal endowmentInsuranceOrgBase;
    /* 养老保险单位缴纳额 */
    private BigDecimal endowmentInsuranceOrgPay;
    /* 社保缴交记录主表 */
    private String foreignerKey;
    /* 失业保险缴纳基数 */
    private BigDecimal unemploymentRadix;
    /* 失业保险个人缴纳基数 */
    private BigDecimal unemploymentOwnBase;
    /* 失业保险个人缴纳额 */
    private BigDecimal unemploymentOwnPay;
    /* 失业保险单位缴纳基数 */
    private BigDecimal unemploymentOrgBase;
    /* 失业保险单位缴纳额 */
    private BigDecimal unemploymentOrgPay;
    /* 工伤保险缴纳基数 */
    private BigDecimal occupationalInjuryRadix;
    /* 工伤保险个人缴纳基数 */
    private BigDecimal occupationalInjuryOwnBase;
    /* 工伤保险个人缴纳额 */
    private BigDecimal occupationalInjuryOwnPay;
    /* 工伤保险单位缴纳基数 */
    private BigDecimal occupationalInjuryOrgBase;
    /* 工伤保险单位缴纳额 */
    private BigDecimal occupationalInjuryOrgPay;
    /* 医疗保险缴纳基数 */
    private BigDecimal medicalTreatmentRadix;
    /* 医疗保险个人缴纳基数 */
    private BigDecimal medicalTreatmentOwnBase;
    /* 医疗保险个人缴纳额 */
    private BigDecimal medicalTreatmentOwnPay;
    /* 医疗保险单位缴纳基数 */
    private BigDecimal medicalTreatmentOorgBase;
    /* 医疗保险单位缴纳额 */
    private BigDecimal medicalTreatmentOorgPay;
    /* 补充医疗保险缴纳基数 */
    private BigDecimal repMedicalTreatmentRadix;
    /* 补充医疗保险个人缴纳基数 */
    private BigDecimal repMedicalTreatmentOwnBase;
    /* 补充医疗保险个人缴纳额 */
    private BigDecimal repMedicalTreatmentOwnPay;
    /* 补充医疗保险单位缴纳基数 */
    private BigDecimal repMedicalTreatmentOrgBase;
    /* 补充医疗保险单位缴纳额 */
    private BigDecimal repMedicalTreatmentOrgPay;
    /* 生育保险缴纳基数 */
    private BigDecimal birthInsuranceRadix;
    /* 生育保险个人缴纳基数 */
    private BigDecimal birthInsuranceOwnBase;
    /* 生育保险个人缴纳额 */
    private BigDecimal birthInsuranceOwnPay;
    /* 生育保险单位缴纳基数 */
    private BigDecimal birthInsuranceOrgBase;
    /* 生育保险单位缴纳额 */
    private BigDecimal birthInsuranceOrgPay;
    /* 大病保险缴纳基数 */
    private BigDecimal seriousIllnessRadix;
    /* 大病保险个人缴纳基数 */
    private BigDecimal seriousIllnessOwnBase;
    /* 大病保险个人缴纳额 */
    private BigDecimal seriousIllnessOwnPay;
    /* 大病保险单位缴纳基数 */
    private BigDecimal seriousIllnessOrgBase;
    /* 大病保险单位缴纳额 */
    private BigDecimal seriousIllnessOrgPay;
    /* 公积金缴纳基数 */
    private BigDecimal reservedFundsRadix;
    /* 公积金个人缴纳基数 */
    private BigDecimal reservedFundsOwnBase;
    /* 公积金个人缴纳额 */
    private BigDecimal reservedFundsOwnPay;
    /* 公积金单位缴纳基数 */
    private BigDecimal reservedFundsOrgBase;
    /* 公积金单位缴纳额 */
    private BigDecimal reservedFundsOrgPay;
    /* 补充公积金缴纳基数 */
    private BigDecimal repReservedFundsRadix;
    /* 补充公积金个人缴纳基数 */
    private BigDecimal repReservedFundsOwnBase;
    /* 补充公积金个人缴纳额 */
    private BigDecimal repReservedFundsOwnPay;
    /* 补充公积金单位缴纳基数 */
    private BigDecimal repReservedFundsOrgBase;
    /* 补充公积金单位缴纳额 */
    private BigDecimal repReservedFundsOrgPay;
    /* 社保缴交id */
    private String insuranceId;
    /* 创建时间 */
    private Date createTime;
    /* 创建人 */
    private String creator;
    /* id */
    private String id;
    /* 修改人 */
    private String modifier;
    /* 修改时间 */
    private Date modifyTime;
    /* pubts */
    private Date pubts;
    /* 租户id */
    private String ytenantId;

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public void setEndowmentInsuranceRadix(BigDecimal endowmentInsuranceRadix) {
        this.endowmentInsuranceRadix = endowmentInsuranceRadix;
    }

    public void setEndowmentInsuranceOwnBase(BigDecimal endowmentInsuranceOwnBase) {
        this.endowmentInsuranceOwnBase = endowmentInsuranceOwnBase;
    }

    public void setEndowmentInsuranceOwnPay(BigDecimal endowmentInsuranceOwnPay) {
        this.endowmentInsuranceOwnPay = endowmentInsuranceOwnPay;
    }

    public void setEndowmentInsuranceOrgBase(BigDecimal endowmentInsuranceOrgBase) {
        this.endowmentInsuranceOrgBase = endowmentInsuranceOrgBase;
    }

    public void setEndowmentInsuranceOrgPay(BigDecimal endowmentInsuranceOrgPay) {
        this.endowmentInsuranceOrgPay = endowmentInsuranceOrgPay;
    }

    public void setForeignerKey(String foreignerKey) {
        this.foreignerKey = foreignerKey;
    }

    public void setUnemploymentRadix(BigDecimal unemploymentRadix) {
        this.unemploymentRadix = unemploymentRadix;
    }

    public void setUnemploymentOwnBase(BigDecimal unemploymentOwnBase) {
        this.unemploymentOwnBase = unemploymentOwnBase;
    }

    public void setUnemploymentOwnPay(BigDecimal unemploymentOwnPay) {
        this.unemploymentOwnPay = unemploymentOwnPay;
    }

    public void setUnemploymentOrgBase(BigDecimal unemploymentOrgBase) {
        this.unemploymentOrgBase = unemploymentOrgBase;
    }

    public void setUnemploymentOrgPay(BigDecimal unemploymentOrgPay) {
        this.unemploymentOrgPay = unemploymentOrgPay;
    }

    public void setOccupationalInjuryRadix(BigDecimal occupationalInjuryRadix) {
        this.occupationalInjuryRadix = occupationalInjuryRadix;
    }

    public void setOccupationalInjuryOwnBase(BigDecimal occupationalInjuryOwnBase) {
        this.occupationalInjuryOwnBase = occupationalInjuryOwnBase;
    }

    public void setOccupationalInjuryOwnPay(BigDecimal occupationalInjuryOwnPay) {
        this.occupationalInjuryOwnPay = occupationalInjuryOwnPay;
    }

    public void setOccupationalInjuryOrgBase(BigDecimal occupationalInjuryOrgBase) {
        this.occupationalInjuryOrgBase = occupationalInjuryOrgBase;
    }

    public void setOccupationalInjuryOrgPay(BigDecimal occupationalInjuryOrgPay) {
        this.occupationalInjuryOrgPay = occupationalInjuryOrgPay;
    }

    public void setMedicalTreatmentRadix(BigDecimal medicalTreatmentRadix) {
        this.medicalTreatmentRadix = medicalTreatmentRadix;
    }

    public void setMedicalTreatmentOwnBase(BigDecimal medicalTreatmentOwnBase) {
        this.medicalTreatmentOwnBase = medicalTreatmentOwnBase;
    }

    public void setMedicalTreatmentOwnPay(BigDecimal medicalTreatmentOwnPay) {
        this.medicalTreatmentOwnPay = medicalTreatmentOwnPay;
    }

    public void setMedicalTreatmentOorgBase(BigDecimal medicalTreatmentOorgBase) {
        this.medicalTreatmentOorgBase = medicalTreatmentOorgBase;
    }

    public void setMedicalTreatmentOorgPay(BigDecimal medicalTreatmentOorgPay) {
        this.medicalTreatmentOorgPay = medicalTreatmentOorgPay;
    }

    public void setRepMedicalTreatmentRadix(BigDecimal repMedicalTreatmentRadix) {
        this.repMedicalTreatmentRadix = repMedicalTreatmentRadix;
    }

    public void setRepMedicalTreatmentOwnBase(BigDecimal repMedicalTreatmentOwnBase) {
        this.repMedicalTreatmentOwnBase = repMedicalTreatmentOwnBase;
    }

    public void setRepMedicalTreatmentOwnPay(BigDecimal repMedicalTreatmentOwnPay) {
        this.repMedicalTreatmentOwnPay = repMedicalTreatmentOwnPay;
    }

    public void setRepMedicalTreatmentOrgBase(BigDecimal repMedicalTreatmentOrgBase) {
        this.repMedicalTreatmentOrgBase = repMedicalTreatmentOrgBase;
    }

    public void setRepMedicalTreatmentOrgPay(BigDecimal repMedicalTreatmentOrgPay) {
        this.repMedicalTreatmentOrgPay = repMedicalTreatmentOrgPay;
    }

    public void setBirthInsuranceRadix(BigDecimal birthInsuranceRadix) {
        this.birthInsuranceRadix = birthInsuranceRadix;
    }

    public void setBirthInsuranceOwnBase(BigDecimal birthInsuranceOwnBase) {
        this.birthInsuranceOwnBase = birthInsuranceOwnBase;
    }

    public void setBirthInsuranceOwnPay(BigDecimal birthInsuranceOwnPay) {
        this.birthInsuranceOwnPay = birthInsuranceOwnPay;
    }

    public void setBirthInsuranceOrgBase(BigDecimal birthInsuranceOrgBase) {
        this.birthInsuranceOrgBase = birthInsuranceOrgBase;
    }

    public void setBirthInsuranceOrgPay(BigDecimal birthInsuranceOrgPay) {
        this.birthInsuranceOrgPay = birthInsuranceOrgPay;
    }

    public void setSeriousIllnessRadix(BigDecimal seriousIllnessRadix) {
        this.seriousIllnessRadix = seriousIllnessRadix;
    }

    public void setSeriousIllnessOwnBase(BigDecimal seriousIllnessOwnBase) {
        this.seriousIllnessOwnBase = seriousIllnessOwnBase;
    }

    public void setSeriousIllnessOwnPay(BigDecimal seriousIllnessOwnPay) {
        this.seriousIllnessOwnPay = seriousIllnessOwnPay;
    }

    public void setSeriousIllnessOrgBase(BigDecimal seriousIllnessOrgBase) {
        this.seriousIllnessOrgBase = seriousIllnessOrgBase;
    }

    public void setSeriousIllnessOrgPay(BigDecimal seriousIllnessOrgPay) {
        this.seriousIllnessOrgPay = seriousIllnessOrgPay;
    }

    public void setReservedFundsRadix(BigDecimal reservedFundsRadix) {
        this.reservedFundsRadix = reservedFundsRadix;
    }

    public void setReservedFundsOwnBase(BigDecimal reservedFundsOwnBase) {
        this.reservedFundsOwnBase = reservedFundsOwnBase;
    }

    public void setReservedFundsOwnPay(BigDecimal reservedFundsOwnPay) {
        this.reservedFundsOwnPay = reservedFundsOwnPay;
    }

    public void setReservedFundsOrgBase(BigDecimal reservedFundsOrgBase) {
        this.reservedFundsOrgBase = reservedFundsOrgBase;
    }

    public void setReservedFundsOrgPay(BigDecimal reservedFundsOrgPay) {
        this.reservedFundsOrgPay = reservedFundsOrgPay;
    }

    public void setRepReservedFundsRadix(BigDecimal repReservedFundsRadix) {
        this.repReservedFundsRadix = repReservedFundsRadix;
    }

    public void setRepReservedFundsOwnBase(BigDecimal repReservedFundsOwnBase) {
        this.repReservedFundsOwnBase = repReservedFundsOwnBase;
    }

    public void setRepReservedFundsOwnPay(BigDecimal repReservedFundsOwnPay) {
        this.repReservedFundsOwnPay = repReservedFundsOwnPay;
    }

    public void setRepReservedFundsOrgBase(BigDecimal repReservedFundsOrgBase) {
        this.repReservedFundsOrgBase = repReservedFundsOrgBase;
    }

    public void setRepReservedFundsOrgPay(BigDecimal repReservedFundsOrgPay) {
        this.repReservedFundsOrgPay = repReservedFundsOrgPay;
    }

    public void setInsuranceId(String insuranceId) {
        this.insuranceId = insuranceId;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public void setPubts(Date pubts) {
        this.pubts = pubts;
    }

    public void setYtenantId(String ytenantId) {
        this.ytenantId = ytenantId;
    }

    public String getStaffId() {
        return staffId;
    }

    public String getOrgId() {
        return orgId;
    }

    public String getDeptId() {
        return deptId;
    }

    public BigDecimal getEndowmentInsuranceRadix() {
        return endowmentInsuranceRadix;
    }

    public BigDecimal getEndowmentInsuranceOwnBase() {
        return endowmentInsuranceOwnBase;
    }

    public BigDecimal getEndowmentInsuranceOwnPay() {
        return endowmentInsuranceOwnPay;
    }

    public BigDecimal getEndowmentInsuranceOrgBase() {
        return endowmentInsuranceOrgBase;
    }

    public BigDecimal getEndowmentInsuranceOrgPay() {
        return endowmentInsuranceOrgPay;
    }

    public String getForeignerKey() {
        return foreignerKey;
    }

    public BigDecimal getUnemploymentRadix() {
        return unemploymentRadix;
    }

    public BigDecimal getUnemploymentOwnBase() {
        return unemploymentOwnBase;
    }

    public BigDecimal getUnemploymentOwnPay() {
        return unemploymentOwnPay;
    }

    public BigDecimal getUnemploymentOrgBase() {
        return unemploymentOrgBase;
    }

    public BigDecimal getUnemploymentOrgPay() {
        return unemploymentOrgPay;
    }

    public BigDecimal getOccupationalInjuryRadix() {
        return occupationalInjuryRadix;
    }

    public BigDecimal getOccupationalInjuryOwnBase() {
        return occupationalInjuryOwnBase;
    }

    public BigDecimal getOccupationalInjuryOwnPay() {
        return occupationalInjuryOwnPay;
    }

    public BigDecimal getOccupationalInjuryOrgBase() {
        return occupationalInjuryOrgBase;
    }

    public BigDecimal getOccupationalInjuryOrgPay() {
        return occupationalInjuryOrgPay;
    }

    public BigDecimal getMedicalTreatmentRadix() {
        return medicalTreatmentRadix;
    }

    public BigDecimal getMedicalTreatmentOwnBase() {
        return medicalTreatmentOwnBase;
    }

    public BigDecimal getMedicalTreatmentOwnPay() {
        return medicalTreatmentOwnPay;
    }

    public BigDecimal getMedicalTreatmentOorgBase() {
        return medicalTreatmentOorgBase;
    }

    public BigDecimal getMedicalTreatmentOorgPay() {
        return medicalTreatmentOorgPay;
    }

    public BigDecimal getRepMedicalTreatmentRadix() {
        return repMedicalTreatmentRadix;
    }

    public BigDecimal getRepMedicalTreatmentOwnBase() {
        return repMedicalTreatmentOwnBase;
    }

    public BigDecimal getRepMedicalTreatmentOwnPay() {
        return repMedicalTreatmentOwnPay;
    }

    public BigDecimal getRepMedicalTreatmentOrgBase() {
        return repMedicalTreatmentOrgBase;
    }

    public BigDecimal getRepMedicalTreatmentOrgPay() {
        return repMedicalTreatmentOrgPay;
    }

    public BigDecimal getBirthInsuranceRadix() {
        return birthInsuranceRadix;
    }

    public BigDecimal getBirthInsuranceOwnBase() {
        return birthInsuranceOwnBase;
    }

    public BigDecimal getBirthInsuranceOwnPay() {
        return birthInsuranceOwnPay;
    }

    public BigDecimal getBirthInsuranceOrgBase() {
        return birthInsuranceOrgBase;
    }

    public BigDecimal getBirthInsuranceOrgPay() {
        return birthInsuranceOrgPay;
    }

    public BigDecimal getSeriousIllnessRadix() {
        return seriousIllnessRadix;
    }

    public BigDecimal getSeriousIllnessOwnBase() {
        return seriousIllnessOwnBase;
    }

    public BigDecimal getSeriousIllnessOwnPay() {
        return seriousIllnessOwnPay;
    }

    public BigDecimal getSeriousIllnessOrgBase() {
        return seriousIllnessOrgBase;
    }

    public BigDecimal getSeriousIllnessOrgPay() {
        return seriousIllnessOrgPay;
    }

    public BigDecimal getReservedFundsRadix() {
        return reservedFundsRadix;
    }

    public BigDecimal getReservedFundsOwnBase() {
        return reservedFundsOwnBase;
    }

    public BigDecimal getReservedFundsOwnPay() {
        return reservedFundsOwnPay;
    }

    public BigDecimal getReservedFundsOrgBase() {
        return reservedFundsOrgBase;
    }

    public BigDecimal getReservedFundsOrgPay() {
        return reservedFundsOrgPay;
    }

    public BigDecimal getRepReservedFundsRadix() {
        return repReservedFundsRadix;
    }

    public BigDecimal getRepReservedFundsOwnBase() {
        return repReservedFundsOwnBase;
    }

    public BigDecimal getRepReservedFundsOwnPay() {
        return repReservedFundsOwnPay;
    }

    public BigDecimal getRepReservedFundsOrgBase() {
        return repReservedFundsOrgBase;
    }

    public BigDecimal getRepReservedFundsOrgPay() {
        return repReservedFundsOrgPay;
    }

    public String getInsuranceId() {
        return insuranceId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getCreator() {
        return creator;
    }

    public String getId() {
        return id;
    }

    public String getModifier() {
        return modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public Date getPubts() {
        return pubts;
    }

    public String getYtenantId() {
        return ytenantId;
    }

    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
