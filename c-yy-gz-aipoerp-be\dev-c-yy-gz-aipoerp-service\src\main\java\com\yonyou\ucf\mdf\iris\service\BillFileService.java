package com.yonyou.ucf.mdf.iris.service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ucf.mdf.iris.enums.SourceBusinessSystem;
import com.yonyou.ucf.mdf.iris.model.FileInfo;
import com.yonyou.ucf.mdf.iris.model.FileNode;
import com.yonyou.ucf.mdf.iris.model.FileProperties;
import com.yonyou.ucf.mdf.iris.util.FileUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @date 2025/4/16 18:03
 * @DESCRIPTION 单据附件服务
 */
@Component
public class BillFileService {
	@Autowired
	private BipOpenApiRequest apiRequest;

	@SneakyThrows
	public List<Map<String, Object>> getFileBase64Maps(String billId, String billType,
			SourceBusinessSystem businessSystem) {
		if (StringUtils.isBlank(billId) || StringUtils.isBlank(billType) || businessSystem == null) {
			throw new RuntimeException("存在空参数");
		}
		String openApiUrl = businessSystem.getOpenApiUrl();
		List<Map<String, Object>> fileBase64Maps = new ArrayList<>();
		Map<String, Object> paramMap = new HashMap<>();
		if (SourceBusinessSystem.PAY_ORDER.equals(businessSystem)) {
			paramMap.put("pk_billtype", billType);
			paramMap.put("billID", billId);
			ResponseResult<List<FileNode>> result = apiRequest.doPost(openApiUrl, paramMap,
					new TypeReference<ResponseResult<List<FileNode>>>() {
					});
			if (result.isSuccess2()) {
				List<FileNode> fileNodes = result.getData();
				if (CollUtil.isNotEmpty(fileNodes)) {
					fileNodes.forEach(fileNode -> traverse(fileBase64Maps, fileNode));
				}
			}
		} else {
			List<String> businessIds = Collections.singletonList(billId);
			paramMap.put("businessIds", businessIds);
			ResponseResult<Map<String, List<FileInfo>>> result = apiRequest.doPost(openApiUrl, paramMap,
					new TypeReference<ResponseResult<Map<String, List<FileInfo>>>>() {
					});
			if (result.isSuccess2()) {
				Map<String, List<FileInfo>> data = result.getData();
				if (CollUtil.isNotEmpty(data)) {
					List<FileInfo> fileInfoList = data.get(billId);
					for (FileInfo fileInfo : fileInfoList) {
						String filePath = fileInfo.getFilePath();
						String base64 = FileUtil.urlToBase64(filePath);
						String name = fileInfo.getName();
						Map<String, Object> map = new HashMap<>();
						if (StringUtils.isNotBlank(name)) {
							String nameWithoutExt = FilenameUtils.removeExtension(name);
							String fileExt = FilenameUtils.getExtension(name);
							String newName = nameWithoutExt + Instant.now().toEpochMilli() + "." + fileExt;
							map.put("file_type", fileExt);
							JSONObject extend_data = new JSONObject();
							extend_data.put("fileName", newName);
							map.put("extend_data", extend_data);
						}
						map.put("file_content", base64);
						fileBase64Maps.add(map);
					}
				}
			}
		}
		return fileBase64Maps.stream().distinct().collect(Collectors.toList());
	}

	@SneakyThrows
	public void traverse(List<Map<String, Object>> mapList, FileNode node) {
		if (!node.getLeaf()) {
			for (FileNode child : node.getChildren()) {
				traverse(mapList, child); // 递归处理子节点
			}
		} else {
			FileProperties properties = node.getProperties();
			String urlForPreview = properties.getDownloadUrlForPreview();
			if (StringUtils.isNotBlank(urlForPreview)) {
				Map<String, Object> map = new HashMap<>();
				String name = node.getText();
				if (StringUtils.isNotBlank(name)) {
					String nameWithoutExt = FilenameUtils.removeExtension(name);
					String fileExt = FilenameUtils.getExtension(name);
					String newName = nameWithoutExt + Instant.now().toEpochMilli() + "." + fileExt;
					map.put("file_type", fileExt);
					JSONObject extend_data = new JSONObject();
					extend_data.put("fileName", newName);
					map.put("extend_data", extend_data);
				}
				String base64 = FileUtil.urlToBase64(urlForPreview);
				map.put("file_content", base64);
				mapList.add(map);
			}
		}
	}
}
