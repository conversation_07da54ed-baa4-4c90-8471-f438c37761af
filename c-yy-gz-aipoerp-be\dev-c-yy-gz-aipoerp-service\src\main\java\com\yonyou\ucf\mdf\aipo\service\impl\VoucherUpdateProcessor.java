package com.yonyou.ucf.mdf.aipo.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.ColumnProcessor;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.aipo.dto.MsgForwardDto;
import com.yonyou.ucf.mdf.aipo.model.AIPOVoucherEventLog;
import com.yonyou.ucf.mdf.aipo.model.EventContent;
import com.yonyou.ucf.mdf.aipo.model.EventProcessResult;
import com.yonyou.ucf.mdf.aipo.model.EventProcessStatus;
import com.yonyou.ucf.mdf.aipo.model.EventType;
import com.yonyou.ucf.mdf.aipo.model.VoucherEventLogDetail;
import com.yonyou.ucf.mdf.aipo.service.AIPOVoucherEventLogService;
import com.yonyou.ucf.mdf.aipo.service.IAipoVoucherSyncLogService;
import com.yonyou.ucf.mdf.aipo.service.IMsgForwardService;
import com.yonyou.ucf.mdf.aipo.service.VoucherEventProcessor;
import com.yonyou.ucf.mdf.aipo.utils.BipApiInvokerUtils;
import com.yonyou.ucf.mdf.aipo.utils.DateTimeFormatterUtil;
import com.yonyou.ucf.mdf.aipo.vo.AIPOVoucherSyncLog;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 凭证修改事件处理器
 */
@Slf4j
@Component
public class VoucherUpdateProcessor extends VoucherEventProcessor {

	// 常量定义
	private static final String SUCCESS_CODE = "200";
	private static final int MAX_STACK_TRACE_LENGTH = 4000;
	private static final String VOUCHER_SYNC_UPDATE = "凭证同步-修改";

	@Autowired
	private AIPOVoucherEventLogService eventLogService;
	@Autowired
	private IAipoVoucherSyncLogService syncLogService;
	@Autowired
	private IMsgForwardService msgForwardService;

	@Autowired
	private IBillRepository billRepository;

	public VoucherUpdateProcessor() {
		super(EventType.GL_VOUCHER_EVENT_UPDATE_AFTER);
	}

	@SuppressWarnings("unchecked")
	@Override
	protected void doProcessEvent(EventContent eventContent) {
		long start = System.currentTimeMillis();
		String content = eventContent.getContent();
		log.error("处理凭证修改事件，内容：{}", content);
		AIPOVoucherEventLog eventLog = getEventLog(eventContent);
		if (eventLog == null) {
			return;
		}
		// 先保存事件日志
		eventLog = eventLogService.save(eventLog);
		try {
			boolean successFlag = true;
			List<VoucherEventLogDetail> details = eventLog.getVoucherEventLogDetailList();
			for (VoucherEventLogDetail voucherEventLogDetail : details) {
				long begin = System.currentTimeMillis();
				AIPOVoucherSyncLog syncLog = getVoucherSyncLog(eventLog, voucherEventLogDetail);
				try {

					syncLog = syncLogService.save(syncLog);

					JSONObject voucher = queryVoucherData(voucherEventLogDetail.getBillid(),
							voucherEventLogDetail.getYtenantId(), syncLog.getBillcode(), syncLog.getVoucherperiod());

					// 获取并删除凭证账簿信息（这个只是日志记录需要用到，并不影响凭证同步）
					Map<String, Object> accbookMap = (Map<String, Object>) voucher.remove("accbookMap");
					// 设置同步日志凭证账簿信息
					setSyncLogAccbookInfo(syncLog, accbookMap);

					// 处理制单人手机号
					dealMakerMobile(voucher);

					// 处理业务伙伴辅助信息
					processBodyArray(voucher);

					// 封装同步到NCC高级版的凭证参数
					MsgForwardDto msgForwardDto = buildMsgForwardDto(voucher);

					// 推送前设置日志推送数据信息
					syncLog.setSendmsg(voucher.toJSONString());
					// 调用接口同步凭证到NCC高级版
					String req = msgForwardService.msgForward(msgForwardDto);
					// 推送后日志记录返回信息
					syncLog.setRetmsg(req);

					// 校验同步返回信息
					validateResponse(req);

					// 设置日志同步成功标识
					syncLog.setIssuccess(EventProcessResult.Y.getCode());
					syncLog.set_status(ActionEnum.UPDATE.getValueInt());

					// 记录事件明细处理结果和状态，以及耗费时间
					long end = System.currentTimeMillis();
					long costTime = end - begin;
					voucherEventLogDetail.setCostTime((int) costTime);
					voucherEventLogDetail.setRequestData(syncLog.getSendmsg());
					voucherEventLogDetail.setResponseData(syncLog.getRetmsg());
					voucherEventLogDetail.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
					voucherEventLogDetail.setProcessResult(EventProcessResult.Y.getCode());
					voucherEventLogDetail.set_status(ActionEnum.UPDATE.getValueInt());
				} catch (Exception e) {
					log.error("凭证同步失败：{}", e.getMessage(), e);
					// 记录存在失败的处理
					successFlag = false;
					String stackTrace = ExceptionUtils.getStackTrace(e);
					if (stackTrace != null && stackTrace.length() > MAX_STACK_TRACE_LENGTH) {
						stackTrace = stackTrace.substring(0, MAX_STACK_TRACE_LENGTH - 2);
					}

					// 设置日志记录异常信息，并更新同步结果为失败
					syncLog.setIssuccess(EventProcessResult.N.getCode());
					syncLog.setErrormsg(e.getMessage());
					syncLog.set_status(ActionEnum.UPDATE.getValueInt());

					// 记录事件明细处理结果和状态，以及耗费时间
					long end = System.currentTimeMillis();
					long costTime = end - begin;
					voucherEventLogDetail.setCostTime((int) costTime);
					voucherEventLogDetail.setErrorMsg(e.getMessage());
					voucherEventLogDetail.setProcessResult(EventProcessResult.N.getCode());
					voucherEventLogDetail.setRequestData(syncLog.getSendmsg());
					voucherEventLogDetail.setResponseData(syncLog.getRetmsg());
					voucherEventLogDetail.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
					voucherEventLogDetail.setExceptionStack(stackTrace); // 记录异常堆栈信息
					voucherEventLogDetail.set_status(ActionEnum.UPDATE.getValueInt());

				} finally {
					// 最后一定要保存每一条同步日志记录
					syncLogService.save(syncLog);
					// 更新凭证同步状态
					updateVoucherStatus(syncLog.getBillid(), syncLog.getErrormsg() , syncLog.getIssuccess());
				}

			}

			// 记录整个事件处理耗费时间
			long end = System.currentTimeMillis();
			long costTime = end - start;
			eventLog.setCostTime((int) costTime);
			if (successFlag) { // 如果所有明细都正常同步成功，则整体时间处理成功
				eventLog.setProcessResult(EventProcessResult.Y.getCode());
			} else { // 否则算处理失败
				eventLog.setProcessResult(EventProcessResult.N.getCode());
			}
			eventLog.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
			eventLog.set_status(ActionEnum.UPDATE.getValueInt());

		} catch (Exception e) {
			log.error("处理凭证修改事件失败", e);

			// 如果事件处理发生异常，记录异常信息
			long end = System.currentTimeMillis();
			long costTime = end - start;
			eventLog.setCostTime((int) costTime);
			eventLog.setProcessResult(EventProcessResult.N.getCode());
			eventLog.setProcessStatus(EventProcessStatus.PROCESSED.getCode());
			eventLog.setErrorMsg(e.getMessage());
			eventLog.set_status(ActionEnum.UPDATE.getValueInt());
		} finally {
			// 最后保存整个事件处理日志记录（包含事件明细处理记录）
			eventLogService.save(eventLog);
		}
	}

	/**
	 * 更新凭证状态
	 */
	private void updateVoucherStatus(String voucherId, String errorMsg, String isSuccess) {
		try {
			String sql = "UPDATE figl.fi_voucher set def25 = ?, def26 = ? WHERE id = ?";
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(errorMsg);
			parameter.addParam(isSuccess);
			parameter.addParam(voucherId);
			billRepository.update(sql, parameter);
		} catch (Exception e) {
			log.error("更新凭证状态异常", e);
		}
	}

	/**
	 * 设置同步日志凭证账簿信息
	 * 
	 * @param syncLog
	 * @param accbookMap
	 */
	private void setSyncLogAccbookInfo(AIPOVoucherSyncLog syncLog, Map<String, Object> accbookMap) {
		syncLog.setAcccode(accbookMap.getOrDefault("acccode", "").toString());
		syncLog.setAccname(accbookMap.getOrDefault("acccname", "").toString());
		syncLog.setVouchertypecode(accbookMap.getOrDefault("vouchertypecode", "").toString());
	}

	/**
	 * 查询凭证数据
	 * 
	 * @param voucherId 凭证id
	 * @param tenantId  租户
	 * @param billcode  凭证号
	 * @param period    期间
	 * @return
	 * @throws Exception
	 */
	private JSONObject queryVoucherData(String voucherId, String tenantId, String billcode, String period)
			throws Exception {

		// 1、先根据凭证id和租户id查询出凭证对应的账簿信息
		List<Map<String, Object>> voucherAccbookMap = getVoucherAccbookMap(voucherId, tenantId);
		Map<String, Object> accbookMap = voucherAccbookMap.get(0);
		String voucherTypeCode = accbookMap.get("vouchertypecode").toString();
		String accCode = accbookMap.get("acccode").toString();

		// 2、封装凭证接口查询参数
		JSONObject voucherQueryMap = buildVoucherQueryMap(voucherTypeCode, accCode, billcode, period);

		// 3、调用凭证接口查询方法
		JSONObject voucherQueryResult = queryVoucher(voucherQueryMap);
		// 把凭证账簿信息页一并返回
		voucherQueryResult.put("accbookMap", accbookMap);

		return voucherQueryResult;
	}

	/**
	 * 查询凭证
	 * 
	 * @param voucherQueryMap
	 * @return
	 * @throws Exception
	 */
	private JSONObject queryVoucher(JSONObject voucherQueryMap) throws Exception {
		String voucherQueryResultStr = queryVoucherData(voucherQueryMap);
		JSONObject jsonQueryRes = JSONObject.parseObject(voucherQueryResultStr);
		if (!isValidVoucherQueryResult(jsonQueryRes)) {
			throw new RuntimeException(String.format("查询凭证返回数据格式错误！返回数据：%s", voucherQueryResultStr));
		}
		JSONObject recordJSONObject = jsonQueryRes.getJSONObject("data").getJSONArray("recordList").getJSONObject(0);

		return recordJSONObject;
	}

	/**
	 * 验证凭证查询结果
	 */
	private boolean isValidVoucherQueryResult(JSONObject jsonQueryRes) {
		return ObjectUtil.isNotEmpty(jsonQueryRes) && ObjectUtil.isNotEmpty(jsonQueryRes.getJSONObject("data"))
				&& ObjectUtil.isNotEmpty(jsonQueryRes.getJSONObject("data").getJSONArray("recordList"));
	}

	/**
	 * 处理业务伙伴辅助信息
	 */
	private void processBodyArray(JSONObject recordJSONObject) {
		JSONArray bodyArray = recordJSONObject.getJSONArray("body");
		if (bodyArray != null && bodyArray.size() > 0) {
			for (int j = 0; j < bodyArray.size(); j++) {
				JSONObject bodyJSON = bodyArray.getJSONObject(j);
				dealClientauxiliary(bodyJSON);
			}
		}
	}

	/**
	 * 处理业务伙伴信息
	 */
	private void dealClientauxiliary(JSONObject bodyJSON) {
		JSONArray clientauxiliaryJsonArray = bodyJSON.getJSONArray("clientauxiliary");
		if (clientauxiliaryJsonArray != null && clientauxiliaryJsonArray.size() > 0) {
			for (int k = 0; k < clientauxiliaryJsonArray.size(); k++) {
				JSONObject clientauxiliaryJsonObject = clientauxiliaryJsonArray.getJSONObject(k);
				String clientauxiliaryCode = clientauxiliaryJsonObject.getString("code");
				String clientauxiliaryValue = clientauxiliaryJsonObject.getString("value");
				if ("0004".equals(clientauxiliaryCode)) {
					processBusinessPartner(clientauxiliaryJsonObject, clientauxiliaryValue);
				}
			}
		}
	}

	/**
	 * 处理业务伙伴信息
	 */
	private void processBusinessPartner(JSONObject clientauxiliaryJsonObject, String clientauxiliaryValue) {
		String queryMerSql = "select partnerid,CAST( merchant_id AS CHAR)  as merid,mt.cCode as code ,mt.cName as name from iuap_apdoc_coredoc.base_partnermerchantcomparison bn \n"
				+ "left join iuap_apdoc_coredoc.merchant  mt on mt.id = bn.merchant_id \n"
				+ "where partnerId = ? and bn.dr = 0 union all \n"
				+ "select partnerid,CAST(vendor_id AS CHAR)  as merid,ar.code,ar.name from iuap_apdoc_coredoc.base_partnervendorcomparison vn \n"
				+ "left join iuap_apdoc_coredoc.aa_vendor ar on ar.id = vn.vendor_id \n"
				+ "where partnerId = ? and vn.dr = 0 \n";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(clientauxiliaryValue);
		parameter.addParam(clientauxiliaryValue);
		List<Map<String, Object>> merMapList = billRepository.queryForList(queryMerSql, parameter,
				new MapListProcessor());
		if (merMapList != null && merMapList.size() > 0) {
			Map<String, Object> map = merMapList.get(0);
			Object mercode = map.get("code");
			Object mername = map.get("name");
			Object merid = map.get("merid");
			clientauxiliaryJsonObject.getJSONObject("data").put("code", mercode);
			clientauxiliaryJsonObject.getJSONObject("data").put("name", mername);
			clientauxiliaryJsonObject.getJSONObject("data").put("id", merid);
		} else {
			log.error("未找到对应的业务伙伴信息");
			throw new RuntimeException("未找到对应的业务伙伴数据:" + queryMerSql);
		}
	}

	/**
	 * 处理制单人手机号
	 */
	private void dealMakerMobile(JSONObject recordJSONObject) {
		JSONObject header = recordJSONObject.getJSONObject("header");
		JSONObject maker = header.getJSONObject("maker");
		String makerId = maker.getString("id");
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(makerId);
		String sql = " select mobile from iuap_apcom_auth.ba_user where id = ? ";
		Object makerMobile = billRepository.queryForObject(sql, parameter, new ColumnProcessor());
		recordJSONObject.getJSONObject("header").getJSONObject("maker").put("mobile", makerMobile);
	}

	/**
	 * 构建消息转发对象
	 */
	private MsgForwardDto buildMsgForwardDto(JSONObject recordJSONObject) {
		MsgForwardDto msgForwardDto = new MsgForwardDto();
		msgForwardDto.setReqType("post");
		msgForwardDto.setUrl("pz_add");
		msgForwardDto.setData(recordJSONObject);
		return msgForwardDto;
	}

	/**
	 * 验证响应结果
	 */
	private void validateResponse(String response) {
		JSONObject jsonObject = JSONObject.parseObject(response);
		if (!SUCCESS_CODE.equals(jsonObject.getString("code"))) {
			throw new RuntimeException("同步失败:" + jsonObject.getString("message"));
		}
	}

	/**
	 * 查询凭证数据
	 * 
	 * @throws Exception
	 */
	private String queryVoucherData(JSONObject voucherQueryMap) throws Exception {
		return BipApiInvokerUtils.postSystemBipApi("/yonbip/fi/ficloud/openapi/voucher/queryVouchers",
				voucherQueryMap.toJSONString());
	}

	/**
	 * 构建凭证查询参数
	 */
	private JSONObject buildVoucherQueryMap(String voucherTypeCode, String accCode, String billCode,
			String periodunion) {
		JSONObject voucherQueryMap = new JSONObject();
		List<String> typeList = new ArrayList<>();
		typeList.add(voucherTypeCode);
		voucherQueryMap.put("voucherTypeCodeList", typeList);
		voucherQueryMap.put("accbookCode", accCode);
		voucherQueryMap.put("billcodeMin", billCode);
		voucherQueryMap.put("billcodeMax", billCode);
		voucherQueryMap.put("periodStart", periodunion);
		voucherQueryMap.put("periodEnd", periodunion);
		return voucherQueryMap;
	}

	/**
	 * 获取凭证账簿信息和凭证类型编号
	 */
	private List<Map<String, Object>> getVoucherAccbookMap(String voucherId, String tenantId) {
		String sql = " select cc.code as vouchertypecode,aa.id as voucherid ,bb.code as acccode,bb.name as acccname ,aa.billcode    from figl.fi_voucher aa "
				+ " left join fiepub.epub_accountbook bb on aa.accbook = bb.id \n"
				+ " left join fiepub.epub_vouchertype cc on cc.id = aa.vouchertype where aa.id = ? and aa.tenantid = ? ";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(voucherId);
		parameter.addParam(tenantId);

		List<Map<String, Object>> voucherMap = billRepository.queryForList(sql, parameter, new MapListProcessor());

		if (CollectionUtils.isEmpty(voucherMap)) {
			log.error("根据凭证id：{}，tenantId：{}，未查询到凭证信息！", voucherId, tenantId);
			String msg = String.format("根据凭证id：%s，tenantId：%s，未查询到凭证信息！", voucherId, tenantId);
			throw new RuntimeException(msg);
		}

		return voucherMap;
	}

	/**
	 * 生成同步日志
	 * 
	 * @param eventLog
	 * @param voucherEventLogDetail
	 * @return
	 */
	private AIPOVoucherSyncLog getVoucherSyncLog(AIPOVoucherEventLog eventLog,
			VoucherEventLogDetail voucherEventLogDetail) {
		AIPOVoucherSyncLog syncLog = new AIPOVoucherSyncLog();
		syncLog.setEntertime(DateTimeFormatterUtil.hmsDate(LocalDateTime.now()));
		syncLog.setYtenantId(eventLog.getYtenantId());
		syncLog.setDef1(eventLog.getBusinessDate());
		syncLog.setEventid(eventLog.getEventid());
		syncLog.setBillid(voucherEventLogDetail.getBillid());
		syncLog.setBillcode(voucherEventLogDetail.getBillcode());
		syncLog.setVoucherperiod(voucherEventLogDetail.getVoucherperiod());
		syncLog.setSynctype(VOUCHER_SYNC_UPDATE);
		syncLog.setSyncdata(eventLog.getEventType());
		syncLog.setIssuccess(EventProcessResult.Y.getCode());
		syncLog.set_status(ActionEnum.INSERT.getValueInt());
		return syncLog;
	}

	/**
	 * 获取事件日志，先查询是否已存在事件
	 * 
	 * @param eventContent
	 * @return
	 */
	private AIPOVoucherEventLog getEventLog(EventContent eventContent) {
		String eventId = eventContent.getEventId();
		AIPOVoucherEventLog eventLogVO = eventLogService.findByEventId(eventId);
		if (eventLogVO == null) {
			eventLogVO = initializeVoucherEventLogVO(eventContent);
		} else {
			if (EventProcessResult.Y.getCode().equals(eventLogVO.getProcessResult())) {
				log.error("事件类型：{}，eventid：{},已经处理成功，无需处理！");
				return null;
			}
			if (EventProcessStatus.PROCESSING.getCode().equals(eventLogVO.getProcessStatus())) {
				log.error("事件类型：{}，eventid：{},已经在处理中，无需处理！");
				return null;
			}
			eventLogVO.set_status(ActionEnum.UPDATE.getValueInt());
			eventLogVO.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
			eventLogVO.setProcessResult(null);
			eventLogVO.setCostTime(null);
			eventLogVO.setProcessCount(eventLogVO.getProcessCount() + 1);
			// 过滤出未处理成功的事件明细
			List<VoucherEventLogDetail> details = eventLogVO.getVoucherEventLogDetailList();
			details = details.stream().filter(d -> !EventProcessResult.Y.getCode().equals(d.getProcessResult())
					&& !EventProcessStatus.PROCESSING.getCode().equals(d.getProcessStatus())).map(d -> {
						d.set_status(ActionEnum.UPDATE.getValueInt());
						d.setProcessResult(null);
						d.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
						d.setCostTime(null);
						d.setProcessCount(d.getProcessCount() + 1);
						return d;
					}).collect(Collectors.toList());

			eventLogVO.setVoucherEventLogDetailList(details);

		}
		return eventLogVO;
	}

	/**
	 * 初始化事件日志
	 * 
	 * @param eventContent
	 * @return
	 */
	private AIPOVoucherEventLog initializeVoucherEventLogVO(EventContent eventContent) {
		String content = eventContent.getContent();
		JSONObject contentJson = JSONObject.parseObject(content);
		JSONArray voucehrVOArray = contentJson.getJSONArray("voucherVO");
		if (voucehrVOArray == null || voucehrVOArray.isEmpty()) {
			log.info("凭证数据为空，无需处理");
			return null;
		}
		AIPOVoucherEventLog voucherEventLogVO = new AIPOVoucherEventLog();
		voucherEventLogVO.set_status(ActionEnum.INSERT.getValueInt());
		voucherEventLogVO.setEventContent(JSONObject.toJSONString(eventContent));
		voucherEventLogVO.setEventid(eventContent.getEventId());
		voucherEventLogVO.setEventType(eventContent.getType().toString());
		voucherEventLogVO.setYtenantId(
				eventContent.getTenantId() == null ? InvocationInfoProxy.getTenantid() : eventContent.getTenantId());
		voucherEventLogVO.setEventValid("Y");
		voucherEventLogVO.setCreateTime(new Date());
		Long timestamp = eventContent.getTimestamp();
		if (timestamp != null) {
			voucherEventLogVO.setBusinessDate(DateTimeFormatterUtil.ymdSpeDate(timestamp));
		} else {
			voucherEventLogVO.setBusinessDate(DateTimeFormatterUtil.hmsDate(LocalDateTime.now()));
		}
		voucherEventLogVO.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
		voucherEventLogVO.setProcessCount(1);
		List<VoucherEventLogDetail> details = Lists.newArrayList();
		for (int i = 0; i < voucehrVOArray.size(); i++) {
			JSONObject voucher = voucehrVOArray.getJSONObject(i);
			VoucherEventLogDetail voucherEventLogDetailVO = new VoucherEventLogDetail();
			voucherEventLogDetailVO.set_status(ActionEnum.INSERT.getValueInt());
			voucherEventLogDetailVO.setBillid(voucher.getString("id"));
			voucherEventLogDetailVO.setBillcode(voucher.getString("billcode"));
			voucherEventLogDetailVO.setVoucherperiod(voucher.getString("periodunion"));
			voucherEventLogDetailVO.setAccbookId(voucher.getString("accbook"));
			voucherEventLogDetailVO.setProcessStatus(EventProcessStatus.PROCESSING.getCode());
			voucherEventLogDetailVO.setProcessCount(1);
			voucherEventLogDetailVO.setCreateTime(voucherEventLogVO.getCreateTime());
			voucherEventLogDetailVO.setYtenantId(voucherEventLogVO.getYtenantId());
			details.add(voucherEventLogDetailVO);
		}
		voucherEventLogVO.setVoucherEventLogDetailList(details);
		return voucherEventLogVO;
	}
}
