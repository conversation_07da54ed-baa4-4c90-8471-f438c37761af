{"version": 3, "file": "./javascripts/extend.603.b52fd227.min.js", "mappings": "qGAMAA,GAAGC,OAAOC,kBAA2B,IAAI,WACrC,IAAIC,EAAoC,CACpCC,SAAU,SAAUC,EAAMC,GAClBC,KAAKF,IACLE,KAAKF,GAAMC,EACnB,EACAE,KAAM,SAAUF,GACZG,MAAM,gBAGNH,EAAUI,IAAI,eAAeC,GAAG,SAAS,WAEzC,GACJ,GAEJ,IACIC,EAAOC,QAAUV,CACrB,CAAE,MAAOW,GAET,CACA,OAAOX,CACX,G,kBCrBAH,GAAGC,OAAOC,mBAA4B,WAClC,IAAIa,EAAgC,CAChCX,SAAU,SAAUC,EAAMC,GAClBC,KAAKF,IACLE,KAAKF,GAAMC,EACnB,EACAE,KAAM,SAAUF,GACZG,MAAM,eAIV,GAEJ,IACIG,EAAOC,QAAUE,CACrB,CAAE,MAAOD,GAET,CACA,OAAOC,CACX,G,kBCzBAf,GAAGC,OAAOC,kBAA2B,CAAC,+BAA+B,SAAUc,GAC7E,IAAIC,EAA0B,CAC5Bb,SAAU,SAAUC,EAAMa,GACpBX,KAAKF,IACPE,KAAKF,GAAMa,EACf,EACAV,KAAM,SAAUU,GAEd,IAAIC,GAAU,EACVC,EAAY,CAAC,EACbC,EAAeH,EAAUR,IAAI,WACP,UAAtBV,GAAGsB,KAAKC,WAAwD,WAA/BL,EAAUM,YAAYC,OACzDT,EAAOU,eAAeL,EAAcH,EAAW,OAC/CG,EAAaV,GAAG,mBAAmB,WACjCK,EAAOU,eAAeL,EAAcH,EAAW,MACjD,KAGFF,EAAOW,YAAYN,EAAcH,GAGjCF,EAAOY,iBAAiBP,EAAcH,EAAW,OAGjDF,EAAOa,iBAAiBX,GACxBF,EAAOc,eAAeZ,GACtB,IAAIa,EAAkB,IAAI/B,GAAGgC,OAAOC,YAAY,CAAEC,WAAW,IAC7DhB,EAAUiB,YAAY,aAAcJ,GAEpCA,EAAgBpB,GAAG,UAAU,SAAUyB,GACrCC,EAAWD,EACb,IAEA,IAAIE,EAAa,GACbD,EAAa,SAAUE,GACzB,GAAgB,GAAZA,EAAkB,CAEpB,IAAIC,EAAOnB,EAAaoB,UAEpBC,EAAU,GACdF,EAAKG,SAAQ,SAAUC,GACjBA,EAAKC,QAAUD,EAAKE,KACtBJ,EAAQK,KAAKH,EAEjB,IACAvB,EAAa2B,cAAcN,EAC7B,MAEErB,EAAa2B,cAAcV,EAE/B,EAEIW,EAAiB,IAAIjD,GAAGgC,OAAOkB,UAAU,CAC3C,QAAW,CACT,QAAW,CACT,UAAa,UACb,aAAgB,KAChB,SAAW,EACX,YAAc,EACd,UAAa,IACb,SAAW,EACX,aAAgB,SAElB,QAAW,CACT,UAAa,UACb,aAAgB,OAChB,SAAW,EACX,YAAc,EACd,UAAa,IACb,SAAW,EACX,aAAgB,SAElB,QAAW,CACT,UAAa,UACb,aAAgB,OAChB,SAAW,EACX,YAAc,EACd,UAAa,IACb,SAAW,EACX,aAAgB,SAElB,WAAc,CACZ,UAAa,aACb,aAAgB,OAChB,SAAW,EACX,YAAc,EACd,UAAa,GACb,SAAW,EACX,aAAgB,SAElB,OAAU,CACR,UAAa,SACb,aAAgB,OAChB,SAAW,EACX,YAAc,EACd,UAAa,IACb,SAAW,EACX,aAAgB,UAGpB,cAAgB,EAChB,WAAa,EACb,gBAAkB,EAClB,mBAAqB,EACrB,YAAc,EACd,SAAW,EACX,YAAc,IAEhBhC,EAAUiB,YAAY,QAASc,GAW/B/B,EAAUR,IAAI,WAAWC,GAAG,SAAS,SAAUwC,GAK7C,IAAIC,GAAc,EACdlC,EAAUR,IAAI,gBAChB0C,EAAclC,EAAUR,IAAI,cAAc2C,YAI5C,IAAIC,EAKU,aALVA,EAOO,UAPPA,EAQW,cARXA,EASW,cATXA,EAaI,OAKJC,EAAcrC,EAAUR,IAAI,eAAe2C,WAC3CG,EAAgBtC,EAAUR,IAAI,6BAA6B2C,WAC3DI,EAAgBN,GAAcjC,EAAUR,IAAI,WAAW2C,WAE3D,GADAI,EAAgBA,EAAcC,OACzBxC,EAAUyC,gBAAf,CAIA,IAmIIC,EAAe,SAAUC,EAAaC,EAAUC,GAC7CA,IAAYA,EAAa,GAC9B,IACIC,EAAU3C,EAAa4C,aAAaJ,EAAaC,GAOrD,OANI,MAAQE,QAAuBE,IAAZF,EACPD,EAGAI,OAAOH,GAAWD,CAGpC,EAoDA,GAAIX,EAAa,CACf,IAjPiB,SAAUlC,GAC7B,QAAKA,EAAUR,IAAI,eAAe2C,aAChCrD,GAAGoE,MAAM3D,MAAM,UAAW,YACnB,EAGX,CA2OS4D,CAAenD,GAElB,YADAA,EAAUR,IAAI,WAAW4D,SAAS,MAGpC,IAAKd,EAAe,CAClB,IAAIe,EAxMU,SAAUlD,EAAcmD,GAGxC,IAFA,IAAIhC,EAAOnB,EAAaoB,UAEfgC,EAAI,EAAGA,EAAIjC,EAAKkC,OAAQD,IAAK,CACpC,IAAIE,EAAMnC,EAAKiC,GAAc,iBAC7B,GAAIE,EAEF,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAID,OAAQE,IAC9B,GAAIJ,GAAOG,EAAIC,GAAGC,GAGhB,OAFyB,MAAfrC,EAAKiC,GAAG3B,IAAc,EAAIN,EAAKiC,GAAG3B,KAElC,EADGN,EAAKiC,GAAG5B,QACS,GAE9BxB,EAAayD,OAAOL,GACbA,EAIf,CACA,OAAQ,CACV,CAoLmBM,CAAY1D,EAAcoC,GACzC,IAAiB,GAAbc,EAMFS,EAAkBvB,EAAewB,MAAO,QACnC,IAAiB,GAAbV,EAETS,EAAkBvB,EAAewB,MAAO,OACnC,CAEL,IAAInC,EAAMc,EAAaW,EAAU,MAAO,GACxClD,EAAa6D,aAAaX,EAAU,MAAOzB,GAAK,GAAM,EACxD,CAEA,YADA5B,EAAUR,IAAI,WAAW4D,SAAS,KAEpC,CACF,CAEA,IAAIa,EAAoBjE,EAAUkE,SAAS,CACzCC,OAAQ,CACNC,IAAK,gCACLC,OAAQ,UAIRC,EAAS,CACXC,SAAU,EACVC,QAASjC,EACTkC,QAASzE,EAAU0E,iBAAiBC,QACpCC,SAAU1C,EAAc,SAAW,GACnCG,YAAaA,EACbC,cAAeA,GAGbC,GAAiBpC,GACnB8D,EAAkBE,OAAOG,GAAQ,SAACO,EAAKC,GACrC,GAAID,EAGF,OAFA/F,GAAGoE,MAAM3D,MAAMsF,EAAIE,QAAS,cAC5B/E,EAAUR,IAAI,WAAW4D,SAAS,MAIpC,QAAeJ,IAAX8B,GAAkD,MAA1BE,KAAKC,UAAUH,GAOzC,OAFAhB,EAAkBvB,EAAe,KAAM,QACvCvC,EAAUR,IAAI,WAAW4D,SAAS,MAE7B,GAAsB,eAAlB0B,EAAOI,QAGhB,OAFApB,EAAkBvB,EAAe,KAAM,QACvCvC,EAAUR,IAAI,WAAW4D,SAAS,MAIpC,IAKIT,EALAiC,EAAWE,EAAOF,SAClBb,EAAQe,EAAO7C,KAAK,GACpBkD,EAAgBpB,EAAMoB,cACtBC,EAAYrB,EAAMsB,YAAY,GAAKC,MAIvC3C,EApPU,SAAUxC,EAAcoF,EAAYJ,EAAepB,EAAOa,GAItE,IAHA,IAAIvB,GAAY,EACZ/B,EAAOnB,EAAaoB,UACpBiE,EAAYlE,EAAKkC,OACZD,EAAI,EAAGA,EAAIiC,EAAWjC,IAC7B,GAAuB,UAAnBjC,EAAKiC,GAAGkC,SAGRtF,EAAa4C,aAAaQ,EAAGnB,IAAgB2B,EAAM2B,QAAvD,CAIA,GAAI3B,EAAMoB,cACR,GAAIpB,EAAMsB,YAAY,GAAI,CACxB,GAAItB,EAAMsB,YAAY,GAAGM,SAAWxF,EAAa4C,aAAaQ,EAAGnB,IAAmB2B,EAAMsB,YAAY,GAAGM,QACvG,SAEF,GAAI5B,EAAM6B,mBAAoB,CAC5B,GAAI7B,EAAMsB,YAAY,GAAGQ,aAAe1F,EAAa4C,aAAaQ,EAAGnB,IAAuB2B,EAAMsB,YAAY,GAAGQ,YAC/G,SAEF,GAAI9B,EAAMsB,YAAY,GAAGS,aAAe3F,EAAa4C,aAAaQ,EAAGnB,IAAuB2B,EAAMsB,YAAY,GAAGS,YAC/G,QAEJ,CACF,KAAO,CACL,GAAI/B,EAAM4B,SAAWxF,EAAa4C,aAAaQ,EAAGnB,IAAmB2B,EAAM4B,QACzE,SAEF,GAAI5B,EAAM6B,mBAAoB,CAC5B,GAAI7B,EAAM8B,aAAe1F,EAAa4C,aAAaQ,EAAGnB,IAAuB2B,EAAM8B,YACjF,SAEF,GAAI9B,EAAM+B,aAAe3F,EAAa4C,aAAaQ,EAAGnB,IAAuB2B,EAAM+B,YACjF,QAEJ,CACF,CAIF,IAAIlE,EAAqB,MAAfN,EAAKiC,GAAG3B,IAAc,EAAIN,EAAKiC,GAAG3B,IACxCD,EAASL,EAAKiC,GAAG5B,OACjBkB,EAAakB,EAAMgC,SAClBlD,IAAYA,EAAa,GAC9BjB,EAAMqB,OAAOrB,GAAOqB,OAAOJ,GAC3B,IAAImD,EAAQlH,GAAGsB,KAAK6F,WAAWC,OAAOC,gBACtCvE,EAAMqB,OAAOrB,EAAIwE,QAAQJ,IACzBrE,EAASsB,OAAOtB,EAAOyE,QAAQJ,IAC/B,IAAI1C,EAAMf,EACV,GAAgB,MAAZqC,EAAkB,CAEpB,GAAI1E,EAAUoD,GAEZ,OADAxE,GAAGoE,MAAM3D,MAAM,YAAc+D,EAAM,MAC3B,EAIV,IAAI+C,EAAc,mBACd5C,EAAMnC,EAAKiC,GAAG8C,GAElB,GAAI5C,EAEF,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAID,OAAQE,IAC9B,GAAIJ,GAAOG,EAAIC,GAAGC,GAAI,CAKpB3D,EAAUR,IAAI6G,GAAarC,aAAaN,EAAG,YAAa,GAAG,GAAM,GACjExD,EAAUoD,IAAO,EACjB,KACF,CAGJ,IAAKpD,EAAUoD,GAAM,QAEvB,CAEA,GAAIiC,GAAcpF,EAAa4C,aAAaQ,EAAGnB,GAAoB,CACjE,GAAI+C,EAAe,CAEjB,GAAIvD,EAAMD,EAAQ,CAChBzB,EAAUoD,IAAO,EACjBD,GAAY,EACZ,QACF,CACEA,EAAWE,EACX,KAIJ,CACE,GAAoDP,MAAhD7C,EAAa4C,aAAaQ,EAAGnB,GAA8B,CAC7D,GAAIR,EAAMD,EAAQ,CAChBzB,EAAUoD,IAAO,EACjBD,GAAY,EACZ,QACF,CACEA,EAAWE,EACX,KAEJ,CAEJ,CA9FA,CAgGF,OAAOF,CACT,CAyIkBiD,CAAQnG,EAAciF,EAAWD,EAAepB,EAAOa,IAEjD,GAAhBjC,EAKFmB,EAAkBvB,EAAewB,EAAO,IACf,GAAhBpB,EACTmB,EAAkBvB,EAAewB,EAAO,GAlI3B,SAAU5D,EAAcwC,EAAaJ,EAAewB,EAAO/D,GAC5E,IAAI4B,EAAMc,EAAaC,EAAa,MAAOoB,EAAMgC,UAC7CpE,EAASe,EAAaC,EAAa,SAAUoB,EAAMgC,UAEnDC,EAAQlH,GAAGsB,KAAK6F,WAAWC,OAAOC,gBAItC,IAHAvE,EAAMqB,OAAOrB,EAAIwE,QAAQJ,OACzBrE,EAASsB,OAAOtB,EAAOyE,QAAQJ,KAM7B,OAFAlC,EAAkBvB,EAAewB,EAAO,QACxC/D,EAAUR,IAAI,WAAW4D,SAAS,MAGpCjD,EAAayD,OAAOjB,GAEpBxC,EAAa6D,aAAarB,EAAa,MAAOf,GAAK,GAAO,GAE1D,IAAI2E,EAAUvG,EAAUR,IAAI,oBAC5B,GAAM+G,GAAWA,EAAQhF,UAEvB,IADA,IAAIiF,EAASD,EAAQhF,UACZgC,EAAI,EAAGA,EAAIiD,EAAOhD,OAAQD,IAAK,CACtC,IAAIkD,EAAQF,EAAQhF,UAAUgC,GAC1BrD,EAAUuG,EAAM9C,KAClB4C,EAAQvC,aAAaT,EAAG,YAAa,GAAG,GAAM,EAElD,CAKF,IADA,IAAImD,EAAavG,EAAa4C,aAAaJ,EAAa,cAC/CY,EAAI,EAAGA,EAAInC,EAAWoC,OAAQD,IACrC,GAAInC,EAAWmC,GAAGmD,YAAcA,EAAY,CAC1CtF,EAAWmC,GAAG3B,IAAMA,EASpB,KACF,CAEFkC,EAAkBvB,EAAewB,EAAO,GACxC/D,EAAUR,IAAI,WAAW4D,SAAS,KACpC,CAqFMuD,CAAaxG,EAAcwC,EAAaJ,EAAewB,EAAO/D,GAEhEA,EAAUR,IAAI,WAAW4D,SAAS,KACpC,GA1RF,CA4RF,IAGA,IAAIwD,GAAiB,EAEjBC,EAAe,CAAC,EAAG,EAAG,GACtBC,EAAc,EAEdhD,EAAoB,SAAUiD,EAAShD,EAAOiD,GAChD,IAAIC,EAAU,CAAC,EACXC,EAAe,EA4BnB,GA1BAD,EAAQF,QAAUA,EACdhD,GAMFkD,EAAQE,QAAUpD,EAAMqD,MACxBH,EAAQI,QAAUtD,EAAMuD,OAMxBJ,EAAenD,EAAMgC,YAEnBmB,EAAe,GAGjBD,EAAQM,WAAaL,IAErBD,EAAQE,QAAU,GAClBF,EAAQI,QAAU,GAClBJ,EAAQM,WAAa,GAGR,GAAXP,EAEFH,EAAa,GAAKA,EAAa,GAAKK,EACpCM,EAAgBX,GAEhBhG,EAAgB4G,SAAS,cAAe1D,EAAMuD,OAE9CI,EAAc,CAAEC,KAAM,UAAW5C,QAAS,aAGrC,CAEL,IAAI6C,EAAY,CAAC,EACjBd,IACe,GAAXE,GACFC,EAAQY,OAAS,QACjBD,EAAY,CAAED,KAAM,QAAS5C,QAASkC,EAAQY,QAC9ChH,EAAgB4G,SAAS,cAAeR,EAAQF,UAC5B,GAAXC,GACTC,EAAQY,OAAS,QACjBD,EAAY,CAAED,KAAM,UAAW5C,QAASkC,EAAQY,QAChDhH,EAAgB4G,SAAS,cAAe1D,EAAMuD,QAC1B,GAAXN,GACTC,EAAQY,OAAS,SACjBD,EAAY,CAAED,KAAM,UAAW5C,QAASkC,EAAQY,QAChDhH,EAAgB4G,SAAS,cAAe1D,EAAMuD,QAC1B,GAAXN,IACTC,EAAQY,OAAS,UACjBD,EAAY,CAAED,KAAM,QAAS5C,QAASkC,EAAQY,SAIhDH,EAAcE,GACd/G,EAAgB4G,SAAS,aAAcX,GACvC/E,EAAe+F,UAAUb,EAC3B,CAEF,EAEIS,EAAgB,SAAUE,GAC5B/G,EAAgB4G,SAAS,gBAAiBG,EAC5C,EAEIJ,EAAkB,SAAUX,GACN,IAApBA,EAAa,KACfA,EAAa,GAAuB,IAAlBA,EAAa,GAAWA,EAAa,IAGzDA,EAAa,GAAKkB,KAAKC,MAAMnB,EAAa,IAC1CA,EAAa,GAAKkB,KAAKC,MAAwB,IAAlBnB,EAAa,IAAY,IACtDhG,EAAgB4G,SAAS,eAAgBZ,EAC3C,EAIA1G,EAAasH,SAAS,aAAc,SACpCtH,EAAaV,GAAG,gBAAgB,SAAUyB,GACxC,GAA4B,WAAxBA,EAAK+G,MAAMC,WAAkD,WAAxBhH,EAAK+G,MAAMC,WAAkD,WAAxBhH,EAAK+G,MAAMC,WAAkD,WAAxBhH,EAAK+G,MAAMC,WACpG,WAAxBhH,EAAK+G,MAAMC,WAAkD,WAAxBhH,EAAK+G,MAAMC,WAAkD,WAAxBhH,EAAK+G,MAAMC,WAAkD,WAAxBhH,EAAK+G,MAAMC,WAClG,WAAxBhH,EAAK+G,MAAMC,WAAkD,WAAxBhH,EAAK+G,MAAMC,WAAkD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WACnG,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WACrG,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WACrG,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WACrG,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WACrG,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,WAAmD,YAAxBhH,EAAK+G,MAAMC,UACvF,OAAO,EAGT,IAAIC,EAAWjH,EAAK+G,MAAMC,UACtB7E,EAAWnC,EAAK+G,MAAM5E,SACtB+E,EAAmBjI,EAAa4C,aAAaM,EAAU,oBAC3D,MAAgB,kBAAZ8E,GAA4C,kBAAZA,GAClCrI,EAAOuI,oBAAoBlI,EAAckD,GACjB,GAApB+E,GAOU,eAAZD,GACFrI,EAAOuI,oBAAoBlI,EAAckD,GACjB,GAApB+E,QAFN,CAQF,IAEIjI,IACFA,EAAaV,GAAG,mBAAmB,SAAU6I,GAC3CxI,EAAOyI,YAAYvI,EAAUR,IAAI,eAAe2C,WAAYhC,GAC5DL,EAAOW,YAAYN,EAAcH,EACnC,IACAG,EAAaV,GAAG,mBAAmB,SAAUwC,GAC3C,GAAG5C,KAAKmJ,YAAYhJ,IAAI,cAAcH,KAAKmJ,YAAYhJ,IAAI,aAAa2C,WACtE,OAAO,CAEX,IAEJhC,EAAaV,GAAG,yBAAyB,SAAUwC,GAClD,OAAQA,EAAKkG,UACX,IAAK,MACL,IAAK,SACL,IAAK,mBACL,IAAK,iBAEN,GAAIrJ,GAAGoE,MAAMuF,QAAQxG,EAAKgG,OAAQ,OAClC,GAAIhG,EAAKgG,OAAS,GAAmB,KAAdhG,EAAKgG,MAE1B,OADAnJ,GAAGoE,MAAM3D,MAAM,IAAMY,EAAauI,UAAUzG,EAAKkG,UAAUQ,aAAe,uBACnE,EAKT,KAGAxI,EAAaV,GAAG,wBAAwB,SAAUwC,GAEhD,OAAQA,EAAKkG,UACX,IAAK,MACH,GAAsB,GAAlBvB,EAAyB,OAE7B,IAAIgC,EAAW3G,EAAKoB,SAChB1B,EAASxB,EAAa4C,aAAa6F,EAAU,UAC7CX,EAAQhG,EAAKgG,MACjB,IAAKY,MAAMZ,IACLhF,OAAOgF,IAAU,GAAKhF,OAAOgF,IAAUhF,OAAOtB,GAAS,CAEzDkF,EAAa,GAAKA,EAAa,GAAK5D,OAAOgF,GAAShF,OAAOhB,EAAK6G,UAEhEtB,EAAgBX,GAIhB,IADA,IAAIH,EAAavG,EAAa4C,aAAa6F,EAAU,cAC5CrF,EAAI,EAAGA,EAAInC,EAAWoC,OAAQD,IACrC,GAAInC,EAAWmC,GAAGmD,YAAcA,EAAY,CAC1CtF,EAAWmC,GAAG3B,IAAMK,EAAKgG,MACzB,KACF,CAEF,MACF,CAEF9H,EAAa6D,aAAa4E,EAAU,MAAO3G,EAAK6G,UAChD,MAEF,IAAK,eACL,IAAK,WACkB,gBAAjB7G,EAAKkG,UAA+C,YAAjBlG,EAAKkG,UACsBnF,MAA5D7C,EAAa4C,aAAad,EAAKoB,SAAU,iBAC3ClD,EAAa6D,aAAa/B,EAAKoB,SAAU,eAAgB,SAKnE,IAuBAlD,EAAaV,GAAG,sBAAsB,YArBN,WAC9B,GAAImH,EAAgB,CAClB,IAAImC,EAAY/I,EAAUR,IAAI,WACxBwJ,EAAUD,EAAUE,SAAS,WAC7BC,EAAgB,GACXH,EAAUxH,UAChBE,SAAQ,SAAUC,GACrB,IAAMyH,EAAc,CAAC,EACrBH,EAAQvH,SAAQ,SAAA2H,GACU,gBAApBA,EAAOC,WACLF,EAA0B,eAAGA,EAA0B,aAAI,CAAEG,SAAS,GAE9E,IACAJ,EAAcrH,KAAKsH,EACrB,IACAJ,EAAUQ,gBAAgBL,EAE5B,CACF,CAIEM,EACF,IAGAxJ,EAAUP,GAAG,YAAY,SAAUyB,GACX,GAAlB0F,EAKJ6C,IAJE3K,GAAGoE,MAAM3D,MAAM,aAMnB,IAEA,IAAIkK,EAAkB,WAEpB7C,GAAiB,EACb/F,GAAiBA,EAAgB6I,YAAW,GAEhD1J,EAAUR,IAAI,eAAe4D,SAAS,OACtCpD,EAAUR,IAAI,eAAeiI,SAAS,YAAa,kBAC/CzH,EAAUR,IAAI,YAAYQ,EAAUR,IAAI,WAAWkK,YAAW,GAC9D1J,EAAUR,IAAI,eAAeQ,EAAUR,IAAI,cAAckK,YAAW,GAExE7I,EAAgB4G,SAAS,iBAAkB1F,GAC3ClB,EAAgB4G,SAAS,cAAe,QAExCC,EAAc,CAAEC,KAAM,UAAW5C,QAAS,KAC1ClE,EAAgB4G,SAAS,aAAc,GAEvC,IAAInG,EAAOnB,EAAaoB,UACxBH,EAAaE,EAAKqI,QAElB,IAAInI,EAAU,GACdF,EAAKG,SAAQ,SAAUC,GAEjBA,EAAKC,OAAS,IAChBD,EAAKE,IAAM,KAEXJ,EAAQK,KAAKH,GACbmF,EAAa,IAAMnF,EAAKC,OAE5B,IACAxB,EAAa2B,cAAcN,GAG3BgG,EAAgBX,EAGlB,EAaC7G,EAAUP,GAAG,aAAa,WAGxBK,EAAO8J,qBAAqB5J,GAE5BF,EAAO+J,qBAAqB7J,EAAUG,GACtCH,EAAUR,IAAI,cAAcQ,EAAUR,IAAI,aAAasK,aAAY,GAEnE9J,EAAUR,IAAI,eAAesK,aAAY,EAC3C,IAEA9J,EAAUP,GAAG,YAAY,WAStB,IAAIwC,EAAO,CACP8H,SAAU,UACVC,OAHM,cAIN1F,OAVS,CACT/D,KAAM,OACN0J,UAAU,EACVC,GAJOlK,EAAUR,IAAI,WAAW2C,aAapCrD,GAAGqL,OAAOC,eAAe,OAAQnI,EAAMjC,EAC3C,IAEAA,EAAUP,GAAG,iBAAiB,SAAUyB,GAEtCpB,EAAOyI,YAAYrH,EAAKmJ,YAAarK,EAAUR,IAAI,YAGnDM,EAAOwK,cAActK,EAAWG,GAGhCH,EAAUR,IAAI,cAAcQ,EAAUR,IAAI,aAAasK,aAAY,GAEnE9J,EAAUR,IAAI,eAAesK,aAAY,GACzCjD,EAAe,CAAC,EAAG,EAAG,GACtBD,GAAiB,EACb5G,EAAUR,IAAI,YAAYQ,EAAUR,IAAI,WAAWkK,YAAW,GAC9D1J,EAAUR,IAAI,eAAeQ,EAAUR,IAAI,cAAckK,YAAW,GACxExJ,EAAY,CAAC,EAEb,IAAIoB,EAAOnB,EAAaoB,UACpBC,EAAU,GACdF,EAAKG,SAAQ,SAAUC,GACrB,IAAI6I,EAAY,GAEhB,GAAI7I,EAAK8I,iBAAmB,EAAG,CAE7B,IAAI/G,EAAM/B,EAAK+I,iBACf,GAAIhH,EAAK,CACP,IAAK,IAAIF,EAAI,EAAGA,EAAIE,EAAID,OAAQD,IACP,GAAnBE,EAAIF,GAAGmH,UAAeH,EAAU1I,KAAK4B,EAAIF,IAE/C7B,EAAK+I,iBAAmBF,CAC1B,CACA/I,EAAQK,KAAKH,EAEf,CACF,IAGA,IAAInB,EAAOP,EAAUM,YAAYC,KAC7BP,EAAUR,IAAI,iBACH,QAATe,GACFP,EAAUR,IAAI,eAAekK,YAAW,GACxC1J,EAAUR,IAAI,eAAesK,aAAY,IAEzC9J,EAAUR,IAAI,eAAekK,YAAW,IAKxC1J,EAAUR,IAAI,6BAA6B2C,WAClDnC,EAAUR,IAAI,WAAWmL,eAAe,sBAAuB,cAAc,GAE7E3K,EAAUR,IAAI,WAAWmL,eAAe,sBAAuB,cAAc,EAE5E,IAGA3K,EAAUP,GAAG,cAAc,SAAUwC,GACnCnC,EAAO8K,WAAW3I,EAAMjC,EAAW,0BAA2B,uBAAwBC,EACxF,IAEAD,EAAUP,GAAG,UAAU,SAAUwC,GAC/BhC,GAAWA,EACNH,EAAO8K,WAAW3I,EAAMjC,EAAW,0BAA2B,uBAAwBC,KACzFA,GAAWA,EAEf,IAGHD,EAAUR,IAAI,WAAWC,GAAG,gBAAgB,SAAUwC,GACrD,GAAqB,wBAAlBA,EAAKkG,WACFnI,EAAUR,IAAI,eAAe2C,WAEjC,OADArD,GAAGoE,MAAM3D,MAAM,UAAW,YACnB,EAGL,GAAI0C,EAAKkG,WAA8B,kBAAjBlG,EAAKkG,UAAiD,kBAAjBlG,EAAKkG,UAAgC,CAC9F,IAAI0C,EAAM1K,EAAaoB,UAAUU,EAAKoB,UACtC,GAAIwH,EAAIC,SAAWD,EAAIC,QAAU,EAAG,CAChC,IAAIC,EAAY,CACZ,UAAaF,EAAIC,QACjB,UAAahM,GAAGsB,KAAK6F,WAAW+E,OAAOd,IAE3CjI,EAAKgJ,QAAQC,aAAaH,EAC9B,CACF,CACI9I,EAAKkG,UAA8B,WAAjBlG,EAAKkG,UACzBrI,EAAOqL,sBAAsBnL,EAAUA,EAAUR,IAAI,WAAWyC,EAEvE,IAGAjC,EAAUR,IAAI,oBAAoBC,GAAG,oBAAmB,SAASwC,GAC5DjC,EAAUR,IAAI,WAAW4L,eAAe,gBAAiB,MAC7DpL,EAAUR,IAAI,WAAW4L,eAAe,sBAAuB,MAC3DpL,EAAUR,IAAI,6BAA6B2C,WAC9CnC,EAAUR,IAAI,WAAWmL,eAAe,sBAAuB,cAAc,GAE7E3K,EAAUR,IAAI,WAAWmL,eAAe,sBAAuB,cAAc,EAEtE,IAYN3K,EAAUP,GAAG,cAAc,SAAUyB,GAEnC,IAAImK,EAASrL,EAAUR,IAAI,UAAU2C,WACjCmJ,EAAetL,EAAUR,IAAI,gBAAgB2C,WAC7CoJ,EAAQvL,EAAUR,IAAI,SAAS2C,WAEnC,GAAImJ,GADctL,EAAUR,IAAI,eAAe2C,YACZkJ,GAAUE,EAE3C,OADAzM,GAAGoE,MAAM3D,MAAM,oBACR,EAGT,IAAI+B,EAAOnB,EAAaoB,UAExB,GAAsB,GAAlBqF,EACF,IAAK,IAAIrD,EAAI,EAAGA,EAAIjC,EAAKkC,OAAQD,IAAK,CACpC,IAAI7B,EAAOJ,EAAKiC,GAChB,GAAIsF,MAAMnH,EAAKE,MAAoB,MAAZF,EAAKE,IAE1B,OADA9C,GAAGoE,MAAM3D,MAAM,YAAa,UACrB,CAEX,CAKF,GADsBS,EAAUR,IAAI,6BAA6B2C,WAE7D,IAAK,IAAIoB,EAAI,EAAGA,EAAIjC,EAAKkC,OAAQD,IAAK,CAElC,IADUjC,EAAKiC,GACe,oBAE1B,OADAzE,GAAGoE,MAAM3D,MAAM,yBAA0B,UAClC,CAEf,CAGJ,IAKI+C,EAAgBtC,EAAUR,IAAI,6BAA6B2C,WAC/D,GAAIrD,GAAGsB,KAAK6F,WAAWC,OAAOsF,cAAgBlJ,EAAe,CAC3D,IAAImJ,EAAO3L,EAAO4L,kBAAkBxK,EAPf,UACL,mBACA,MAKsElB,GACtF,GAAY,GAARyL,EACF,OAAO,EAET,GAAY,GAARA,EAEF,OADA3M,GAAGoE,MAAM3D,MAAM,wBAAyB,YACjC,CAGX,CAEA,IAAIoM,EAAgB,IAAI7M,GAAG8M,QAEvBC,EAAW3K,EA8Bf,OAAI0F,GAAqC,KAAnBC,EAAa,IAEjC/H,GAAGoE,MAAM4I,QAAQ,iCA/BD,WAChB,IAAIC,EAAQ/G,KAAKgH,MAAMH,EAAS5J,KAAKA,MAIjCT,EAAU,GACdF,EAAKG,SAAQ,SAAUC,IAChBmH,MAAMnH,EAAKE,MAAQqB,OAAOvB,EAAKE,KAAO,GAOzCJ,EAAQK,KAAKH,EAEjB,IAEAqK,EAAME,QAAUzK,EAChBqK,EAAS5J,KAAKA,KAAO+C,KAAKC,UAAU8G,GAEpCnF,GAAiB,EACjB+E,EAAcO,SAChB,IAEiB,WACfP,EAAcQ,QAChB,IAKSR,QAHT,CAMF,IAEA,IAAIrH,EAAStE,EAAUM,YAEnBC,EAAO+D,GAAUA,EAAO/D,MAAQ6L,IAAIC,qBAE3B,WAAT9L,IACEP,EAAUR,IAAI,YAChBQ,EAAUR,IAAI,WAAWkK,YAAW,GAElC1J,EAAUR,IAAI,eAChBQ,EAAUR,IAAI,cAAckK,YAAW,IAG9B,QAATnJ,IACEP,EAAUR,IAAI,gBAChBQ,EAAUR,IAAI,eAAekK,YAAW,GAEtC1J,EAAUR,IAAI,gBAChBQ,EAAUR,IAAI,eAAekK,YAAW,GAG9C,GAEF,IACEhK,EAAOC,QAAUI,CACnB,CAAE,MAAOH,GAET,CACA,OAAOG,CACT,G,kBCp9BAjB,GAAGC,OAAOC,mBAA2B,WACnC,IAAIsN,EAEK,CAAC,EAEV,IACE5M,EAAOC,QAAU2M,CACnB,CAAE,MAAO1M,GAAS,CAClB,OAAO0M,CACT,G,iCCEA,IAUMtD,EAAU,CACduD,SAXe,SAACC,EAAQxM,EAAWsE,EAAQmI,EAAWC,GAEtDD,EAAUnI,GAAQ,WAEhBoI,EAAS,CACP,EAEJ,GACF,GAKA,K,gDCtBMC,E,MAAiBC,GAAAA,OAAiB,CACtCC,WAAY,CAAC,EACbC,YAAY,IAGd,eAAmD,IAAnCC,EAAKC,UAAAxJ,OAAA,QAAAR,IAAAgK,UAAA,GAAAA,UAAA,GAAGL,EAAgBvD,EAAM4D,UAAAxJ,OAAA,EAAAwJ,UAAA,QAAAhK,EAC5C,MACO,6BADCoG,EAAOzB,KAEJoF,EAAME,MAAM7D,EAAO8D,SAEnBH,CAEZ,C,kBCbDrN,EAAOC,QAAU,CACfwN,KAAM,CACJ,YAAa,cAEfC,KAAM,CACJ,YAAa,Q,sBCNjB,IAAIC,EAAM,CACT,iDAAkD,IAClD,oDAAqD,IACrD,6CAA8C,IAC9C,gDAAiD,IACjD,+BAAgC,IAChC,kCAAmC,IACnC,4BAA6B,IAC7B,+BAAgC,KAIjC,SAASC,EAAeC,GACvB,IAAIrD,EAAKsD,EAAsBD,GAC/B,OAAOE,EAAoBvD,EAC5B,CACA,SAASsD,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAEL,EAAKE,GAAM,CACpC,IAAII,EAAI,IAAIC,MAAM,uBAAyBL,EAAM,KAEjD,MADAI,EAAEE,KAAO,mBACHF,CACP,CACA,OAAON,EAAIE,EACZ,CACAD,EAAeQ,KAAO,WACrB,OAAOC,OAAOD,KAAKT,EACpB,EACAC,EAAepB,QAAUsB,EACzB9N,EAAOC,QAAU2N,EACjBA,EAAepD,GAAK,G", "sources": ["webpack://c-yy-gz-aipoerp/./src/business/GT40884AT9/GT40884AT9_9a33f420List_VM.Extend.js", "webpack://c-yy-gz-aipoerp/./src/business/GT40884AT9/GT40884AT9_9a33f420_VM.Extend.js", "webpack://c-yy-gz-aipoerp/./src/business/ST/ST_st_storein_VM.Extend.js", "webpack://c-yy-gz-aipoerp/./src/business/common/common_VM.Extend.js", "webpack://c-yy-gz-aipoerp/./src/client/common/biz/actions.js", "webpack://c-yy-gz-aipoerp/./src/client/common/redux/modules/demo.jsx", "webpack://c-yy-gz-aipoerp/./src/pack.js", "webpack://c-yy-gz-aipoerp/./src/business/ sync ^\\.\\/.*$"], "sourcesContent": ["/**\n * 所属节点：五险二金单位信息变更-列表页面\n * 创建人：liud\n * 创建时间：2020年10月30日\n * 最后修改人：liud\n */\ncb.define(process.env.__DOMAINKEY__, [], function () {\n    let GT40884AT9_9a33f420List_VM_Extend = {\n        doAction: function (name, viewModel) {\n            if (this[name])\n                this[name](viewModel);\n        },\n        init: function (viewModel) {\n            alert('1234, 啦啦啦啦啦啦');\n            console.log('测试调试 - 1');\n            console.log('测试调试 - 2');\n            viewModel.get('button687ub').on('click', function () {\n                console.log('测试脚本是否起作用！！');\n            })\n        }\n    }\n    try {\n        module.exports = GT40884AT9_9a33f420List_VM_Extend;\n    } catch (error) {\n\n    }\n    return GT40884AT9_9a33f420List_VM_Extend;\n});", "/**\n * 所属节点：五险二金单位信息变更-卡片页面\n * 创建人：liud\n * 创建时间：2020年10月29日\n * 最后修改人：liud\n */\ncb.define(process.env.__DOMAINKEY__,  function () {\n    let GT40884AT9_9a33f420_VM_Extend = {\n        doAction: function (name, viewModel) {\n            if (this[name])\n                this[name](viewModel);\n        },\n        init: function (viewModel) {\n            alert('1234, 啦啦啦啦啦啦');\n            console.log('测试调试 - 1');\n            console.log('测试调试 - 2');\n\n        }\n    }\n    try {\n        module.exports = GT40884AT9_9a33f420_VM_Extend;\n    } catch (error) {\n\n    }\n    return GT40884AT9_9a33f420_VM_Extend;\n});", "cb.define(process.env.__DOMAINKEY__, ['common/common_VM.Extend.js'], function (common) {\n  var ST_st_storein_VM_Extend = {\n    doAction: function (name, viewmodel) {\n      if (this[name])\n        this[name](viewmodel);\n    },\n    init: function (viewmodel) {\n\n      var bShowSn = false;\n      var scanedSns = {};\n      var detailsModel = viewmodel.get(\"details\");\n      if (cb.rest.interMode === 'touch' && viewmodel.getParams().mode !== 'browse') {\n        common.touchFormatter(detailsModel, viewmodel, \"qty\");\n        detailsModel.on('afterSetColumns', function () {\n          common.touchFormatter(detailsModel, viewmodel, \"qty\");\n        });\n      }\n\n      common.snformatter(detailsModel, viewmodel);\n\n      //电子秤称重\n      common.elecBalanceWeigh(detailsModel, viewmodel, \"qty\");\n\n      //初始化barcode wangyda\n      common.initBarcodeModel(viewmodel);\n      common.initSnCheckbox(viewmodel);\n      var inspectionModel = new cb.models.SimpleModel({ needClear: false });\n      viewmodel.addProperty('inspection', inspectionModel);\n\n      inspectionModel.on('filter', function (args) {\n        filterData(args);\n      });\n\n      var detailRows = [];\n      var filterData = function (isfilter) {\n        if (isfilter == true) {\n          //只显示未验货数据\n          var rows = detailsModel.getRows();\n          // var rows = detailsModel.getRows();\n          var newRows = [];\n          rows.forEach(function (item) {\n            if (item.recqty != item.qty) {\n              newRows.push(item);\n            }\n          });\n          detailsModel.setDataSource(newRows);\n        } else {\n          //显示全部数据\n          detailsModel.setDataSource(detailRows);\n        }\n      }\n\n      var errorGridModel = new cb.models.GridModel({\n        \"columns\": {\n          \"barcode\": {\n            \"cItemName\": \"barcode\",\n            \"cShowCaption\": \"条码\",\n            \"bHidden\": false,\n            \"bCanModify\": false,\n            \"iColWidth\": 120,\n            \"bShowIt\": true,\n            \"cControlType\": \"Input\"\n          },\n          \"skucode\": {\n            \"cItemName\": \"skucode\",\n            \"cShowCaption\": \"商品编码\",\n            \"bHidden\": false,\n            \"bCanModify\": false,\n            \"iColWidth\": 120,\n            \"bShowIt\": true,\n            \"cControlType\": \"Input\"\n          },\n          \"skuname\": {\n            \"cItemName\": \"skuname\",\n            \"cShowCaption\": \"商品名称\",\n            \"bHidden\": false,\n            \"bCanModify\": false,\n            \"iColWidth\": 200,\n            \"bShowIt\": true,\n            \"cControlType\": \"Input\"\n          },\n          \"errorcount\": {\n            \"cItemName\": \"errorcount\",\n            \"cShowCaption\": \"错误数量\",\n            \"bHidden\": false,\n            \"bCanModify\": false,\n            \"iColWidth\": 80,\n            \"bShowIt\": true,\n            \"cControlType\": \"Input\"\n          },\n          \"reason\": {\n            \"cItemName\": \"reason\",\n            \"cShowCaption\": \"错误原因\",\n            \"bHidden\": false,\n            \"bCanModify\": false,\n            \"iColWidth\": 120,\n            \"bShowIt\": true,\n            \"cControlType\": \"Input\"\n          }\n        },\n        \"showCheckBox\": false,\n        \"showRowNo\": false,\n        \"showAggregates\": false,\n        \"showColumnSetting\": false,\n        \"bCanModify\": false,\n        \"bIsNull\": true,\n        \"pagination\": false\n      });\n      viewmodel.addProperty('error', errorGridModel);\n\n      var checkWarehouse = function (viewmodel) {\n        if (!viewmodel.get('inwarehouse').getValue()) {\n          cb.utils.alert('请先录入仓库!', 'warning');\n          return false;\n        }\n        return true;\n      }\n      //============================================================barcode related\n      //扫码\n      viewmodel.get(\"barcode\").on('enter', function (data) {\n\n        // if(inspectionFlag == false){\n        //   cb.utils.alert('验货前请先点验货按钮!');\n        // }\n        var isSnChecked = false;\n        if (viewmodel.get('sncheckbox')) {\n          isSnChecked = viewmodel.get('sncheckbox').getValue();\n        }\n\n        //字段对照\n        var fields = {\n          mainid: 'storein',\n          product: 'product',\n          productcode: 'product_cCode',\n          productname: 'product_cName',\n          productsku: 'productsku',\n          barcode: 'barcode',\n          batchno: 'batchno',\n          producedate: 'producedate',\n          invaliddate: 'invaliddate',\n          productskucode: 'productsku_cCode',\n          weight: 'weight',\n          quantity: 'num',\n          unit: 'unit',\n          unitname: 'unitName',//'',product_unitName\n          product_modelDescription: 'product_modelDescription',\n          isbatch: 'isBatchManage'\n        };\n        var snWarehouse = viewmodel.get('inwarehouse').getValue();\n        var iSerialManage = viewmodel.get('inwarehouse_iSerialManage').getValue();\n        var curInputValue = data ? data : viewmodel.get(\"barcode\").getValue();\n        curInputValue = curInputValue.trim();\n        if (!viewmodel.getGridModels()) {\n          return;\n        }\n\n        var findRowBySn = function (detailsModel, val) {\n          let rows = detailsModel.getRows();\n          let snModelName = 'storeInDetailSNs';\n          for (let i = 0; i < rows.length; i++) {\n            let sns = rows[i][snModelName];\n            if (sns) {\n              // let snRows = sns.filter(row => row._status != 'Delete');\n              for (let j = 0; j < sns.length; j++) {\n                if (val == sns[j].sn) {\n                  let qty = rows[i].qty == null ? 0 : rows[i].qty;\n                  let recqty = rows[i].recqty;\n                  if (qty + 1 > recqty) return -2;//超数量\n\n                  detailsModel.select(i);\n                  return i;\n                }\n              }\n            }\n          }\n          return -1;\n        }\n\n        var findRow = function (detailsModel, productsku, isBatchManage, pData, codeType) {\n          var rowIndex = -1;\n          var rows = detailsModel.getRows();\n          var rowLength = rows.length;\n          for (var i = 0; i < rowLength; i++) {\n            if (rows[i]._status == 'Delete') {\n              continue;\n            }\n            if (detailsModel.getCellValue(i, fields.unit) != pData.oUnitId) { //单位不相同认为不是同一行\n              continue;\n            }\n\n            if (pData.isBatchManage) {\n              if (pData.productskus[0]) {\n                if (pData.productskus[0].batchno && detailsModel.getCellValue(i, fields.batchno) != pData.productskus[0].batchno) {\n                  continue;\n                }\n                if (pData.isExpiryDateManage) {\n                  if (pData.productskus[0].producedate && detailsModel.getCellValue(i, fields.producedate) != pData.productskus[0].producedate) {\n                    continue;\n                  }\n                  if (pData.productskus[0].invaliddate && detailsModel.getCellValue(i, fields.invaliddate) != pData.productskus[0].invaliddate) {\n                    continue;\n                  }\n                }\n              } else {\n                if (pData.batchno && detailsModel.getCellValue(i, fields.batchno) != pData.batchno) {\n                  continue;\n                }\n                if (pData.isExpiryDateManage) {\n                  if (pData.producedate && detailsModel.getCellValue(i, fields.producedate) != pData.producedate) {\n                    continue;\n                  }\n                  if (pData.invaliddate && detailsModel.getCellValue(i, fields.invaliddate) != pData.invaliddate) {\n                    continue;\n                  }\n                }\n              }\n\n            }\n\n            let qty = rows[i].qty == null ? 0 : rows[i].qty;\n            let recqty = rows[i].recqty;\n            let barcodeQty = pData.quantity;\n            if (!barcodeQty) barcodeQty = 1;\n            qty = Number(qty) + Number(barcodeQty);\n            let scale = cb.rest.AppContext.option.quantitydecimal;\n            qty = Number(qty.toFixed(scale));\n            recqty = Number(recqty.toFixed(scale));\n            let val = curInputValue;\n            if (codeType == 'sn') {\n\n              if (scanedSns[val]) {\n                cb.utils.alert('已扫描过该序列号[' + val + ']');\n                return -1;\n              }\n              // let rows = detailsModel.getRows();\n              // for (let i = 0; i < rows.length; i++) {\n              let snModelName = 'storeInDetailSNs';\n              let sns = rows[i][snModelName];\n              // let sns = rows[i][snmodeln]\n              if (sns) {\n                // let snRows = sns.filter(row => row._status != 'Delete');\n                for (let j = 0; j < sns.length; j++) {\n                  if (val == sns[j].sn) {\n                    // cb.utils.alert('序列号重复!','error');\n                    // return false;\n                    // sns[j].sninspect = 1;\n                    // detailsModel.\n                    viewmodel.get(snModelName).setCellValue(j, 'sninspect', 1, true, false);\n                    scanedSns[val] = true;\n                    break;\n                  }\n                }\n              }\n              if (!scanedSns[val]) continue;//序列号没有匹配上,继续查找下一商品行\n              // }\n            }\n\n            if (productsku == detailsModel.getCellValue(i, fields.productsku)) {\n              if (isBatchManage) {\n                //if (batchNo == detailsModel.getCellValue(i, fields.batchno)) {\n                if (qty > recqty) {\n                  scanedSns[val] = false;\n                  rowIndex = -2;//超数量\n                  continue;\n                } else {\n                  rowIndex = i;\n                  break;\n                }\n\n                // }\n              } else {\n                if (detailsModel.getCellValue(i, fields.batchno) == undefined) {\n                  if (qty > recqty) {\n                    scanedSns[val] = false;\n                    rowIndex = -2;//超数量\n                    continue;\n                  } else {\n                    rowIndex = i;\n                    break;\n                  }\n                }\n              }\n            }\n          }\n          return rowIndex;\n        };\n\n        var getUpdateQty = function (curRowIndex, qtyField, barcodeQty) {\n          if (!barcodeQty) barcodeQty = 1;\n          let newQuantity = null;\n          let tempQty = detailsModel.getCellValue(curRowIndex, qtyField);\n          if (null == tempQty || tempQty === undefined) {\n            newQuantity = barcodeQty;\n          } else {\n            // newQuantity = parseInt(tempQty) + barcodeQty;\n            newQuantity = Number(tempQty) + barcodeQty;\n          }\n          return newQuantity;\n        };\n\n        //更新行上数量\n        var updateRowQty = function (detailsModel, curRowIndex, curInputValue, pData, viewmodel) {\n          let qty = getUpdateQty(curRowIndex, \"qty\", pData.quantity);\n          let recqty = getUpdateQty(curRowIndex, \"recqty\", pData.quantity);\n          let rowindex = curRowIndex + 1;\n          let scale = cb.rest.AppContext.option.quantitydecimal;\n          qty = Number(qty.toFixed(scale));\n          recqty = Number(recqty.toFixed(scale));\n\n          if (qty >= recqty) {\n            // cb.utils.alert(\"第\" + rowindex + \"行条码为[\" + curInputValue + \"]的商品入库数量大于待入库数量！\", 'error');\n            setInspectionInfo(curInputValue, pData, 3);\n            viewmodel.get(\"barcode\").setValue(null);\n            return;\n          }\n          detailsModel.select(curRowIndex);//选中行\n          // detailsModel.setCellValue(curRowIndex, \"qty\", qty, true, false); //触发cellcheck\n          detailsModel.setCellValue(curRowIndex, \"qty\", qty, false, false);\n\n          var snModel = viewmodel.get('storeInDetailSNs');\n          if (!!snModel && snModel.getRows()) {\n            let snRows = snModel.getRows();\n            for (let i = 0; i < snRows.length; i++) {\n              let snRow = snModel.getRows()[i];\n              if (scanedSns[snRow.sn]) {\n                snModel.setCellValue(i, 'sninspect', 1, true, false);\n              }\n            }\n          }\n\n          //更新原数据源数量\n          var srcBillRow = detailsModel.getCellValue(curRowIndex, 'srcBillRow');\n          for (let i = 0; i < detailRows.length; i++) {\n            if (detailRows[i].srcBillRow == srcBillRow) {\n              detailRows[i].qty = qty;\n              //这里不更新,在保存前更新 序列号 验货结果\n              // if(detailRows[i].storeInDetailSNs){\n              //   for(let j=0;j<detailRows[i].storeInDetailSNs.length;j++){\n              //     if(scanedSns[detailRows[i].storeInDetailSNs[j].sn]){\n              //       detailRows[i].storeInDetailSNs[j].sninspect = 1;\n              //     }\n              //   }\n              // }\n              break;\n            }\n          }\n          setInspectionInfo(curInputValue, pData, 0);\n          viewmodel.get(\"barcode\").setValue(null);\n        };\n\n        if (isSnChecked) { //如果扫描序列号的话,需要先录入仓库\n          if (!checkWarehouse(viewmodel)) {\n            viewmodel.get(\"barcode\").setValue(null);\n            return;\n          }\n          if (!iSerialManage) { //不严格管理\n            let rowIndex = findRowBySn(detailsModel, curInputValue);\n            if (rowIndex == -1) {\n              // cb.utils.alert(\"表体行没有条码为[\" + curInputValue + \"]的商品！\", 'error');\n              // inspectionModel.setState('productInfo', curInputValue);\n              // inspectionModel.setState('promptMessage', '超入库范围');\n              // inspectionModel.setState('errorCount', 1);\n              // eslint-disable-next-line no-undef\n              setInspectionInfo(curInputValue, pData, 2);\n            } else if (rowIndex == -2) {\n              // eslint-disable-next-line no-undef\n              setInspectionInfo(curInputValue, pData, 3);\n            } else {\n              // updateRowQty(detailsModel, rowIndex, curInputValue, pData);\n              let qty = getUpdateQty(rowIndex, \"qty\", 1);\n              detailsModel.setCellValue(rowIndex, \"qty\", qty, true, true);\n            }\n            viewmodel.get(\"barcode\").setValue(null);\n            return;\n          }\n        }\n\n        var queryProductProxy = viewmodel.setProxy({\n          settle: {\n            url: '/bill/ref/getBarcodeResult.do',\n            method: 'POST'\n          }\n        });\n\n        var params = {\n          isReturn: 0,\n          keyword: curInputValue,\n          billnum: viewmodel.originalViewMeta.cBillNo,\n          codeType: isSnChecked ? 'snOnly' : '',\n          snWarehouse: snWarehouse,\n          iSerialManage: iSerialManage\n        };\n\n        if (curInputValue && detailsModel) {\n          queryProductProxy.settle(params, (err, result) => {\n            if (err) {\n              cb.utils.alert(err.message, 'error');\n              viewmodel.get(\"barcode\").setValue(null);\n              return;\n            }\n\n            if (result === undefined || JSON.stringify(result) == \"{}\") {\n              // cb.utils.alert(\"条码为[\" + curInputValue + \"]的商品档案不存在！\", 'error');\n              // inspectionModel.setState('productInfo', curInputValue);\n              // inspectionModel.setState('promptMessage', '商品不存在');\n              // inspectionModel.setState('errorCount', 1);\n              setInspectionInfo(curInputValue, null, 1);\n              viewmodel.get(\"barcode\").setValue(null);\n              return;\n            } else if (result.errtype == 'multirecord') {\n              setInspectionInfo(curInputValue, null, 4);\n              viewmodel.get(\"barcode\").setValue(null);\n              return;\n            }\n\n            let codeType = result.codeType;\n            let pData = result.data[0];\n            let isBatchManage = pData.isBatchManage;\n            let resultSku = pData.productskus[\"0\"].skuId;\n            // let batchno = isBatchManage ? pData.productskus[0].batchno : undefined;\n            var curRowIndex = -1;\n\n            curRowIndex = findRow(detailsModel, resultSku, isBatchManage, pData, codeType);\n\n            if (curRowIndex == -1) {\n              // cb.utils.alert(\"表体行没有条码为[\" + curInputValue + \"]的商品！\", 'error');\n              // inspectionModel.setState('productInfo', curInputValue);\n              // inspectionModel.setState('promptMessage', '超入库范围');\n              // inspectionModel.setState('errorCount', 1);\n              setInspectionInfo(curInputValue, pData, 2);\n            } else if (curRowIndex == -2) {\n              setInspectionInfo(curInputValue, pData, 3);\n            } else {\n              updateRowQty(detailsModel, curRowIndex, curInputValue, pData, viewmodel);\n            }\n            viewmodel.get(\"barcode\").setValue(null);\n          });\n        }\n      });\n\n      //=====验货================\n      var inspectionFlag = false; //验货标识\n      // var errorCount = 0;\n      var progressData = [0, 0, 0];\n      var totalErrQty = 0;\n      //var saveRows = [];//保存前的验货\n      var setInspectionInfo = function (barcode, pData, errflag) {\n        var errInfo = {};\n        var scanQuantity = 1;\n\n        errInfo.barcode = barcode;\n        if (pData) {\n          // var skus = pData.productskus;\n          // if (skus) {\n          //   errInfo.skucode = skus[0].skuCode;\n          //   errInfo.skuname = skus[0].skuName;\n          // } else {\n          errInfo.skucode = pData.cCode;\n          errInfo.skuname = pData.cName;\n          // }\n\n          // if (pData.quantity != undefined && pData.quantity != null) {\n          //   scanQuantity = pData.quantity;\n          // }\n          scanQuantity = pData.quantity;\n          if (!scanQuantity) {\n            scanQuantity = 1;\n          }\n\n          errInfo.errorcount = scanQuantity;\n        } else {\n          errInfo.skucode = '';\n          errInfo.skuname = '';\n          errInfo.errorcount = 1;\n        }\n\n        if (errflag == 0) {\n\n          progressData[1] = progressData[1] + scanQuantity;\n          setProgressData(progressData);\n\n          inspectionModel.setState('productInfo', pData.cName);\n          // inspectionModel.setState('promptMessage', { type: 'success', message: '匹配正确' });\n          setPromoteMsg({ type: 'success', message: '匹配正确' });\n          // inspectionModel.setState('errorCount', 0);\n\n        } else {\n\n          let promotMsg = {};\n          totalErrQty++;\n          if (errflag == 1) {\n            errInfo.reason = '商品不存在';\n            promotMsg = { type: 'error', message: errInfo.reason };\n            inspectionModel.setState('productInfo', errInfo.barcode);\n          } else if (errflag == 2) {\n            errInfo.reason = '超入库范围';\n            promotMsg = { type: 'warning', message: errInfo.reason };\n            inspectionModel.setState('productInfo', pData.cName);\n          } else if (errflag == 3) {\n            errInfo.reason = '超待入库数量';\n            promotMsg = { type: 'warning', message: errInfo.reason };\n            inspectionModel.setState('productInfo', pData.cName);\n          } else if (errflag == 4) {\n            errInfo.reason = '匹配到多个商品';\n            promotMsg = { type: 'error', message: errInfo.reason };\n          }\n\n          // inspectionModel.setState('promptMessage', promotMsg);\n          setPromoteMsg(promotMsg);\n          inspectionModel.setState('errorCount', totalErrQty);\n          errorGridModel.appendRow(errInfo);\n        }\n\n      };\n\n      var setPromoteMsg = function (promotMsg) {\n        inspectionModel.setState('promptMessage', promotMsg);\n      };\n\n      var setProgressData = function (progressData) {\n        if (progressData[2] !== 0) {\n          progressData[0] = progressData[1] * 100 / progressData[2];\n        }\n\n        progressData[0] = Math.round(progressData[0]);//百分比\n        progressData[1] = Math.round(progressData[1] * 100) / 100;//入库数量\n        inspectionModel.setState('progressData', progressData);\n      };\n\n      // var detailsModel = viewmodel.get(\"details\");\n      //向后台传递行号\n      detailsModel.setState('orderField', 'rowno');\n      detailsModel.on('rowColChange', function (args) {\n        if (args.value.columnKey == 'batchno' || args.value.columnKey == 'define1' || args.value.columnKey == 'define2' || args.value.columnKey == 'define3' ||\n          args.value.columnKey == 'define4' || args.value.columnKey == 'define5' || args.value.columnKey == 'define6' || args.value.columnKey == 'define7' ||\n          args.value.columnKey == 'define8' || args.value.columnKey == 'define9' || args.value.columnKey == 'define10' || args.value.columnKey == 'define11' ||\n          args.value.columnKey == 'define12' || args.value.columnKey == 'define13' || args.value.columnKey == 'define14' || args.value.columnKey == 'define15' ||\n          args.value.columnKey == 'define16' || args.value.columnKey == 'define17' || args.value.columnKey == 'define18' || args.value.columnKey == 'define19' ||\n          args.value.columnKey == 'define20' || args.value.columnKey == 'define21' || args.value.columnKey == 'define22' || args.value.columnKey == 'define23' ||\n          args.value.columnKey == 'define24' || args.value.columnKey == 'define25' || args.value.columnKey == 'define26' || args.value.columnKey == 'define27' ||\n          args.value.columnKey == 'define28' || args.value.columnKey == 'define29' || args.value.columnKey == 'define30') {\n          return false;\n        }\n\n        var cellName = args.value.columnKey;\n        var rowIndex = args.value.rowIndex;\n        var unitExchangeType = detailsModel.getCellValue(rowIndex, 'unitExchangeType');\n        if (cellName == 'stockUnit_code' || cellName == 'stockUnit_name') {\n          common.setInvExchRateState(detailsModel, rowIndex);\n          if (unitExchangeType == 0) {\n            return true;\n          } else {\n            return false;\n          }\n        }\n\n        if (cellName == 'invExchRate') {\n          common.setInvExchRateState(detailsModel, rowIndex);\n          if (unitExchangeType == 0) {\n            return false;\n          } else {\n            return true;\n          }\n        }\n      });\n\n      if (detailsModel) {\n        detailsModel.on('afterSetColumns', function (columns) {\n          common.fieldVisble(viewmodel.get('srcBillType').getValue(), detailsModel);\n          common.snformatter(detailsModel, viewmodel);\n        });\n        detailsModel.on('beforeInsertRow', function (data) {\n          if(this.getParent().get(\"srcBillNO\")&&this.getParent().get(\"srcBillNO\").getValue()){\n            return false;\n          }\n        });\n        //单元格值改变以前\n\t\t\t\tdetailsModel.on('beforeCellValueChange', function (data) {\n\t\t\t\t\tswitch (data.cellName) {\n\t\t\t\t\t  case \"qty\":\n\t\t\t\t\t  case \"subQty\":\n\t\t\t\t\t  case \"contactsQuantity\":\n\t\t\t\t\t  case \"contactsPieces\":\n\t\t\t\t\t  {\n\t\t\t\t\t\tif (cb.utils.isEmpty(data.value)) return;\n\t\t\t\t\t\tif (data.value <= 0 || data.value == '-') {\n\t\t\t\t\t\t  cb.utils.alert(\"[\" + detailsModel.getColumn(data.cellName).cShowCaption + \"]只能录入大于0的值，请检查后重试！\");\n\t\t\t\t\t\t  return false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t  }\n\t\t\t\t\t}\n\t\t\t\t  });\n      }\n\n      detailsModel.on('afterCellValueChange', function (data) {\n        console.log(data);\n        switch (data.cellName) {\n          case \"qty\": {\n            if (inspectionFlag == false) return;\n\n            var rowindex = data.rowIndex;\n            var recqty = detailsModel.getCellValue(rowindex, 'recqty');\n            var value = data.value;\n            if (!isNaN(value)) {\n              if (Number(value) >= 0 && Number(value) <= Number(recqty)) {\n                //detailsModel.setCellValue(rowindex,'recqty',data.oldValue);\n                progressData[1] = progressData[1] + Number(value) - Number(data.oldValue);\n\n                setProgressData(progressData);\n\n                //更新原数据源数量\n                var srcBillRow = detailsModel.getCellValue(rowindex, 'srcBillRow');\n                for (let i = 0; i < detailRows.length; i++) {\n                  if (detailRows[i].srcBillRow == srcBillRow) {\n                    detailRows[i].qty = data.value;\n                    break;\n                  }\n                }\n                return;\n              }\n            }\n            detailsModel.setCellValue(rowindex, 'qty', data.oldValue);\n            break;\n          }\n          case \"natUnitPrice\":\n          case \"natMoney\": {\n            if (data.cellName == 'natUnitPrice' || data.cellName == 'natMoney') { //财务联查，控制自动计算成本\n              if (detailsModel.getCellValue(data.rowIndex, 'autoCalcCost') != undefined)\n                detailsModel.setCellValue(data.rowIndex, 'autoCalcCost', 'false');\n            }\n            break;\n          }\n        }\n      });\n\n      var setDeleteRowButtonVisible = function () {\n        if (inspectionFlag) {\n          let gridModel = viewmodel.get('details');\n          const actions = gridModel.getCache('actions');\n          const actionsStates = [];\n          var rows = gridModel.getRows();\n          rows.forEach(function (item) {\n            const actionState = {};\n            actions.forEach(action => {\n              if (action.cItemName == 'btnDeleteRow') {\n                if (actionState[\"btnDeleteRow\"]) actionState[\"btnDeleteRow\"] = { visible: false };\n              }\n            });\n            actionsStates.push(actionState);\n          });\n          gridModel.setActionsState(actionsStates);\n          return false;\n        }\n      }\n      // let gridModel = viewmodel.getGridModel();\n\n      detailsModel.on('afterSetDataSource', () => {\n        setDeleteRowButtonVisible();\n      });\n\n      //验货\n      viewmodel.on('checkRow', function (args) {\n        if (inspectionFlag == true) {\n          cb.utils.alert('当前已处于验货状态!');\n          return;\n        }\n\n        initInspectInfo();\n\n      });\n\n      var initInspectInfo = function () {\n\n        inspectionFlag = true;\n        if (inspectionModel) inspectionModel.setVisible(true);\n        //viewmodel.get('btnCheckRow').setDisabled(true);\n        viewmodel.get('btnCheckRow').setValue('验货中');\n        viewmodel.get('btnCheckRow').setState('className', 'btn-inspecting');\n        if (viewmodel.get(\"barcode\")) viewmodel.get(\"barcode\").setVisible(true);\n        if (viewmodel.get(\"sncheckbox\")) viewmodel.get(\"sncheckbox\").setVisible(true);\n\n        inspectionModel.setState('errorGridModel', errorGridModel);\n        inspectionModel.setState('productInfo', '商品名称');\n\n        setPromoteMsg({ type: 'success', message: '' });\n        inspectionModel.setState('errorCount', 0);\n\n        var rows = detailsModel.getRows();\n        detailRows = rows.slice();//用于保存的数据备份\n\n        var newRows = [];\n        rows.forEach(function (item) {\n          //待入库数量\n          if (item.recqty > 0) {\n            item.qty = null;\n            // item.qty = 0;\n            newRows.push(item);\n            progressData[2] += item.recqty;\n          }\n        });\n        detailsModel.setDataSource(newRows);\n\n        // inspectionModel.setState('progressData', progressData);\n        setProgressData(progressData);\n\n        // setDeleteRowButtonVisible();会提示错误\n      };\n\n      // var setSnCheckBoxVisible = function(viewmodel){\n      //   if (viewmodel.get('sncheckbox')) {\n      //     var mode = viewmodel.getParams().mode;\n      //     if (mode == 'add' || mode == 'edit') {\n      //       viewmodel.get('sncheckbox').setVisible(true);\n      //     } else {\n      //       viewmodel.get('sncheckbox').setVisible(false);\n      //     }\n      //   }\n      // };\n\n       viewmodel.on('afterEdit', function() {\n      //   setSnCheckBoxVisible(viewmodel);\n         //财务联查模板，隐藏按钮\n         common.hiddenFiSearchButton(viewmodel);\n         //成对推单生成的入库单，单价金额不可改\n         common.hiddenFiSearchFields(viewmodel,detailsModel);\n         viewmodel.get(\"btnAddRow\")&&viewmodel.get(\"btnAddRow\").setDisabled(true);\n\n         viewmodel.get(\"btnCheckRow\").setDisabled(true);\n       });\n\n       viewmodel.on('relating', function () {\n          var code = viewmodel.get('srcBill').getValue();\n          var params = {\n              mode: 'edit',\n              readOnly: true,\n              id: code\n          };\n          var sbillno;\n          sbillno = 'st_storeout';\n          var data = {\n              billtype: 'voucher',\n              billno: sbillno,\n              params: params\n          };\n          cb.loader.runCommandLine('bill', data, viewmodel)\n      });\n\n      viewmodel.on('afterLoadData', function (args) {\n        //调拨类型的隐藏单价金额字段\n        common.fieldVisble(args.srcBillType, viewmodel.get('details'));\n\n        //是否显示序列号相关\n        common.showSnVisible(viewmodel, detailsModel);\n\n        // setSnCheckBoxVisible(viewmodel);\n        viewmodel.get(\"btnAddRow\")&&viewmodel.get(\"btnAddRow\").setDisabled(true);\n\n        viewmodel.get(\"btnCheckRow\").setDisabled(true);\n        progressData = [0, 0, 0];\n        inspectionFlag = false;\n        if (viewmodel.get(\"barcode\")) viewmodel.get(\"barcode\").setVisible(false);\n        if (viewmodel.get(\"sncheckbox\")) viewmodel.get(\"sncheckbox\").setVisible(false);\n        scanedSns = {};\n\n        var rows = detailsModel.getRows();\n        var newRows = [];\n        rows.forEach(function (item) {\n          let newSnRows = [];\n          //待入库数量\n          if (item.contactsQuantity > 0) {\n\n            let sns = item.storeInDetailSNs;\n            if (sns) {\n              for (let i = 0; i < sns.length; i++) {\n                if (sns[i].binspect == 0) newSnRows.push(sns[i]);\n              }\n              item.storeInDetailSNs = newSnRows;\n            }\n            newRows.push(item);\n\n          }\n        });\n        //detailsModel.setDataSource(newRows);\n\n        let mode = viewmodel.getParams().mode;\n        if (viewmodel.get(\"btnCheckRow\")) {\n          if (mode === \"add\") {\n            viewmodel.get(\"btnCheckRow\").setVisible(false);\n            viewmodel.get(\"btnCheckRow\").setDisabled(false);\n          } else {\n            viewmodel.get(\"btnCheckRow\").setVisible(false);\n          }\n        }\n\n        //非货位仓不允许参照\n        if (viewmodel.get('warehouse_isGoodsPosition').getValue()) {\n\t\t\t\t\tviewmodel.get(\"details\").setColumnState('goodsposition_cName', 'bCanModify', true);\n\t\t\t\t}else{\n\t\t\t\t\tviewmodel.get(\"details\").setColumnState('goodsposition_cName', 'bCanModify', false);\n\t\t\t\t}\n      });\n\n      //是否显示序列号信息\n      viewmodel.on(\"modeChange\", function (data) {\n        common.bsnTabShow(data, viewmodel, 'st_storein_body_page_sn', 'st_storein_head_page', bShowSn);\n      });\n\n      viewmodel.on(\"showsn\", function (data) {\n        bShowSn = !bShowSn;\n        if (!common.bsnTabShow(data, viewmodel, 'st_storein_body_page_sn', 'st_storein_head_page', bShowSn)) {\n          bShowSn = !bShowSn;//恢复原值\n        }\n      });\n\n      //选择货位前需先选中仓库\n\t\t\tviewmodel.get('details').on('beforeBrowse', function (data) {\n\t\t\t\tif(data.cellName === 'goodsposition_cName'){\n\t\t\t\t\tif (!viewmodel.get('inwarehouse').getValue()) {\n\t\t\t\t\t\tcb.utils.alert('请先选择仓库！', 'warning');\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n        }\n        if (data.cellName && (data.cellName == \"stockUnit_code\" || data.cellName == \"stockUnit_name\" )) {\n          let row = detailsModel.getRows()[data.rowIndex];\n          if (row.product && row.product > 0) {\n              let condition = {\n                  \"productId\": row.product,\n                  \"tenant_id\": cb.rest.AppContext.tenant.id\n              };\n              data.context.setCondition(condition);\n          }\n        }\n        if (data.cellName && (data.cellName == \"batchno\")) {\n          common.checkBeforeaddBatchno(viewmodel,viewmodel.get('details'),data);\n        }\n\t\t\t});\n\n\t\t\t//切换仓库刷新货位参照是否可编辑\n\t\t\tviewmodel.get(\"inwarehouse_name\").on('afterValueChange',function(data){\n        viewmodel.get(\"details\").setColumnValue(\"goodsposition\", null);\n\t\t\t\tviewmodel.get(\"details\").setColumnValue(\"goodsposition_cName\", null);\n\t\t\t\tif (viewmodel.get('warehouse_isGoodsPosition').getValue()) {\n\t\t\t\t\tviewmodel.get(\"details\").setColumnState('goodsposition_cName', 'bCanModify', true);\n\t\t\t\t}else{\n\t\t\t\t\tviewmodel.get(\"details\").setColumnState('goodsposition_cName', 'bCanModify', false);\n\t\t\t\t}\n            });\n\n      //非货位仓不允许参照\n\t\t\t// viewmodel.on('beforeAddRow', function (data) {\n\t\t\t// \tif (viewmodel.get('warehouse_isGoodsPosition').getValue()) {\n\t\t\t// \t\tviewmodel.get(\"details\").setColumnState('goodsposition_cName', 'bCanModify', true);\n\t\t\t// \t}else{\n\t\t\t// \t\tviewmodel.get(\"details\").setColumnState('goodsposition_cName', 'bCanModify', false);\n\t\t\t// \t}\n\t\t\t// });\n\n      //保存之前，校验出库仓库和入库仓库不可以相同\n      viewmodel.on('beforeSave', function (args) {\n\n        var outorg = viewmodel.get(\"outorg\").getValue();\n        var outwarehouse = viewmodel.get(\"outwarehouse\").getValue();\n        var inorg = viewmodel.get(\"inorg\").getValue();\n        var inwarehouse = viewmodel.get(\"inwarehouse\").getValue();\n        if (outwarehouse == inwarehouse && outorg == inorg) {\n          cb.utils.alert(\"出库仓库和入库仓库不可以相同！\");\n          return false;\n        }\n\n        var rows = detailsModel.getRows();\n\n        if (inspectionFlag == false) {\n          for (let i = 0; i < rows.length; i++) {\n            let item = rows[i];\n            if (isNaN(item.qty) || item.qty == null) {\n              cb.utils.alert('入库数量不能为空!', 'error');\n              return false;\n            }\n          }\n        }\n\n        //货位仓校验货位必输\n        let isGoodsPosition = viewmodel.get('warehouse_isGoodsPosition').getValue();\n        if (isGoodsPosition) {\n            for (let i = 0; i < rows.length; i++) {\n                let row = rows[i];\n                if (!row[\"goodsposition_cName\"]) {\n                    cb.utils.alert('启用货位管理的仓库行的货位不能为空,请检查!', 'error');\n                    return false;\n                }\n            }\n        }\n\n        var detailsModelName = 'details';\n        var snModelName = 'storeInDetailSNs';\n        var qtyfildname = 'qty';\n\n        // var detailsModel = viewmodel.get(\"details\");\n        var iSerialManage = viewmodel.get('inwarehouse_iSerialManage').getValue();\n        if (cb.rest.AppContext.option.serialManage && iSerialManage) {\n          let bflg = common.checkSnBeforeSave(args, detailsModelName, snModelName, qtyfildname, viewmodel);\n          if (bflg == 1) {\n            return false;//序列号重复\n          }\n          if (bflg == 2) {\n            cb.utils.alert('商品行入库数量与序列号数量不一致，请检查！', 'warning');\n            return false;\n          }\n          // return true;\n        }\n\n        var returnPromise = new cb.promise();\n\n        var argsdata = args;\n        var confirmSave = function () {\n          var datas = JSON.parse(argsdata.data.data);\n\n          //自动清空入库数量为空的行\n\n          var newRows = [];\n          rows.forEach(function (item) {\n            if (!isNaN(item.qty) && Number(item.qty) > 0) {\n              // if (item.storeInDetailSNs) {//更新验货结果\n              //   for (let j = 0; j < item.storeInDetailSNs.length; j++) {\n              //     if (item.storeInDetailSNs[j]._status != 'Delete')\n              //       item.storeInDetailSNs[j].sninspect = 1;\n              //   }\n              // }\n              newRows.push(item);\n            }\n          });\n\n          datas.details = newRows;\n          argsdata.data.data = JSON.stringify(datas);\n\n          inspectionFlag = false;\n          returnPromise.resolve();\n        };\n\n        var cancelSave = function () {\n          returnPromise.reject();\n        };\n\n        if (inspectionFlag && progressData[0] != 100) {\n\n          cb.utils.confirm('待入库数量和入库数量匹配有差异,请确认是否终止验货并保存?', confirmSave, cancelSave);\n          return returnPromise;\n        }\n\n      });\n\n      let params = viewmodel.getParams();\n      // eslint-disable-next-line no-undef\n      let mode = params && params.mode || env.VOUCHER_STATE_BROWSE;\n      // let gridModel = viewmodel.getGridModel();\n      if (mode === \"browse\") {\n        if (viewmodel.get(\"btnSave\")) {\n          viewmodel.get(\"btnSave\").setVisible(false);\n        }\n        if (viewmodel.get(\"btnAbandon\")) {\n          viewmodel.get(\"btnAbandon\").setVisible(false);\n        }\n      }\n      if (mode === \"add\") {\n        if (viewmodel.get(\"btnMoveprev\")) {\n          viewmodel.get(\"btnMoveprev\").setVisible(false);\n        }\n        if (viewmodel.get(\"btnMovenext\")) {\n          viewmodel.get(\"btnMovenext\").setVisible(false);\n        }\n      }\n    }\n  }\n  try {\n    module.exports = ST_st_storein_VM_Extend;\n  } catch (error) {\n\n  }\n  return ST_st_storein_VM_Extend;\n});\n", "cb.define(process.env.__DOMAINKEY__, function () {\n  var common_VM_Extend = (function () {\n\n    return {}\n  }());\n  try {\n    module.exports = common_VM_Extend;\n  } catch (error) { }\n  return common_VM_Extend;\n});", "/**\n * 新增editPlus，导出的action移动端和PC端可共用\n * 如果导出的action名称和系统action相同时，则覆盖系统action\n * 如果导出的action名称和系统action不相同时，则新增该action\n *\n * @param {*} billNo 单据编码\n * @param {*} viewmodel 单据模型\n * @param {*} params 参照\n * @param {*} beforeAct 前置事件\n * @param {*} afterAct 后置事件\n */\nconst editPlus = (billNo, viewmodel, params, beforeAct, afterAct) => {\n  // 组装params参数\n  beforeAct(params, () => {\n    console.log('editPlus action的逻辑代码', billNo, viewmodel, params);\n    afterAct({\n      // 参数\n    });\n  })\n}\n\nconst actions = {\n  editplus: editPlus\n}\nexport default actions\n", "import Immutable from \"immutable\";\n\nconst $$initialState = Immutable.fromJS({\n  demoValue1: {},\n  demoValue2: true\n});\n\nexport default (state = $$initialState, action) => {\n  switch (action.type) {\n    case \"PLATFORM_DATA_DEMO_VALUE\":\n      return state.merge(action.payload);\n    default:\n      return state;\n  }\n};\n", "\nmodule.exports = {\n  enus: {\n    'mdf-title': 'mdd driver'\n  },\n  zhcn: {\n    'mdf-title': '模型驱动'\n  }\n}\n", "var map = {\n\t\"./GT40884AT9/GT40884AT9_9a33f420List_VM.Extend\": 246,\n\t\"./GT40884AT9/GT40884AT9_9a33f420List_VM.Extend.js\": 246,\n\t\"./GT40884AT9/GT40884AT9_9a33f420_VM.Extend\": 254,\n\t\"./GT40884AT9/GT40884AT9_9a33f420_VM.Extend.js\": 254,\n\t\"./ST/ST_st_storein_VM.Extend\": 356,\n\t\"./ST/ST_st_storein_VM.Extend.js\": 356,\n\t\"./common/common_VM.Extend\": 515,\n\t\"./common/common_VM.Extend.js\": 515\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 635;"], "names": ["cb", "define", "process", "GT40884AT9_9a33f420List_VM_Extend", "doAction", "name", "viewModel", "this", "init", "alert", "get", "on", "module", "exports", "error", "GT40884AT9_9a33f420_VM_Extend", "common", "ST_st_storein_VM_Extend", "viewmodel", "bShowSn", "scanedSns", "detailsModel", "rest", "interMode", "getParams", "mode", "touchFormatter", "snformatter", "elecBalanceWeigh", "initBarcodeModel", "initSnCheckbox", "inspectionModel", "models", "SimpleModel", "needClear", "addProperty", "args", "filterData", "detailRows", "isfilter", "rows", "getRows", "newRows", "for<PERSON>ach", "item", "recqty", "qty", "push", "setDataSource", "errorGridModel", "GridModel", "data", "isSnChecked", "getValue", "fields", "snWarehouse", "iSerialManage", "curInputValue", "trim", "getGridModels", "getUpdateQty", "curRowIndex", "qtyField", "barcodeQty", "tempQty", "getCellValue", "undefined", "Number", "utils", "checkWarehouse", "setValue", "rowIndex", "val", "i", "length", "sns", "j", "sn", "select", "findRowBySn", "setInspectionInfo", "pData", "setCellValue", "queryProductProxy", "setProxy", "settle", "url", "method", "params", "isReturn", "keyword", "billnum", "originalViewMeta", "cBillNo", "codeType", "err", "result", "message", "JSON", "stringify", "errtype", "isBatchManage", "resultSku", "productskus", "skuId", "productsku", "<PERSON><PERSON><PERSON><PERSON>", "_status", "oUnitId", "batchno", "isExpiryDateManage", "producedate", "invaliddate", "quantity", "scale", "AppContext", "option", "quantitydecimal", "toFixed", "snModelName", "findRow", "snModel", "snRows", "snRow", "srcBillRow", "updateRowQty", "inspectionFlag", "progressData", "totalErrQty", "barcode", "errflag", "errInfo", "scanQuantity", "skucode", "cCode", "skuname", "cName", "errorcount", "setProgressData", "setState", "setPromoteMsg", "type", "promotMsg", "reason", "appendRow", "Math", "round", "value", "column<PERSON>ey", "cellName", "unitExchangeType", "setInvExchRateState", "columns", "fieldVisble", "getParent", "isEmpty", "getColumn", "cShowCaption", "rowindex", "isNaN", "oldValue", "gridModel", "actions", "getCache", "actionsStates", "actionState", "action", "cItemName", "visible", "setActionsState", "setDeleteRowButtonVisible", "initInspectInfo", "setVisible", "slice", "hiddenFiSearchButton", "hidden<PERSON>iSearch<PERSON><PERSON>s", "setDisabled", "billtype", "bill<PERSON>", "readOnly", "id", "loader", "runCommandLine", "srcBillType", "showSnVisible", "newSnRows", "contactsQuantity", "storeInDetailSNs", "binspect", "setColumnState", "bsnTabShow", "row", "product", "condition", "tenant", "context", "setCondition", "checkBeforeaddBatchno", "setColumnValue", "outorg", "outwarehouse", "inorg", "serialManage", "bflg", "checkSnBeforeSave", "returnPromise", "promise", "argsdata", "confirm", "datas", "parse", "details", "resolve", "reject", "env", "VOUCHER_STATE_BROWSE", "common_VM_Extend", "editplus", "billNo", "beforeAct", "afterAct", "$$initialState", "Immutable", "demoValue1", "demoValue2", "state", "arguments", "merge", "payload", "enus", "zhcn", "map", "webpackContext", "req", "webpackContextResolve", "__webpack_require__", "o", "e", "Error", "code", "keys", "Object"], "sourceRoot": ""}