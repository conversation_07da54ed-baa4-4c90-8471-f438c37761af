package com.yonyou.ucf.mdf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.project.ProjectBill;
import com.yonyou.aipierp.dto.ncapi.project.ProjectBillHead;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IProjectService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class ProjectServiceImpl implements IProjectService {

    NCOpenApiConfig ncOpenApiConfig;
    NCOpenApiService ncOpenApiService;
    AIPORepository aipoRepository;
    SimpleDateFormat projectDateFormat;


    public ProjectServiceImpl(NCOpenApiConfig ncOpenApiConfig,
                                NCOpenApiService ncOpenApiService,
                                AIPORepository aipoRepository) {
        this.ncOpenApiConfig = ncOpenApiConfig;
        this.ncOpenApiService = ncOpenApiService;
        this.aipoRepository = aipoRepository;
        this.projectDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    }

    @Override
    public JSONObject pushProjectToNC(JSONObject oriSaveReq, JSONObject saveRuleReturn) {
        String id = oriSaveReq.getString("id");
        JSONObject finalSaveProject = getFinalSavedProject(id,oriSaveReq,saveRuleReturn);
        UFinterface uFinterface = convertToProjectSaveUFinterface(finalSaveProject);
        JSONObject resp = ncOpenApiService.saveProject(uFinterface);
        if (!resp.getBooleanValue("success") ||
                !"1".equals(resp.getJSONObject("data").getJSONObject("ufinterface").getJSONArray("sendresult").getJSONObject(0).getString("resultcode"))) {
            log.error("同步企业项目信息至高级版系统失败 uFinterface-->{},resp-->{}", uFinterface, resp);
            throw new BusinessException("同步项目信息至高级版系统失败，错误原因：" + resp.toJSONString());
        }
        return resp;
    }

    @Override
    public JSONObject deleteProjectFromNC(String projectId, String orgId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bipid",projectId);
        jsonObject.put("pk_org",orgId);
        JSONObject resp = ncOpenApiService.deleteProject(jsonObject);
        if (!resp.getBooleanValue("success")) {
            log.error("从高级版删除项目信息失败 req-->{},resp-->{}", jsonObject, resp);
            throw new BusinessException("高级版删除项目（公司级）信息失败，错误原因：" + resp.toJSONString());
        }
        return resp;

    }

    /**
     * 获取最终保存的项目结果<br/>
     *  执行扩展规则时，保存事务还未结束、提交，此时查库无法查到更新后的数据，所以如果涉及子表更新，则必须拼接下 【数据库查询结果+请求requestbody+saveRuleReturn】
     * @param  projectId 项目ID
     * @param oriSaveReq  初始保存请求
     * @param saveRuleReturn rule返回的return中的信息
     * @return {@link JSONObject }
     */
    private JSONObject getFinalSavedProject(String projectId, JSONObject oriSaveReq, JSONObject saveRuleReturn) {
        if (oriSaveReq.getString("_status") != null && !"insert".equals(oriSaveReq.getString("_status").toLowerCase())) {  // 非新增时
            // 从数据库里查下项目信息
            JSONObject dataBaseProject = aipoRepository.queryProjectById(projectId);
            if (dataBaseProject != null) {
                List<JSONObject> dataBaseProjectApplyRangeList = JSON.parseArray(dataBaseProject.getString("projectApplyRangeList"), JSONObject.class);

                if (oriSaveReq.get("projectApplyRangeList") != null) {
                    List<JSONObject> oriReqProjectApplyRangeList = JSON.parseArray(oriSaveReq.getString("projectApplyRangeList"), JSONObject.class);
                    if (CollectionUtils.isNotEmpty(oriReqProjectApplyRangeList)) {
                        // 此时说明更新了子表信息，需要把所有项目范围子表都补充上
                        List<JSONObject> saveRuleReturnProjectApplyRangeList = JSON.parseArray(saveRuleReturn.getString("projectApplyRangeList"), JSONObject.class);
                        // 1,dataBaseProjectApplyRangeList 删去request中删除的projectApplyRange
                        oriReqProjectApplyRangeList.stream().forEach(reqProjectApplyRange -> {
                            if (reqProjectApplyRange.getString("_status").toLowerCase().equals("delete")) {
                                String deleteId = reqProjectApplyRange.getString("id");
                                dataBaseProjectApplyRangeList.removeIf(projectApplyRange -> projectApplyRange.getString("id").equals(deleteId));
                                saveRuleReturnProjectApplyRangeList.removeIf(projectApplyRange -> projectApplyRange.getString("id").equals(deleteId));
                            }
                        });
                        // 2,saveReturn中的所有子表，相同id覆盖到 dataBaseProjectApplyRangeList 中，不能覆盖就新增
                        saveRuleReturnProjectApplyRangeList.stream().forEach(saveReturn -> {
                            boolean saveReturnOverWrite = false;
                            for (int i = 0; i < dataBaseProjectApplyRangeList.size(); i++) {
                                JSONObject dataBaseProjectApplyRange = dataBaseProjectApplyRangeList.get(i);
                                if (dataBaseProjectApplyRange.getString("id").equals(saveReturn.getString("id"))) {
                                    dataBaseProjectApplyRangeList.set(i, saveReturn);
                                    saveReturnOverWrite = true;
                                    break;
                                }
                            }
                            if (!saveReturnOverWrite) {
                                dataBaseProjectApplyRangeList.add(saveReturn);
                            }
                        });

                    }
                }
                // 3，把database子表全部替换到saveReturn中
                saveRuleReturn.put("projectApplyRangeList", dataBaseProjectApplyRangeList);
            }
        }
        return saveRuleReturn;
    }

    private UFinterface convertToProjectSaveUFinterface(JSONObject bipProjectInfo){
        // 先查下需要翻译的字段
        JSONObject transFieldInfo = getTranslateField(bipProjectInfo);

        UFinterface uFinterface = UFinterface.init(ncOpenApiConfig.getBiz_center(),"project",ncOpenApiConfig.getGroupcode());

        ProjectBillHead projectBillHead = new ProjectBillHead();
        projectBillHead.setPk_group(ncOpenApiConfig.getGroupcode());
        projectBillHead.setPk_org(bipProjectInfo.getString("orgid"));   // 所属组织填到管理组织中
        projectBillHead.setProject_code(bipProjectInfo.getString("code"));
        projectBillHead.setProject_name(bipProjectInfo.getString("name"));
        projectBillHead.setPk_eps(transFieldInfo.getString("pk_eps"));  // 项目分类编码
        if (!"0".equals(bipProjectInfo.getString("parent"))){   // 【项目】如果没有父项目，bip系统默认是0，这个不要传高级版
            projectBillHead.setPk_parentpro(bipProjectInfo.getString("parent"));
        }
        if (bipProjectInfo.getDate("planStartTime") != null) {  // 计划开始、计划完成、实际开始、实际完成 日期
            projectBillHead.setPlan_start_date(projectDateFormat.format(bipProjectInfo.getDate("planStartTime")));
        }
        if (bipProjectInfo.getDate("planEndTime") != null){
            projectBillHead.setPlan_finish_date(projectDateFormat.format(bipProjectInfo.getDate("planEndTime")));
        }
        if (bipProjectInfo.getDate("realStartTime") != null){
            projectBillHead.setActu_start_date(projectDateFormat.format(bipProjectInfo.getDate("realStartTime")));
        }
        if (bipProjectInfo.getDate("realEndTime") != null){
            projectBillHead.setActu_finish_date(projectDateFormat.format(bipProjectInfo.getDate("realEndTime")));
        }
        projectBillHead.setPlanduration(bipProjectInfo.getString("planDate"));  // 计划工期
        projectBillHead.setPk_duty_dept(bipProjectInfo.getString("deptid"));    // 责任部门
        projectBillHead.setPk_duty_org(bipProjectInfo.getString("orgid"));
        projectBillHead.setMemo(bipProjectInfo.getString("description"));
        projectBillHead.setUpload_flag("N");

        // 构建子表 - 适用范围
        if (bipProjectInfo.get("projectApplyRangeList")!=null){
            List<JSONObject> itemList = new ArrayList<>();
            List<JSONObject> projectApplyRangeList = JSON.parseArray(bipProjectInfo.getString("projectApplyRangeList"), JSONObject.class);
            projectApplyRangeList.stream().forEach(projectApplyRange -> {
                JSONObject item = new JSONObject();
                item.put("pk_parti_org",projectApplyRange.getString("rangeOrgId"));
                item.put("memo","");
                itemList.add(item);
            });

            JSONObject bodyvos = new JSONObject();
            bodyvos.put("item",itemList);
            projectBillHead.setBodyvos(bodyvos);
        }

        ProjectBill projectBill = new ProjectBill();
        projectBill.setId(bipProjectInfo.getString("id"));
        projectBill.setBillhead(projectBillHead);

        uFinterface.setBill(projectBill);

        return uFinterface;
    }

    /**
     * 翻译字段信息
     *
     * @param bipProjectInfo 待同步高级版的bip项目信息
     * @return {@link JSONObject }
     */
    private JSONObject getTranslateField(JSONObject bipProjectInfo){
        JSONObject res = new JSONObject();

        JSONObject projectClass = aipoRepository.queryProjectClassById(bipProjectInfo.getString("classifyid"));
        if (projectClass!=null){
            res.put("pk_eps",projectClass.getString("code"));   // 项目分类编码
        }

        return res;
    }

}
