package com.yonyou.ucf.mdf.equip.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yonyou.ucf.mdf.equip.model.GaUrl;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/14 16:16
 * @DESCRIPTION 网关信息服务
 */
@Component
public class GatewayService {
    @Value("${app.gateway-address-url}")
    private String gatewayAddressUrl;

    @SneakyThrows
   public GaUrl getGateway() {
       String body = HttpUtil.createGet(gatewayAddressUrl).execute().body();
       if (StringUtils.isNotBlank(body)) {
           ResponseResult<GaUrl> gaUrlResponseResult = JSONObject.parseObject(body, new TypeReference<ResponseResult<GaUrl>>() {
           });
           if (gaUrlResponseResult.isSuccess()) {
               return gaUrlResponseResult.getData();
           }
       }
       return null;
   }
}
