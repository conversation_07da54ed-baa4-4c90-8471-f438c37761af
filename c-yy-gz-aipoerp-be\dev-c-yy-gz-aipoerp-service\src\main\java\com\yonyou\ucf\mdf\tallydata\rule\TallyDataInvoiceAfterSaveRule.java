package com.yonyou.ucf.mdf.tallydata.rule;

import java.util.List;
import java.util.Map;

import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.bill.rule.base.AbstractCommonRule;
import com.yonyou.ucf.mdd.ext.model.BillContext;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("tallyDataInvoiceAfterSaveRule")
public class TallyDataInvoiceAfterSaveRule implements IYpdCommonRul {
	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
		log.error("-----------进入TallyDataInvoiceAfterSaveRulelifeid---------start");
		String action = rulCtxVO.getAction();

		log.error("-----------进入TallyDataInvoiceAfterSaveRule---------end");
		return null;
	}
}
