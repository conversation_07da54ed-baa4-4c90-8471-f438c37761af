package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.yonyou.iuap.context.InvocationInfoProxy;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.equip.model.ResponseResult;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ucf.mdf.rbsm.model.StringProcessor;
import com.yonyou.ucf.mdf.rbsm.model.VendorInfo;
import com.yonyou.ucf.mdf.rbsm.service.itf.IVendorQryervice;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

@Service
public class VendorQryServiceImpl implements IVendorQryervice {

	@Autowired
	private IBillQueryRepository billQryRepository;

	@Autowired
	private IBillRepository billRepository;

	@Autowired
	private BipOpenApiRequest apiRequest;

	private String vendorUrl = "/yonbip/digitalModel/vendor/detail";

	@Override
	public List<Map<String, Object>> queryVendorByCode(String code) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("*");
		QuerySchema bankSchema = QuerySchema.create().name("vendorbanks").addSelect("*");
		schema.addCompositionSchema(bankSchema);
		QueryConditionGroup cond = new QueryConditionGroup();
		cond.addCondition(new QueryCondition<String>("code", ConditionOperator.eq, code)); // 编码
		schema.addCondition(cond);
		List<Map<String, Object>> result = billQryRepository.queryMapBySchema("aa.vendor.Vendor", schema, "yssupplier");
		return result;
	}

	@Override
	public VendorInfo queryVendorById(String id) {
		Map<String, String> param = Maps.newHashMap();
		param.put("id", id);
		ResponseResult<VendorInfo> resp = apiRequest.doGet(vendorUrl, param,
				new TypeReference<ResponseResult<VendorInfo>>() {
				});
		if (!resp.isSuccess2()) {
			throw new RuntimeException(resp.getMessage());
		}
		return resp.getData();
	}

	@Override
	public VendorInfo queryVendorByCode2(String code) {
		String sql = "select id from iuap_apdoc_coredoc.aa_vendor where code = ? and ytenant_id = ?";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(code);
		parameter.addParam(InvocationInfoProxy.getTenantid());
		String id = billRepository.queryForObject(sql, parameter, new StringProcessor());
		if (StringUtils.isBlank(id)) {
			return null;
		}
		return queryVendorById(id);
	}

	@Override
	public Map<String, VendorInfo> queryByIds(List<String> vendorIds) {
		Map<String, VendorInfo> result = Maps.newHashMap();
		if (CollectionUtils.isEmpty(vendorIds)) {
			return result;
		}
		for (String id : vendorIds) {
			VendorInfo vendorInfo = queryVendorById(id);
			if (vendorInfo != null) {
				result.put(id, vendorInfo);
			}
		}
		return result;
	}
}
