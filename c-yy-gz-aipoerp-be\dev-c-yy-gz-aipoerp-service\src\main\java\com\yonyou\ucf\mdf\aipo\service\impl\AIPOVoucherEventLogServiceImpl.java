package com.yonyou.ucf.mdf.aipo.service.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryJoin;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.aipo.model.AIPOVoucherEventLog;
import com.yonyou.ucf.mdf.aipo.service.AIPOVoucherEventLogService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * 凭证事件日志服务实现类
 */
@Service
public class AIPOVoucherEventLogServiceImpl implements AIPOVoucherEventLogService {

	@Autowired
	private IBillCommonRepository billCommonRepository;
	@Autowired
	private IBillQueryRepository billQryRepository;

    @Override
	public AIPOVoucherEventLog save(AIPOVoucherEventLog eventLog) {
		try {
			List<IBillDO> billDOs = Lists.newArrayList(eventLog);
			billDOs = billCommonRepository.commonSaveBill(billDOs, "SocialSecurityRecord");
			return (AIPOVoucherEventLog) billDOs.get(0);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
    }

	@Override
	public AIPOVoucherEventLog findByEventId(String eventid) {
		if (StringUtils.isBlank(eventid)) {
			return null;
		}
		QuerySchema schema = QuerySchema.create().addSelect("*");
		schema.addCompositionSchema(QuerySchema.create().name("VoucherEventLogDetailList").addSelect("*"));
		schema.addCondition(QueryConditionGroup.and(QueryCondition.name("eventid").eq(eventid)));
		List<AIPOVoucherEventLog> result = findBySchema(schema);
		if (CollectionUtils.isNotEmpty(result)) {
			return result.get(0);
		}
		return null;
	}

	@Override
    public List<AIPOVoucherEventLog> findByBillId(String billId) {
		if (StringUtils.isBlank(billId)) {
			return Collections.emptyList();
		}
		QuerySchema schema = QuerySchema.create().addSelect("*");
		schema.addJoin(new QueryJoin("VoucherEventLogDetailList", null, "left"));
		schema.addCompositionSchema(QuerySchema.create().name("VoucherEventLogDetailList").addSelect("*"));
		schema.addCondition(
				QueryConditionGroup.and(QueryCondition.name("VoucherEventLogDetailList.billId").eq(billId)));
		return findBySchema(schema);
    }

	@SuppressWarnings("unchecked")
	@Override
	public List<AIPOVoucherEventLog> findBySchema(QuerySchema schema) {
		return (List<AIPOVoucherEventLog>) billQryRepository
				.queryBySchema("AIPOERPCREATE.AIPOERPCREATE.AIPOVoucherEventLog", schema);
	}

	@Override
	public List<AIPOVoucherEventLog> findAllFail() {
		QuerySchema schema = QuerySchema.create().addSelect("*");
		schema.addCondition(QueryConditionGroup.and(QueryCondition.name("processStatus").not_eq("2"),
				QueryCondition.name("processResult").eq("N"), QueryCondition.name("eventValid").eq("Y")));
		schema.addOrderBy("createTime");
		return findBySchema(schema);
	}

}
