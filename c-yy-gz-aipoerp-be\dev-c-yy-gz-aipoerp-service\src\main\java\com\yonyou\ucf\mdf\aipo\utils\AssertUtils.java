package com.yonyou.ucf.mdf.aipo.utils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;
import com.alibaba.fastjson.JSON;

/**
 * 断言工具
 *
 * <AUTHOR>
 */
public class AssertUtils {

    /**
     * 字符串为空
     *
     * @param str
     * @param tips
     * @throws Exception
     */
    public static void stringIsNull(Object str, String tips) throws Exception {
        if(str == null || "".equals(str.toString().trim())) {
            throw new Exception(tips);
        }
    }

    /**
     * Map为空
     *
     * @param map
     * @param tips
     * @throws Exception
     */
    public static void mapIsNull(Map map, String tips) throws Exception {
        if(map == null || map.size() == 0) {
            throw new Exception(tips);
        }
    }

    /**
     * List为空
     *
     * @param list
     * @param tips
     * @throws Exception
     */
    public static void listIsNull(List list, String tips) throws Exception {
        if(list == null || list.size() == 0) {
            throw new Exception(tips);
        }
    }

    /**
     * 根据条件判断是否抛出异常
     *
     * @param isTrue 为true时抛出异常
     * @param tips
     * @throws Exception
     */
    public static void checkException(boolean isTrue, String tips) throws Exception {
        if(isTrue) {
            throw new Exception(tips);
        }
    }

    /**
     * 异常处理
     *
     * @param e
     * @return
     */
    public static String toStackTrace(Exception e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);

        try {
            String message = e.getMessage();
            if(e.getCause() != null) {// && e.getCause().getMessage() != null
                message += "\n" + e.getCause().getMessage();
                if(e.getCause().getCause() != null) {
                    message += "\n" + e.getCause().getCause().getMessage();
                    e.getCause().getCause().printStackTrace(pw);
                }
                e.getCause().printStackTrace(pw);
            }
            e.printStackTrace(pw);
            String messagecob = message + "\n" + sw.toString();
            message = message + "\n" + sw.toString();
            if(message != null && message.length() > 4000) {
                message = message.substring(0, 3800);
            }
            return message;
        } catch(Exception e1) {
            e.printStackTrace(pw);
            String ss = e.getMessage() + sw.toString();
            return ss;
        }
    }

    /**
     * 多语数据获取
     * @param multiLangData
     * @return
     */
    public static String getMultiLangData(Object multiLangData) {
        if(multiLangData == null) {
            return "";
        }
        if(multiLangData instanceof String) {
            if(((String) multiLangData).startsWith("{")) {
                multiLangData = JSON.parseObject(multiLangData.toString());
            } else if(((String) multiLangData).startsWith("[")) {
                multiLangData = JSON.parseArray(multiLangData.toString());
            }
        }

        if(multiLangData instanceof Map) {
            return ((Map) multiLangData).get("zh_CN") == null ? "" : (String) ((Map) multiLangData).get("zh_CN");
        } else if(multiLangData instanceof List) {
            if(((List<?>) multiLangData).size() > 1 && ((List) multiLangData).get(0) instanceof String) {
                return null;
            }
            return ((Map) ((List) multiLangData).get(0)).get("zh_CN") == null ? "" : (String) ((Map) ((List) multiLangData).get(0)).get("zh_CN");
        } else {
            return multiLangData.toString();
        }
    }
}
