package com.yonyou.ucf.mdf.travelexpense.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.travelexpense.service.IStaffQryService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

/**
 * 员工查询接口
 *
 * <AUTHOR>
 *
 * 2025年4月16日
 */
@Service
public class StaffQryServiceImpl implements IStaffQryService {

    @Autowired
    private IBillQueryRepository billQryRepository;

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Map<String, Object>> queryStaffByIds(List<String> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Collections.emptyMap();
        }
        QuerySchema schema = QuerySchema.create().addSelect("*")
                .addCompositionSchema(QuerySchema.create().name("staffJob").addSelect("*")
                        .addSelect(new QueryField("deptId", "deptId", null, "bd.adminOrg.AdminOrgVO/id"))
                        .addSelect("deptId.code as deptCode,deptId.name as deptName")
                        .addSelect(new QueryField("orgId", "orgId", null, "org.func.AdminOrg/id"))
                        .addSelect("orgId.code as orgCode,orgId.name as orgName"))
                .addCondition(QueryConditionGroup.and(QueryCondition.name("id").in(staffIds)));
        List<Map<String, Object>> result = billQryRepository.queryMapBySchema("hred.staff.Staff", schema,
                "hrcloud-staff-mgr");
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyMap();
        }
        return result.stream()
                .collect(Collectors.toMap(m -> m.getOrDefault("id", "").toString(), m -> m, (m1, m2) -> m2));
    }

}
