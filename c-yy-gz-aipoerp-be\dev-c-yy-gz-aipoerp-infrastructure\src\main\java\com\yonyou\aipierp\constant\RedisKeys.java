package com.yonyou.aipierp.constant;

public class RedisKeys {

    /**
     * redis分布式锁前缀
     */
    public static String PRE_LOCK = "aipolock";

    /**
     * redis缓存前缀
     */
    public static String PRE_CACHE = "aipocache";

    public static String SEPARATOR = ":";

    /**
     * 过期时间 一分钟
     */
    public static final int REDIS_KEY_EXPIRE_ONE_MINUTE = 60;
    /**
     * 过期时间 一小时
     */
    public static final long REDIS_KEY_EXPIRE_ONE_HOUR = 60 * 60;
    /**
     * 过期时间 一天
     */
    public static final int REDIS_KEY_EXPIRE_ONE_DAY = 24 * 60 * 60;

    // 以下为redis锁==========================================================================================
    public static final String LOCK_STAFFSYNCTONC_TASK = PRE_LOCK + SEPARATOR + "staffSyncToNc" + SEPARATOR + "tenantLock";
}
