#### **测试环境**

```java
堡垒机地址：
jrmpravwsm-public.bastionhost.aliyuncs.com
 账号：Yanghang
 密码：Get_rich@1988
    
系统登录地址：
https://yonyoubip-test.aipocloud.com/#/
系统的管理员账号：
yhtmanager/manager@2020
    
阿里云
尊敬的Yanghang：
云堡垒机bastionhost-cn-0mm3zng7e03公网运维地址为jrmpravwsm-public.bastionhost.aliyuncs.com，内网运维地址为jrmpravwsm.bastionhost.aliyuncs.com，SSH运维端口为60022，RDP运维端口为63389。运维操作请参见 运维使用手册。如需帮助，可以扫描控制台中二维码加入专家群获取更多支持。
```

--流水线--无法使用--作废

```
git地址
http://*********:8090/aibogroup/r6project/aibosyncdata.git
账号：aibolsx
密码：NphPEN1m_t-hHsxGs6-y

host
*********     yonyoubip-test.aipocloud.com
```

**C3-R6开发环境**

```
git地址
http://kkgit.yyrd.com/005/005/aipogroup/aipor6/aipoerp.git
账号：Aipolsx
密码：5zKLqrp7bFy6t_xXpGhH


配置信息

租户：国资-艾珀ERP项目
租户管理员：杨航
hosts 绑定
************    tech-devr6.yybip.cn
************    devr6.yybip.cn
数据库地址： ************ 3306
数据库账号：c-yy-gz-aipoerp
密码：sBE7nXG01sWtPhT

引擎行业可自行创建技术中台
技术中台
https://tech-devr6.yybip.cn/#/
业务中台
https://devr6.yybip.cn/#/

13348905219
账号密码：手机号/get_rich@1988

YMS
https://tech-devr6.yybip.cn/confcenter
账号密码：yanghangh/get_Rich@1988

Git地址（在行业group下创建项目）:
https://kkgit.yyrd.com/005
 
```

