package com.yonyou.ucf.mdf.aipo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.ucf.mdf.aipo.dto.MsgForwardDto;
import com.yonyou.ucf.mdf.aipo.service.IAipoVoucherCheckService;
import com.yonyou.ucf.mdf.aipo.service.IMsgForwardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class AipoVoucherCheckServiceimpl implements IAipoVoucherCheckService {

    @Autowired
    private IMsgForwardService msgForwardService;

    @Override
    public void beforeVoucherCheck(JSONObject jsonObject) throws Exception {

       /* unSettlement：反结账
        addVoucher:新增凭证
        updateVoucher:修改凭证
        voucherAudit：审核
        unVoucherAudit:取消审核
        voucherTally：记账
        unvoucherTally:取消记账
        initialAdjust：期初调整
        unCreateAccount：取消建账
        voucherDel：凭证删除*/
        String operateType = jsonObject.getString("operateType");
        String voucherid = jsonObject.getString("voucherid");//凭证主键
        String accbook = jsonObject.getString("accbook");//账簿
        String period = jsonObject.getString("period");//期间
        String accentity = jsonObject.getString("accentity");//组织
        Integer billCode = jsonObject.getInteger("billCode");//凭证号
        // JSONObject data = jsonObject.getJSONObject("data");
        //凭证操作 时拿查询sql的方式来获取数据
        //先将类型和凭证id传给nc65
        //校验数据不能为空
        if (ObjectUtils.isEmpty(operateType)) {
            throw new RuntimeException("operateType参数不能为空");
        }
        //if(ObjectUtils.isEmpty(voucherid)){
        //    return null;
        //}

        JSONObject sendJson = new JSONObject();
        sendJson.put("operateType", operateType);
        sendJson.put("voucherid", voucherid);
        if (!"voucherDel".equals(operateType)) {
            //  throw new Exception("删除凭证暂不支持"); 目前只做删除凭证前校验
            //查询凭证所有信息
//        String tenantid = InvocationInfoProxy.getTenantid();
//        String userid = InvocationInfoProxy.getUserid();
//        log.error("进入凭证租户：{}", tenantid);
//        log.error("进入凭证用户：{}", userid);
//        //这个是前事件  所以库中还没有数据 就不查询库了
//        String voucehrSql = "select aa.id,bb.user_id as auditorid,bb.code as auditcode ,cc.user_id as tallyid,cc.code as tallycode from figl.fi_voucher aa left join iuap_apcom_auth.ba_user bb on aa.auditor =  bb.user_id\n" +
//"left join iuap_apcom_auth.ba_user cc on cc.user_id  = aa.tallyman\n" +
//"where  aa.tenantid = '"+tenantid+"' and aa.id = '"+voucherid+"'\n";
//  log.error("查询凭证信息sql为：{}", voucehrSql);
//        List<Map> voucherMapList = MetaDaoHelper.selectSql(voucehrSql, new HashMap<>());
//
//        if(voucherMapList!=null && voucherMapList.size()>0){
//             //凭证map
//        Map voucherMap = voucherMapList.get(0);
//        sendJson.putAll(voucherMap);
//        }
//
//
//         String userQuery = " select distinct code as currentUserCode,name as currentUserName,id as currentUserId from iuap_apcom_auth.ba_user where user_id = '"+userid+"' and tenant_id = '"+tenantid+"' ";
//        List<Map> userMapList = MetaDaoHelper.selectSql(userQuery, new HashMap<>());
//         log.error("查询凭证信息结果为：{}", JSONObject.toJSONString(userMapList));
//        if(userMapList==null || userMapList.size()<=0){
//            throw new RuntimeException("用户不存在:"+userid);
//        }
//        Map userMap = userMapList.get(0);
//
//        sendJson.putAll(userMap);
//
//         log.error("查询凭证信息结果为：{}", sendJson.toJSONString());

            return;

        }
        ObjectMapper objectMapper = new ObjectMapper();
        MsgForwardDto msgForwardDto = new MsgForwardDto();
        msgForwardDto.setReqType("post");
        msgForwardDto.setUrl("pz_check");
        //  log.error("参数组装结果为：{}", JSONUtil.toJsonStr(jsonObject));
        // String dataStr = sendJson.toJSONString().replaceAll(";", "；");

        msgForwardDto.setData(sendJson);

        String req = msgForwardService.msgForward(msgForwardDto);

        //  log.error("调用nc65接口结果为：{}", req);
        try {
            JSONObject resultJson = JSONObject.parseObject(req);
            //  log.error("返回结果解析为：{}", JSONUtil.toJsonStr(reqInfo));

            if (!"200".equals(resultJson.getString("code"))) {
                if (resultJson.getString("message") != null && resultJson.getString("message").contains("CheckSuccess")) {
                    return;

                }

                String errorMessage = resultJson.getString("message");
                if (errorMessage != null && errorMessage.contains("凭证不存在") && "voucherDel".equals(operateType)) {
                    return;
                }

                throw new RuntimeException(resultJson.getString("message"));
            }
        } catch (Exception e) {
            throw new RuntimeException("同步失败:" + e.getMessage());
        }


    }
}
