package com.yonyou.aipierp.dto.ncapi;

import com.alibaba.fastjson.JSONObject;

/**
 * dept add dto
 *
 * <AUTHOR>
 * @date 2024/12/11
 */
public class UFinterface extends JSONObject {
    /**
     * "账套编码（sm_busicenter的值）"
     */
    public String ACCOUNT = "account";
    /**
     * "dept（固定值）"
     */
    public String BILLTYPE = "billtype";
    /**
     * "0001（集团编码）"
     */
    public String GROUPCODE = "groupcode";
    /**
     * "Y（一般默认Y）"
     */
    public String ISEXCHANGE = "isexchange";
    /**
     * "Y（一般默认Y）"
     */
    public String REPLACE = "replace";
    /**
     * "default（一般默认default）"
     */
    public String SENDER = "sender";
    /**
     * 承载单据
     */
    public String BILL = "bill";

    public static UFinterface init(String account,String billType,String groupCode){
        UFinterface uFinterface = new UFinterface();
        uFinterface.setAccount(account);
        uFinterface.setBilltype(billType);
        uFinterface.setGroupcode(groupCode);

        uFinterface.setIsexchange("Y");
        uFinterface.setReplace("Y");
        uFinterface.setSender("default");
        return uFinterface;
    }

    public String getAccount() {
        return this.getString(ACCOUNT);
    }

    public void setAccount(String account) {
        this.put(ACCOUNT, account);
    }

    public String getBilltype() {
        return this.getString(BILLTYPE);
    }

    public void setBilltype(String billtype) {
        this.put(BILLTYPE, billtype);
    }

    public String getGroupcode() {
        return this.getString(GROUPCODE);
    }

    public void setGroupcode(String groupcode) {
        this.put(GROUPCODE, groupcode);
    }

    public String getIsexchange() {
        return this.getString(ISEXCHANGE);
    }

    public void setIsexchange(String isexchange) {
        this.put(ISEXCHANGE, isexchange);
    }

    public String getReplace() {
        return this.getString(REPLACE);
    }

    public void setReplace(String replace) {
        this.put(REPLACE, replace);
    }

    public String getSender() {
        return this.getString(SENDER);
    }

    public void setSender(String sender) {
        this.put(SENDER, sender);
    }

    public JSONObject getBill() {
        return this.getJSONObject(BILL);
    }

    public void setBill(JSONObject bill) {
        this.put(BILL, bill);
    }
}
