package com.yonyou.ucf.mdf.task.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.iuap.fileservice.sdk.module.pojo.CooperationFileInfo;
import com.yonyou.iuap.fileservice.sdk.service.CooperationFileUploadService;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.iuap.yms.processor.MapProcessor;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ucf.mdf.task.service.IPrintTaskService;
import com.yonyou.ucf.mdf.utils.TaskUtil;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @className: PrintTaskServiceImpl
 * @author: wjc
 * @date: 2025/4/8 14:30
 * @Version: 1.0
 * @description:
 */
@Slf4j
@Service
public class PrintTaskServiceImpl implements IPrintTaskService {

	@Autowired
	private CooperationFileUploadService fileUploadService;

	@Value("${app.tenantId}")
	private String tenantId;

	@Autowired
	private IBillRepository billRepository;

	@Autowired
	private BipOpenApiRequest apiRequest;

	/**
	 * 获取单据打印模板pdf文件
	 */
	private String printbillpdf = "/yonbip/znbz/rbsm/api/bill/common/printbillpdf";
	/**
	 * 协同单据发起打印预览任务生成PDF附件
	 */
	private String xtprintpdf = "/%s/yonbip_kk/api/external/archives/print";

	/**
	 * 通过任务Id获取打印预览附件下载地址
	 */
	private String pdfCallback = "/%s/yonbip_kk/api/external/archives/pdfCallback";

	private String attachupload = "/yonbip/znbz/rbsm/api/attach/upload";

	@Override
	public Object billPrint2PDF() {
		String id = "1111";
		String msg = "请求成功";
		try {
			// 查询审批完成单据
			StringBuffer buff = new StringBuffer();
			buff.append("select * from znbz.bill_print_pdf_v");
			List<Map<String, Object>> list = billRepository.queryForList(buff.toString(), null, new MapListProcessor());
			if (list == null || list.size() == 0) {
				return TaskUtil.ok(id, msg, "处理完成[无新增审批完成单子]");
			}
			List<String> pkBos = getPkBo(list);
			Map<String, List<Map<String, Object>>> templates = getTemplate(pkBos);
			if (templates.isEmpty()) {
				log.error("未查询到打印模板");
				return TaskUtil.error(id, msg, "未查询到打印模板");
			}
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				id = map.getOrDefault("id", "").toString();
				String templatecode = getTemplatecode(map, templates);
				if (StringUtils.isBlank(templatecode)) {
					log.error("没有获取到默认打印模板：{}", JSONObject.toJSON(map));
					continue;
				}
				boolean processing = isProcessing(id, map, templatecode);
				if (processing) {
					log.error("正在处理中：{}", JSONObject.toJSON(map));
					continue;
				}
				if ("fk".equals(map.getOrDefault("btype", "").toString())) {
					String attachmentAss = null;
					// 获取附件目录，如果为空，则创建目录
					if (map.get("vattachmentass") != null && !"".equals(map.get("vattachmentass"))) {
						attachmentAss = (String) map.get("vattachmentass");
					} else {
						String uuid = String.valueOf(UUID.randomUUID());
						attachmentAss = uuid.replace("-", "");

						// 更新单据文件目录
						buff = new StringBuffer();
						buff.append("update ").append(map.get("tbname"));
						buff.append(" set ").append(map.get("vcfiled")).append(" = ? ");
						buff.append(" where ").append(map.get("pkfiled")).append(" = ? ");
						SQLParameter parameter = new SQLParameter();
						parameter.addParam(attachmentAss);
						parameter.addParam(id);
						billRepository.update(buff.toString(), parameter);
					}

					JSONObject json = new JSONObject();
					json.put("billID", id);
					json.put("pk_billtype", map.get("pk_billtype"));
					json.put("printTmplCode", templatecode);

					String strfiles = apiRequest.doPost(printbillpdf, json);

					log.error("[获取单据打印模板pdf文件]返回：{}", strfiles);
					JSONObject res = null;
					if (strfiles != null && !"".equals(strfiles)) {
						res = JSONObject.parseObject(strfiles);
						if (res == null || res.get("data") == null || "".equals(res.get("data"))) {
							// 未获取到
							log.error("未获取到打印PDF文件URL，id:{},billtype:{},tpid:{}", id, map.get("pk_billtype"),
									map.get("printTmplCode"));
							updatePDFLog(3, id, "未获取到打印PDF文件URL,res:" + strfiles);
							continue;
						}
					}
					String fileUrl = (String) res.get("data");
					if (StringUtils.isBlank(fileUrl)) {
						updatePDFLog(3, id, "获取单据打印模板pdf文件返回为空");
						continue;
					}
					uploadFile(fileUrl, map, attachmentAss); // 上传附件
				} else {

					// 协同附件
					JSONObject json = new JSONObject();
					json.put("billno", map.get("pk_bo"));
					json.put("printcountswitch", true);
					json.put("printrefreshinterval", 1000);
					json.put("printAction", "preview");
					json.put("context_path", "/mdf-node/uniform");
					JSONArray jsonArr = new JSONArray();
					jsonArr.add(id);
					json.put("ids", jsonArr);
					Map<String, Object> paramsmap = new HashMap<>();
					paramsmap.put("tenantId", tenantId);
					paramsmap.put("printcode", templatecode);
					paramsmap.put("params", json.toJSONString());
					paramsmap.put("keepAlive", "true");
					paramsmap.put("isCache", "0");
					paramsmap.put("sendType", "6");
					paramsmap.put("meta", "5");
					// 协同单据发起打印预览任务生成PDF附件
					String strfiles = apiRequest.doPostByFormData(String.format(xtprintpdf, tenantId), paramsmap);
					log.error("[协同单据发起打印预览任务生成PDF附件]返回：{}", strfiles);
					JSONObject res = null;
					if (strfiles != null && !"".equals(strfiles)) {
						res = JSONObject.parseObject(strfiles);
						if (res == null || !"200".equals(res.get("code")) || res.get("data") == null
								|| "".equals(res.get("data"))) {
							// 未获取到
							log.error("协同单据未获取到打印PDF文件taskid，id:{},billtype:{},tpid:{}", id, map.get("pk_billtype"),
									map.get("printTmplCode"));
							updatePDFLog(3, id, "协同单据未获取到打印PDF文件taskid,res:" + strfiles);
							continue;
						}
						res = res.getJSONObject("data");
					}
					String taskId = (String) res.get("data");
					if (StringUtils.isBlank(taskId)) {
						log.error("协同单据打印预览返回任务id为空，msg:{}", res.getString("msg"));
						updatePDFLog(3, id, "协同单据未获取到打印PDF文件taskid,res:" + strfiles);
						continue;
					}
					jsonArr = new JSONArray();
					jsonArr.add(taskId);
					// 暂停5秒供文件生成
					Thread.sleep(5000);
					// 通过任务Id获取打印预览附件下载地址
					strfiles = apiRequest.doPost(String.format(pdfCallback, tenantId), jsonArr);
					log.error("[通过任务Id获取打印预览附件下载地址]返回：{}", strfiles);
					if (strfiles != null && !"".equals(strfiles)) {
						res = JSONObject.parseObject(strfiles);
						if (res == null || !"200".equals(res.get("code")) || res.get("data") == null
								|| "".equals(res.get("data"))) {
							// 未获取到
							log.error("协同单据未获取到打印PDF文件URl，id:{},billtype:{},tpid:{}", id, map.get("pk_billtype"),
									map.get("printTmplCode"));
							updatePDFLog(3, id, "协同单据未获取到打印PDF文件URl,res:" + strfiles);
							continue;
						}
					}
					res = res.getJSONObject("data");
					res = res.getJSONObject("data");
					res = res.getJSONObject(taskId);
					String fileUrl = res.getString("data");
					if (StringUtils.isBlank(fileUrl)) {
						updatePDFLog(3, id, "获取单据打印模板pdf文件返回为空");
						continue;
					}
					uploadFile(fileUrl, map, (String) map.get("vattachmentass")); // 上传附件

				}

			}
			return TaskUtil.ok(id, msg, "处理完成");
		} catch (Exception e) {
			log.error("[单据审批附件]异常:{}", e.getMessage(), e);
			updatePDFLog(3, id, "异常:" + e.getMessage());
			return TaskUtil.error(id, msg, "[单据审批附件]异常:{}" + e.getMessage());
		}
	}

	/**
	 * 获取打印模板编码
	 * 
	 * @param map
	 * @param templates
	 * @return
	 */
	private String getTemplatecode(Map<String, Object> map, Map<String, List<Map<String, Object>>> templates) {
		String pk_bo = map.getOrDefault("pk_bo", "").toString();
		List<Map<String, Object>> templateList = templates.get(pk_bo);
		if (CollectionUtils.isEmpty(templateList)) {
			return null;
		}
		if (templateList.size() == 1) {
			return templateList.get(0).getOrDefault("templatecode", "").toString();
		}
		String bustype = map.getOrDefault("bustype", "").toString();
		String defaultTemplate = null;
		String otherTemplate = null;
		for (Map<String, Object> templateMap : templateList) {
			String trans_type_codes = templateMap.getOrDefault("trans_type_codes", "").toString();
			String isdefault = templateMap.getOrDefault("isdefault", "").toString();
			if (StringUtils.isNotBlank(trans_type_codes)) {
				if (trans_type_codes.contains(bustype)) {
					return templateMap.getOrDefault("templatecode", "").toString();
				} else if ("1".equals(isdefault)) {
					defaultTemplate = templateMap.getOrDefault("templatecode", "").toString();
				} else if ("[]".equals(trans_type_codes)) {
					otherTemplate = templateMap.getOrDefault("templatecode", "").toString();
				}
			} else if ("1".equals(isdefault)) {
				defaultTemplate = templateMap.getOrDefault("templatecode", "").toString();
			} else {
				otherTemplate = templateMap.getOrDefault("templatecode", "").toString();
			}
		}
		if (StringUtils.isNotBlank(defaultTemplate)) {
			return defaultTemplate;
		}
		return otherTemplate;
	}

	/**
	 * 获取打印模板
	 * 
	 * @param pkBos
	 * @return
	 */
	private Map<String, List<Map<String, Object>>> getTemplate(List<String> pkBos) {
		if (CollectionUtils.isEmpty(pkBos)) {
			return Collections.emptyMap();
		}
		String sql = "select busiext5,templatecode,trans_type_codes,isdefault from znbz.bill_print_template_v where busiext5 in (%s)";
		sql = String.format(sql, pkBos.stream().map(v -> StrUtil.wrap(v, "'")).collect(Collectors.joining(",")));
		List<Map<String, Object>> result = billRepository.queryForList(sql, null, new MapListProcessor());
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyMap();
		}
		return result.stream().collect(Collectors.groupingBy(m -> {
			return m.getOrDefault("busiext5", "").toString();
		}));
	}

	/**
	 * 获取业务实体主键
	 * 
	 * @param list
	 * @return
	 */
	private List<String> getPkBo(List<Map<String, Object>> list) {
		if (CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		return list.stream().map(m -> {
			Object pk_bo = m.getOrDefault("pk_bo", null);
			if (pk_bo != null) {
				return pk_bo.toString();
			}
			return null;
		}).filter(Objects::nonNull).distinct().collect(Collectors.toList());
	}

	/**
	 * 上传附件
	 * 
	 * @param fileUrl
	 */
	private void uploadFile(String fileUrl, Map<String, Object> map, String attachmentAss) {

		String filename = "审批详情.pdf";

		String id = map.getOrDefault("id", "").toString();
		if (StringUtils.isBlank(id)) {
			return;
		}
		// 1. 建立连接
		HttpURLConnection connection = null;
		try {
			URL url = new URL(fileUrl);
			connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("GET");
			connection.setConnectTimeout(30_000);
			connection.setReadTimeout(60_000);

			// 2. 处理响应
			if (connection.getResponseCode() != 200) {
				throw new IOException("HTTP错误代码: {}" + connection.getResponseCode());
			}
		} catch (MalformedURLException e1) {
			log.error("下载附件文件异常，请检查请求地址是否正确！", e1);
			updatePDFLog(3, id, String.format("下载附件文件异常，请检查请求地址是否正确！,附件地址：{}", fileUrl));
			return;
		} catch (ProtocolException e1) {
			log.error("下载附件文件异常，请检查请求协议是否正确！", e1);
			updatePDFLog(3, id, String.format("下载附件文件异常，请检查请求协议是否正确！,附件地址：{}", fileUrl));
			return;
		} catch (IOException e1) {
			log.error("下载附件文件异常，请检查请求地址是否是否能访问！", e1);
			updatePDFLog(3, id, String.format("下载附件文件异常，请检查请求地址是否是否能访问！,附件地址：{}", fileUrl));
			return;
		}
		// 3. 流式处理（可能有大文件）
		try (InputStream inputStream = connection.getInputStream();
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

			byte[] buffer = new byte[4096];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 4. 转换为Base64
			String base64Data = Base64Utils.encodeToString(outputStream.toByteArray());

			// 5. 上传单据附件
			JSONObject json = new JSONObject();
			if ("fk".equals(map.get("btype"))) {
				// 费控
				json.put("billType", map.get("pk_billtype"));
				json.put("fileName", filename);
				json.put("file", base64Data);
				json.put("attachmentAss", attachmentAss);
				String strfiles = apiRequest.doPost(attachupload, json);
				log.error("[上传单据附件]返回：{}", strfiles);
				if (strfiles == null || "".equals(strfiles)) {
					updatePDFLog(3, id, "上传单据失败,res:" + strfiles);
					return;// 上传单据失败
				}
				json = JSONObject.parseObject(strfiles);
				if (!"200".equals(json.get("code"))) {
					updatePDFLog(3, id, "上传单据失败,res:" + strfiles);
					return;// 上传单据失败
				}
			} else {
				// 协同
				Base64.Decoder decoder = Base64.getDecoder();
				byte[] imageByte = decoder.decode(base64Data);
				InputStream stream = new ByteArrayInputStream(imageByte);
				CooperationFileInfo fileInfo = fileUploadService.uploadFile((String) map.get("vattachmentass"), id,
						stream, filename, null);
				if (fileInfo == null || fileInfo.getFileId() == null) {
					updatePDFLog(3, id, "协同单据上传附件失败返回为空");
					return;// 上传单据失败
				}
			}

			// 上传成功
			updatePDFLog(2, id, "上传到附件成功");

		} catch (IOException e) {
			updatePDFLog(3, id, String.format("上传单据附件失败：{}", e.getMessage()));
		}

	}

	/**
	 * 判断某个附件是否在处理中
	 * 
	 * @param id
	 * @return
	 */
	private boolean isProcessing(String id, Map<String, Object> map, String templatecode) {
		// 生成文件之前先判断是否有5分钟内在处理中的文件，避免系统补偿机制循环调用生成多个文件
		StringBuffer buff = new StringBuffer();
		buff.append("select * from znbz.bip_bill_print_pdf_log");
		buff.append(" where id=? ");
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(id);
		Map<String, String> logmap = billRepository.queryForObject(buff.toString(), parameter, new MapProcessor());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (logmap != null && logmap.get("status") != null) {
			int status = Integer.parseInt(logmap.get("status"));
			if (status == 1) {
				// 处理中
				return true;
			} else {
				Date pubts = null;
				try {
					pubts = sdf.parse(logmap.get("pubts"));
				} catch (ParseException e) {
					log.error("时间转换错误{}", logmap.get("pubts"), e);
					return true;
				}
				long tslong = pubts.getTime();
				long now = System.currentTimeMillis();
				long diff = (now - tslong) / 1000 / 60;// 获取两个时间相差的分钟
				if (diff > 5) {
					updatePDFLog(1, id, "再次处理");
				} else {
					// 5分钟内，不处理
					return true;
				}
			}
		} else {
			buff = new StringBuffer();
			buff.append(
					"insert into znbz.bip_bill_print_pdf_log(id,code,pk_billtype,vattachmentass,pk_bo,templatecode,status,pubts)");
			buff.append(" values (?,?,?,?,?,?,?,?)");

			parameter = new SQLParameter();
			parameter.addParam(id);
			parameter.addParam(map.get("code"));
			parameter.addParam(map.get("pk_billtype"));
			parameter.addParam(map.get("vattachmentass"));
			parameter.addParam(map.get("pk_bo"));
			parameter.addParam(templatecode);
			parameter.addParam(1);
			parameter.addParam(sdf.format(new Date()));

			billRepository.update(buff.toString(), parameter);
		}
		return false;
	}

	private void updatePDFLog(int status, String id, String mark) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		StringBuffer buff = new StringBuffer();
		buff.append("update znbz.bip_bill_print_pdf_log");
		buff.append(" set status= ? ");
		buff.append(",mark= ? ");
		buff.append(",pubts= ? ");
		buff.append(" where id= ? ");

		SQLParameter parameter = new SQLParameter();
		parameter.addParam(status);
		parameter.addParam(mark);
		parameter.addParam(sdf.format(new Date()));
		parameter.addParam(id);

		billRepository.update(buff.toString(), parameter);
	}

}
