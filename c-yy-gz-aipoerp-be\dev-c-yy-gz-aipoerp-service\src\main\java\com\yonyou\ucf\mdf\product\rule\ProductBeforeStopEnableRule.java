package com.yonyou.ucf.mdf.product.rule;

import cn.hutool.core.collection.CollUtil;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.equip.service.AssetNameLibraryService;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/4 17:53
 * @DESCRIPTION 物料启用
 */
@Component("productBeforeStopEnableRule")
public class ProductBeforeStopEnableRule implements IYpdCommonRul {
    @Autowired
    private AssetNameLibraryService nameLibraryService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        try {
            List<BizObject> bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
            Object[] productIds = bills.stream().map(BizObject::getId).toArray();
            //根据物料编码查询是否存在在资产名称库中
            List<Map<String, Object>> assentNameList = nameLibraryService.getAssentNameListByProductId(productIds);
            if (CollUtil.isEmpty(assentNameList)){
                return null;
            }
            //获取资产名称库的ID去同步跟新启停状态
            List<String> assentNameIds = assentNameList.stream().map(m -> {
                Object bj = m.get("id");
                if (bj==null) return null;
                return bj.toString();
            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(assentNameIds)) {
                String action = rulCtxVO.getAction();
                if ("stop".equals(action)) {
                    nameLibraryService.stop(assentNameIds);
                }
                if ("unstop".equals(action)) {
                    nameLibraryService.enable(assentNameIds);
                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return null;
    }
}
