package com.yonyou.ucf.mdf.travelexpense.service;

import java.util.List;
import java.util.Map;

/**
 * 账单消费类型查询接口
 * 
 * <AUTHOR>
 *
 *         2025年3月26日
 */
public interface IConsumeKindQryService {

	/**
	 * 根据账单消费类型编码查询出消费类型ID
	 * 
	 * @param consumeKindCodes
	 * @return
	 */
	List<String> queryIdByCodes(List<String> consumeKindCodes);

	/**
	 * 获取所有消费类型id/编码映射
	 * @return
	 */
	Map<String, String> queryIdCodeMap();

}
