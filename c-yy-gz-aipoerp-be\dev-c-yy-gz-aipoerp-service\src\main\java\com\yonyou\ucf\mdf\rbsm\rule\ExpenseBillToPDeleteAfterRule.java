package com.yonyou.ucf.mdf.rbsm.rule;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.equip.utils.BipOpenApiRequest;
import com.yonyou.ucf.mdf.product.util.YpdRuleBillUtil;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ypd.bill.basic.bean.CharacteristicsEntity;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.basic.entity.convert.EntityConverter;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

@Component("expenseBillToPDeleteAfterRule")
@Slf4j
public class ExpenseBillToPDeleteAfterRule implements IYpdCommonRul {

    @Autowired
    private IBillRepository billRepository;

    @Autowired
    private IBillQueryRepository billQueryRepository;

    @Autowired
    private EntityConverter entityConverter;

    @Autowired
    private BipOpenApiRequest apiRequest;

    private String contractSaveUrl = "/yonbip/cpu/contract/saveStandardVersion";
    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        log.error("进入通用报销单删除后规则----lifeid-------------单据：{}");
        //获取删除后数据
        List<BizObject> bills = null;
        try {
            bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
            if (CollectionUtils.isEmpty(bills)) {
                return null;
            }
            log.error("进入通用报销单保存后规则----lifeid-------------单据：{}",JSONUtil.toJson(bills));
            for (BizObject b : bills) {
                //获取单据主键
                String id = b.getString("id");
                //采购合同主键
                BizObject expensebillDcs1 = b.getBizObject("expensebillDcs", BizObject.class);
                String cght = "";
                if (expensebillDcs1 != null) {
                    if(expensebillDcs1.getString("CGHT") != null){
                        cght = expensebillDcs1.getString("CGHT");
                    }

                }
                log.error("处理新采购合同回写----lifeid-------------单据：{}");
                //处理新采购合同回写
                if(StringUtils.isNotEmpty(cght)){
                    BigDecimal sumNsummny = getHistoryNsummny(id, cght);
                    updateCGHT(cght, sumNsummny);
                }
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 获取历史数据的价税总额
     * @param id
     * @param ocght
     * @return
     */
    private BigDecimal getHistoryNsummny(String id, String ocght) {
        List<Map<String, Object>> maps = queryCommonExpenseBillVOByCGHT(id, ocght);
        BigDecimal sumNsummny = new BigDecimal(0.0);
        if (CollectionUtils.isNotEmpty(maps)) {
            for (Map<String, Object> map : maps) {
                Object obj = map.get("nsummny");
                if(obj != null && obj instanceof BigDecimal){
                    sumNsummny = sumNsummny.add( (BigDecimal) obj );
                }else if(obj != null && obj instanceof BigInteger){
                    BigInteger bigInteger = (BigInteger) obj;
                    sumNsummny = sumNsummny.add( new BigDecimal(bigInteger) );
                }

            }
        }
        return sumNsummny;
    }

    /**
     * 根据采购合同主键获取报销单数据
     *
     * @param id
     * @param cght
     * @return
     */
    public List<Map<String, Object>> queryCommonExpenseBillVOByCGHT(String id, String cght) {
        QuerySchema querySchema = new QuerySchema();
        querySchema.addSelect("expensebillDcs.CGHT,sum(nsummny) as nsummny ");
        querySchema.addGroupBy("expensebillDcs.CGHT");
        querySchema.appendQueryCondition(
                QueryCondition.name("expensebillDcs.CGHT").eq(cght),
                QueryCondition.name("id").not_eq(id)
        );

        List<Map<String, Object>> maps = billQueryRepository.queryMapBySchema("znbzbx.commonexpensebill.CommonExpenseBillVO", querySchema);
        if (CollectionUtils.isNotEmpty(maps)) {
            return maps;
        }
        return null;
    }

    /**
     * 更新采购合同数据
     * @param cght
     * @param sumNsummn
     * @return
     */
    public boolean updateCGHT( String cght, BigDecimal sumNsummn) {
        //获取采购合同
        IBillDO billDO = billQueryRepository.findById("cpu-contract.contract.ContractVO", cght);
        if(billDO == null){
            throw new RuntimeException("获取采购合同失败！！！");
        }
        log.error("billDO----lifeid-------------单据：{}",JSONUtil.toJson(billDO));
        CharacteristicsEntity contractVODefineCharacter = (CharacteristicsEntity) billDO.getAttrValue("ContractVODefineCharacter");
        if(contractVODefineCharacter == null ){
            contractVODefineCharacter = new CharacteristicsEntity();
            contractVODefineCharacter.set_status(2);
		} else {
			Object tzInitbalance = contractVODefineCharacter.getAttribute("tz_initbalance");
			if (tzInitbalance != null) { // 期初已报销金额
				BigDecimal initbalance = new BigDecimal(tzInitbalance.toString());
				sumNsummn = sumNsummn.add(initbalance);
			}
        }
        contractVODefineCharacter.setAttribute("yykCS004", sumNsummn);
        contractVODefineCharacter.set_status(1);
        billDO.setAttrValue("ContractVODefineCharacter",contractVODefineCharacter);
        billDO.setAttrValue("_status",1);

        JSONObject param = JSONObject.parseObject(JSONUtil.toJson(billDO));
        JSONObject defineCharacter = param.getJSONObject("ContractVODefineCharacter");
        if (defineCharacter.getInteger("_status") != null) {
            if (defineCharacter.getInteger("_status") == 1) {
                defineCharacter.put("_status", "Update");
            } else if (defineCharacter.getInteger("_status") == 2) {
                defineCharacter.put("_status", "Insert");
            }
        }

        JSONArray contractMaterialList = param.getJSONArray("contractMaterialList");
        for (int i = 0; i < contractMaterialList.size(); i++) {
            JSONObject jsonObject = contractMaterialList.getJSONObject(i);
            jsonObject.put("_status", "Unchanged");
        }

        param.put("_status", "Update");
        param.put("supplierDocId", param.get("supplierId"));

        //遍历异常JSONArray
        JSONObject temp = JSON.parseObject(param.toJSONString());
        for (String key : temp.keySet()) {
            Object value = temp.get(key);
            if (value instanceof JSONArray) {
                if(!key.equals("contractMaterialList")){
                    param.remove(key);
                }
            }
        }
        log.error("contractSaveUrl----lifeid-------------单据：{}",JSONUtil.toJson(param));
        String result = apiRequest.doPost(contractSaveUrl, param);
        log.error("result----lifeid-------------单据：{}",result);
        if (result == null || result.equals("")) {
            throw new RuntimeException("回写采购合同出错！！！");
        }else{
            JSONObject jsonObject = JSON.parseObject(result);
            if(jsonObject.getInteger("code") != 200){
                throw new RuntimeException("回写采购合同失败！！！");
            }
        }
        return true;
    }
}
