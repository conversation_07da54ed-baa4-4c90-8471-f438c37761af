package com.yonyou.ucf.mdf.rbsm.model;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Expensebillb {
	private String id; // 否 报销单明细id(_status为Update更新时必填)
	private String pk_busimemo; // 否 费用项目id(id与编码必填一项) 示例：2184860029718784
	private String pk_busimemo_code; // 否 费用项目编码(id与编码必填一项)
	private String vnatcurrency = "CNY"; // 是 组织本币(支持id和code) 示例：2183687327011840
	private String vnatexchratetype = "01"; // 是 组织本币汇率类型(支持id和code) 示例：py7y8nze
	private String vcurrency = "CNY"; // 是 报销币种(支持id和code) 示例：2183687327011840
	private String nnatbaseexchrate = "1"; // 是 组织本币企业汇率 示例：1
	private String nnatexchrate = "1";// 是 组织本币汇率 示例：1
	private String dnatexchratedate; // 是 组织本币汇率日期(格式：yyyy-MM-dd) 示例：2021-09-26
	private String pk_handlepsn; // 是 报销人(支持id和code) 示例：****************
	private String vhandledeptid; // 是 报销人部门(支持id和code) 示例：****************
	private String chandleorg; // 是 报销人组织(支持id和code) 示例：****************
	private String caccountorg; // 是 会计主体(支持id和code) 示例：****************
	private String cfinaceorg; // 是 费用承担组织(支持id和code) 示例：****************
	private String vfinacedeptid; // 是 费用承担部门(支持id和code) 示例：****************
	private String pk_cusdoc; // 否 供应商id 示例：****************
	private String pk_cusdoc_code; // 否 供应商编码
	private String nexpensemny; // 是 不含税金额 示例：133.74
	private String nnatexpensemny; // 是 不含税总额-本币 示例：133.74
	private String nsummny; // 是 价税合计 示例：133.74
	private String nnatsummny; // 是 价税合计-本币 示例：133.74
	private String nshouldpaymny; // 是 应付额(应付额=价税合计-核销额) 示例：1
	private String nnatshouldpaymny; // 是 应付总额-本币(应付总额-本币=报销价税总额-本币 - 核销总额-本币) 示例：1
	private String npaymentmny; // 否 付款总额 示例：1
	private String nnatpaymentmny; // 否 付款总额-本币 示例：1
	private String _status = "Insert"; // 否 操作标识, Insert:新增、Update:更新 示例:Insert 示例：Insert

	private JSONObject expensebillBDcs; // 特征组自定义字段

	private String extend4; // 扩展字段-期间
	private String extend5; // 扩展字段-纳税期间
}
