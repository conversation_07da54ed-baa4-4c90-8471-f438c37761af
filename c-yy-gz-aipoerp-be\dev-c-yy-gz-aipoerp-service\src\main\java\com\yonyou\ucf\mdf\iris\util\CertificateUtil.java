package com.yonyou.ucf.mdf.iris.util;


import cfca.sadk.algorithm.common.Mechanism;
import cfca.sadk.lib.crypto.JCrypto;
import cfca.sadk.lib.crypto.Session;
import cfca.sadk.util.CertUtil;
import cfca.sadk.util.KeyUtil;
import cfca.sadk.util.Signature;
import cfca.sadk.x509.certificate.X509Cert;
import cfca.sadk.x509.certificate.X509CertVerifier;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/11 16:35
 * @DESCRIPTION 类描述
 */
@Slf4j
public class CertificateUtil {
    private static Session session = null;

    public CertificateUtil() throws Exception {
        // 软库初始化
        JCrypto.getInstance().initialize(JCrypto.JSOFT_LIB, null);
        session = JCrypto.getInstance().openSession(JCrypto.JSOFT_LIB);
    }

    @SneakyThrows
    public String sign(String data, String signPfxPath, String password) {
        String path = Objects.requireNonNull(getClass().getResource(signPfxPath)).getPath();
        PrivateKey privateKey = KeyUtil.getPrivateKeyFromPFX(path, password);
        X509Cert x509Cert = CertUtil.getCertFromPFX(path, password);
        Signature signature = new Signature();
        byte[] bytes = signature.p7SignMessageDetach(Mechanism.SHA256_RSA, data.getBytes(StandardCharsets.UTF_8), privateKey, x509Cert, session);
        return new String(bytes);
    }

    @SneakyThrows
    public boolean verify(String data, String sign, String verifyCerPath) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(sign)) {
            return false;
        }
        String path = Objects.requireNonNull(getClass().getResource(verifyCerPath)).getPath();
        Signature signature = new Signature();
        boolean detach = signature.p7VerifyMessageDetach(data.getBytes(StandardCharsets.UTF_8), sign.getBytes(), session);
        if (detach) {
            X509Cert userCert = signature.getSignerCert();
            log.debug("the cfca cert info is -------" + userCert.getSubject());
            //证书有效期
            if (!X509CertVerifier.verifyCertDate(userCert)) {
                throw new Exception("验签失败：证书已过期，请联系运营人员");
            }
            log.debug("the trues cert is ---------" + path);
            X509CertVerifier.updateTrustCertsMap(path);
            if (!X509CertVerifier.validateCertSign(userCert)) {
                throw new Exception("验签失败：该证书颁发者不合法，请核实！");
            }
            return true;
        }
        return false;
    }
}
