package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.rbsm.model.FieldRef;
import com.yonyou.ucf.mdf.rbsm.model.PeriodParam;
import com.yonyou.ucf.mdf.rbsm.model.PeriodVO;
import com.yonyou.ucf.mdf.rbsm.service.itf.IFieldRefService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPeriodService;
import com.yonyou.ucf.mdf.utils.AIPODaoHelper;
import com.yonyou.ypd.bill.basic.entity.IBillDO;

import lombok.SneakyThrows;

/**
 * <AUTHOR>
 *
 *         2025年3月20日
 */
@Service
public class PeriodServiceImpl implements IPeriodService {

	@Autowired
	private IFieldRefService fieldRefService;
	@Autowired
	private AIPODaoHelper dao;

	@SneakyThrows
	@Override
	public PeriodVO getPeriod(PeriodParam param) {

		FieldRef fieldRef = fieldRefService.getFieldRefByKkField("payPeriodRuleName");
		if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
			throw new RuntimeException(
					"没有配置默认发薪期间名称【payPeriodRuleName】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【payPeriodRuleName】！");
		}

		QuerySchema schema = QuerySchema.create().fullname("hrxc.period.PeriodRule");
		schema.addSelect("*");
		QueryConditionGroup cond = new QueryConditionGroup();
		cond.addCondition(new QueryCondition<String>("name", ConditionOperator.eq, fieldRef.getRefField()));
		schema.addCondition(cond);

		List<? extends IBillDO> result = dao.queryOuter(schema);
		if (CollectionUtils.isEmpty(result)) {
			throw new RuntimeException(String.format(
					"根据配置默认发薪期间名称【payPeriodRuleName】值【%s】，没有查询到发薪期间，请检查【数字化建模>字段映射>客开系统配置>字段映射】配置【payPeriodRuleName】值是否正确！",
					fieldRef.getRefField()));
		}
		IBillDO bill = result.get(0);
		Object generateRule = bill.getAttrValue("generateRule");
		if (generateRule == null) {
			throw new RuntimeException(
					String.format("发薪期间没有配置默认纳税期间规则，请检查【薪酬管理>薪酬计算>基础设置>薪资期间】对应的【%s】设置是否正确", fieldRef.getRefField()));
		}
		String rule = generateRule.toString();

		String payPeriod = "";
		String taxPeriod = "";
		if ("1".equals(param.getType())) { // 发薪期间
			payPeriod = param.getPeriodDate();
			taxPeriod = getTaxPeriodByPayPeriod(payPeriod, rule);
		} else {
			taxPeriod = param.getPeriodDate();
			payPeriod = getPayPeriodByTaxPeriod(taxPeriod, rule);
		}

		PeriodVO vo = new PeriodVO();
		vo.setPayPeriod(payPeriod);
		vo.setTaxPeriod(taxPeriod);

		return vo;
	}

	/**
	 * 根据纳税期间和纳税期间规则获取发薪期间
	 * 
	 * @param taxPeriod
	 * @param rule
	 * @return
	 */
	private String getPayPeriodByTaxPeriod(String taxPeriod, String rule) {
		LocalDate date = LocalDate.parse(taxPeriod);
		switch (rule) {
		case "0": // 薪资期间前一个月
			date = date.plusMonths(1);
			break;
		case "1": // 薪资期间所在月
			break;
		case "2": // 薪资期间后一个月
			date = date.minusMonths(1);
			break;
		case "3": // 薪资期间后两个月
			date = date.minusMonths(2);
			break;

		default:
			break;
		}
		return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
	}

	/**
	 * 根据发薪期间和纳税期间规则获取纳税期间
	 * 
	 * @param payPeriod
	 * @param rule
	 * @return
	 */
	private String getTaxPeriodByPayPeriod(String payPeriod, String rule) {
		LocalDate date = LocalDate.parse(payPeriod);
		switch (rule) {
		case "0": // 薪资期间前一个月
			date = date.minusMonths(1);
			break;
		case "1": // 薪资期间所在月
			break;
		case "2": // 薪资期间后一个月
			date = date.plusMonths(1);
			break;
		case "3": // 薪资期间后两个月
			date = date.plusMonths(2);
			break;

		default:
			break;
		}
		return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
	}

}
